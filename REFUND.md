### 退款逻辑业务梳理

#### 退款流程
<div align="center">
<img src="./.sequence/puml/refund.puml" height="50" width="50" align="left">
<br><p align="right">退款</p>
</div> 


##### 代码核心逻辑

- com.didapinche.trade.domin.refund.service.refundaccount : 账户

         举例说明:
                 BonusAccount ：顺风金账户，
                        accountRemaining() : 查询当前订单（传入的CalculateOrderDO对象）当前顺风金账户存量
                        addAmount（）： 当前订单（传入的CalculateOrderDO对象）顺风金账户增加相应金额
                BonusRecoverAccount ：顺风金账户，
                                accountRemaining() : 查询当前订单（传入的CalculateOrderDO对象）当前顺风金账户存量
                                addAmount（）： 当前订单（传入的CalculateOrderDO对象）顺风金回收账户增加相应金额
- com.didapinche.trade.domin.refund.service.refundfactory : 不同的退款逻辑处理
            
         举例说明:
                 AllRefund ： 全部金额累计  (重复支付场景)
                 AccountOrderDeductRefund : 根据传入的金额按照订单、账户顺序进行抵扣 （cms退款）
                 OrderAccountDeductRefund : 根据传入的金额按照账户、订单顺序进行抵扣  （补偿金退款）
                 AccountOrderRemainRefund : 根据传入的金额按照账户、订单顺序进行抵扣,将抵扣完剩余金额返回  （暂无）
                 OrderAccountRemainRefund : 根据传入的金额按照订单、账户顺序进行抵扣,将抵扣完剩余金额返回  （顺风车取消订单，AccountOrderRemainRefund个人认为退款逻辑体现更好，历史逻辑延续）
                 DeductionRefund ： 按照不同订单、账户顺序进行抵扣，区别：会对传入的Map<Long, CalculateOrderDO>进行更新 （退差价）
       
- com.didapinche.trade.domin.refund.service.refundconfiguration : 不同的业务退款配置
          
          举例说明:      
                CarpoolCancelConfiguration(@Component("carpool_cancel")): 取消订单
                        逻辑处理：ORDER_ACCOUNT_REMAIN：根据传入的金额按照订单、账户顺序进行抵扣,将抵扣完剩余金额返回的
                        账户逻辑：CASH_ACCOUNT_LIST_INVERTED ：出租车挂账企业，出租车充值企业，顺风车挂账企业，不可体现，可提现
                                     CASH_ACCOUNT_LIST_INVERTED.add(new TEnterpriseVipAccount());
                                     CASH_ACCOUNT_LIST_INVERTED.add(new TEnterpriseAccount());
                                     CASH_ACCOUNT_LIST_INVERTED.add(new CEnterpriseVipAccount());
                                     CASH_ACCOUNT_LIST_INVERTED.add(new NoCashAccount());
                                     CASH_ACCOUNT_LIST_INVERTED.add(new CashAccount());
                                     CASH_ACCOUNT_LIST_INVERTED.add(new ThirdPayAccount());
                                     
          guaranteed方法说明：不可扣除金额计算
                                     
- 场景举例:

        顺风车支付 订单 A 支付组成 余额 1 不可提现余额 2   优惠券 3 顺风金 4 三方 0.02  挂账企业  1 （券包金额 0.02已退款） 
                 补单 B  支付组成 余额 1 不可提现余额 2   优惠券 3 顺风金 4 三方 0.02  挂账企业  1 
                         
        第1次调用 orderList（A.id b.id） compensation 8.02
                退款结果：coupon 8  bonus 6 
        第2次调用 orderList（A.id b.id） compensation 7.02
                退款结果：passenger 1
        第3次调用 orderList（A.id b.id） compensation 5.01
                退款结果：cEnterpriseVip 0.01  noPassenger 2
        第4次调用 orderList（A.id b.id） compensation 3
                退款结果：cEnterpriseVip 0.99 passenger 1
        第4次调用 orderList（A.id b.id） compensation 100
                退款结果： 退款金额错误                            
                                     
       
       

##### 现有退款业务说明

名词解释:
- 第三方:微信支付宝等
- 现金：第三方 + 用户余额


| 业务类型     | 业务说明                | 场景逻辑说明               | 
|-------------------|------------------------------|-------------------------|
|  carpool_arrears_recharge    |  欠款充值退款         |  只退第三方支付金额         |
|  carpool_additional    |    行程追加费用退款       |     只退第三方支付金额      |
|   carpool_cancel   |    取消订单退款       |     订单、账户顺序依次扣除除补偿金以外的金额（扣除随单券包）      |
|   carpool_compensation   |  补偿金退款         |    订单、账户顺序退款       |
|   carpool_highway   |   高速费退款        |    只退第三方支付金额（额外处理扣除车主到账金额）       |
|    carpool_highway_repeatpay  |  高速费重复支付退款         |    只退第三方支付金额       |
|   carpool_multi   |   顺风车合拼返款        |   分别计算现金，优惠券顺风金，按订单顺序退款，其中现金退回，优惠券和顺风金回收不退回原账户        |
|   carpool_multi_Illegal、user_carpool_offline_false   |   顺风车违规多频        |   账户、订单顺序退款       |
|   carpool_repeatpay   |   顺风车重复支付        |     原路退（扣去券包金额）      |
|   carpool_offline_true   |  顺风车线下全款退         |   账户、订单顺序退款 （关注是否到账扣除车主到账）       |
|   carpool_offline_false   |    顺风车线下部分退       |    账户、订单顺序退款，其中现金退回，优惠券和顺风金回收不退回原账户  （关注是否到账扣除车主到账）     |
|   taxi_offline_true   |      出租车线下全款退     |     账户、订单顺序退款  （关注是否到账扣除车主到账）     |
|   taxi_offline_false   |     顺风车线下部分退      |  账户、订单顺序退款，其中现金退回，优惠券和顺风金回收不退回原账户   （关注是否到账扣除车主到账）      |
|    mall  |   商城退款        |    只退第三方支付金额       |
|    carpool_deduction  |  顺风车退差价         |   分别计算现金，优惠券顺风金，按订单顺序将支付以外的金额退回        |
|   taxi_repeatpay   |   出租车重复支付        |    原路退（扣去券包金额）       |







