package com.didapinche.trade.application.service.impl.confiscate;

import cn.hutool.core.util.NumberUtil;
import com.didapinche.agaue.common.exception.DidaCode;
import com.didapinche.agaue.common.exception.SuccessCode;
import com.didapinche.agaue.common.result.Result;
import com.didapinche.agaue.common.result.ResultBuilder;
import com.didapinche.agaue.tools.convert.AmountConvertUtil;
import com.didapinche.payment.frigate.Frigate;
import com.didapinche.trade.application.BO.ConfiscateBO;
import com.didapinche.trade.application.service.ITradeService;
import com.didapinche.trade.application.service.impl.bill.BillMangerSupport;
import com.didapinche.trade.domin.DO.TradeOrderDO;
import com.didapinche.trade.domin.DO.common.CommonResultDO;
import com.didapinche.trade.domin.bill.action.BillTradeActionService;
import com.didapinche.trade.domin.bill.action.confiscate.ConfiscateActionVisitor;
import com.didapinche.trade.domin.bill.action.confiscate.bo.ConfiscateElementBO;
import com.didapinche.trade.domin.bill.action.mapping.BillOrderMappingVisitor;
import com.didapinche.trade.domin.confiscate.DO.ConfiscateDO;
import com.didapinche.trade.domin.confiscate.request.ConfiscateDomainRequest;
import com.didapinche.trade.domin.confiscate.service.ConfiscateDomainService;
import com.didapinche.trade.domin.order.service.BizLineMapper;
import com.didapinche.trade.infrastructure.entities.TradeOrderNote;
import com.didapinche.trade.infrastructure.exception.TradeErrorCode;
import com.didapinche.trade.infrastructure.service.impl.TradeOrderModelService;
import com.didapinche.trade.infrastructure.tbl.TradeBillModel;
import com.didapinche.trade.infrastructure.tbl.TradeOrderModel;
import com.didapinche.trade.infrastructure.util.BigDecimalUtil;
import com.didapinche.trade.thrift.entities.CommonConfiscateRequest;
import com.didapinche.trade.thrift.entities.ConfiscateResult;
import com.didapinche.trade.thrift.enums.TBusinessEnum;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

import static com.didapinche.trade.infrastructure.constants.TradeConstants.ENTERPRISE_ACCOUNT_TYPE;
import static com.didapinche.trade.infrastructure.util.SystemUtils.validNum;
import static com.didapinche.trade.infrastructure.util.SystemUtils.validNums;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CommonConfiscateServiceImpl implements ITradeService<CommonConfiscateRequest> {

    @Autowired
    private ConfiscateDomainService confiscateDomainService;

    @Autowired
    BillMangerSupport billMangerSupport;

    @Resource
    private ConfiscateActionVisitor confiscateActionVisitor;
    @Resource
    private BillOrderMappingVisitor billOrderMappingVisitor;

    @Autowired
    private TradeOrderModelService tradeOrderModelService;

    @Autowired
    BillTradeActionService billTradeActionService;

    @Override
    public DidaCode verifyParameter(CommonConfiscateRequest parameter) {
        if (TBusinessEnum.c_highway_advance.name().equals(parameter.getBusinessType()) && parameter.getPayTradeId() <= 0) {
            return TradeErrorCode.PARAM_ERROR;
        }
        return SuccessCode.SUCCESS;
    }

    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Frigate(name = "没收")
    public Result<ConfiscateResult> doTrade(CommonConfiscateRequest request)  {
        TradeOrderDO tradeOrderDO = new TradeOrderDO();
        tradeOrderDO.setMajorProductId(request.getProductId());
        tradeOrderDO.setBusinessType(request.getBusinessType());
        tradeOrderDO.setSource(request.getSource());
        tradeOrderDO.setOrderDesc(request.getDesc());
        if (request.getPayTradeId() > 0){
            tradeOrderDO.setPayTradeNo(request.getPayTradeId());
        }
        if (ENTERPRISE_ACCOUNT_TYPE.contains(request.getAccountType())){
            tradeOrderDO.setCompanyId(Integer.parseInt(request.getConfiscateUserId() + ""));
        }else {
            tradeOrderDO.setUserId(request.getConfiscateUserId());
        }
        if (validNums(request.getPayTradeId(),request.getPayuserId()) ) {
            TradeOrderModel tradeOrderModel = tradeOrderModelService.selectById(request.getPayTradeId(), request.getPayuserId());
            if (tradeOrderModel != null && validNum(tradeOrderModel.getBillId())) {
                log.debug("根据payTradeId:{},payuserId:{} 查询BillId:{}", request.getPayTradeId(), request.getPayuserId(),tradeOrderModel.getBillId());
                tradeOrderDO.setBillId(tradeOrderModel.getBillId());
            }
        } else if (validNum(request.getProductId())) {//根据产品ID查询
            String bizLineEnum = BizLineMapper.getBizLineEnum(request.getBusinessType());
            TradeBillModel tradeBillModel = billTradeActionService.billOrderByProductId(request.getProductId(), bizLineEnum);
            if(tradeBillModel!=null){
                tradeOrderDO.setBillId(tradeBillModel.getId());
                updateTaxiDriverId(request, tradeBillModel.getPayerId());
            }else {
                log.debug("查询不到产品【{}】对应的账单", request.getProductId());
            }
        }


        ConfiscateDO confiscateDO = new ConfiscateDO();
        confiscateDO.setAccountType(request.getAccountType());
        confiscateDO.setAmount(AmountConvertUtil.yuan2BigDecimal(request.getAmount()));
        confiscateDO.setUserId(tradeOrderDO.getUserId());
        confiscateDO.setCompanyId(tradeOrderDO.getCompanyId());
        confiscateDO.setBonus(AmountConvertUtil.yuan2BigDecimal(request.getBonus()));
        confiscateDO.setSysopId(request.getSysOpId());
        confiscateDO.setWithdrawId(request.getWithdrawId());
        confiscateDO.setDetails(request.getDetails());
        confiscateDO.setRemarks(request.getRemarks());
        confiscateDO.setBusinessType(request.getBusinessType());
        confiscateDO.setPayTradeNo(tradeOrderDO.getPayTradeNo());
        confiscateDO.setComment(request.getComment());


        Date tradeDate = new Date();
        ConfiscateDomainRequest confiscateDomainRequest = new ConfiscateDomainRequest();
        confiscateDomainRequest.setPayUserId(request.getPayuserId());
        confiscateDomainRequest.setTradeOrderDO(tradeOrderDO);
        confiscateDomainRequest.setConfiscateDO(confiscateDO);
        confiscateDomainRequest.setTradeDate(tradeDate);
        CommonResultDO commonResultDO = confiscateDomainService.confiscate(confiscateDomainRequest);

        ConfiscateResult confiscateResult = new ConfiscateResult();
        confiscateResult.setTradeOrderId(commonResultDO.getTradeOrderId());
        if (commonResultDO.getDidaCode() != null){
            return ResultBuilder.buildResult(commonResultDO.getDidaCode().getCode(),commonResultDO.getDidaCode().getMsg(),confiscateResult);
        }
        //更新bill
        updateBill(request, tradeDate, commonResultDO.getTradeOrderId());
        return ResultBuilder.buildSuccessResult(confiscateResult);
    }

    private void updateTaxiDriverId(CommonConfiscateRequest request, Long payUserId) {
        if (!TBusinessEnum.taxi_offline_commission.name().equals(request.getBusinessType())) {
            return;
        }
        if (!validNum(request.getPayTradeId())) {
            return;
        }
        TradeOrderModel tradeOrderModel = tradeOrderModelService.selectById(request.getPayTradeId(), payUserId);
        if (tradeOrderModel == null) {
            return;
        }
        TradeOrderNote note = TradeOrderNote.create(tradeOrderModel.getNote());
        if (note.getDriverId() != null && note.getDriverId() > 0) {
            return;
        }
        //设置司机id
        note.setDriverId(request.getConfiscateUserId());
        TradeOrderModel updateTradeOrderModel = new TradeOrderModel();
        updateTradeOrderModel.setId(tradeOrderModel.getId());
        updateTradeOrderModel.setUserId(tradeOrderModel.getUserId());
        updateTradeOrderModel.setNote(note.toJson());
        updateTradeOrderModel.setUpdateTime(new Date());
        tradeOrderModelService.updateById(updateTradeOrderModel);
    }

    private void updateBill(CommonConfiscateRequest request, Date tradeDate, Long confiscateOrderId) {
        ConfiscateElementBO confiscateElementBO = billMangerSupport.buildConfiscateBO(generateConfiscateBO(request));
        confiscateElementBO.setTradeDate(tradeDate);
        //没收单号
        confiscateElementBO.setConfiscateOrderId(confiscateOrderId);
        //更新罚没金额
        confiscateElementBO.accept(confiscateActionVisitor);
        confiscateElementBO.accept(billOrderMappingVisitor);
    }

    private ConfiscateBO generateConfiscateBO(CommonConfiscateRequest request) {
        ConfiscateBO confiscateBO= new ConfiscateBO();
        confiscateBO.setBusinessType(request.getBusinessType());
        confiscateBO.setUserId(request.getPayuserId());
        confiscateBO.setConfiscateUserId(request.getConfiscateUserId());
        confiscateBO.setTradeOrderId(request.getPayTradeId());
        confiscateBO.setMajorProductId(request.getProductId());
        confiscateBO.setConfiscateAmount(BigDecimalUtil.add(request.getAmount(), request.getBonus()));
        return confiscateBO;
    }
}
