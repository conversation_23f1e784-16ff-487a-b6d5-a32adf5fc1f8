package com.didapinche.trade.application.service.impl.query;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.didapinche.agaue.common.exception.DException;
import com.didapinche.agaue.common.exception.DidaCode;
import com.didapinche.agaue.common.exception.SuccessCode;
import com.didapinche.agaue.datasource.common.Routing;
import com.didapinche.agaue.datasource.enums.DatabaseEnum;
import com.didapinche.agaue.datasource.enums.DatabaseTypeEnum;
import com.didapinche.agaue.datasource.toolkit.DynamicDataSourceContextHolder;
import com.didapinche.agaue.tools.convert.AmountConvertUtil;
import com.didapinche.finance.mq.context.enums.TradeTypeEnum;
import com.didapinche.generator.service.impl.bean.IdType;
import com.didapinche.generator.service.impl.converter.IdConverterFactory;
import com.didapinche.generator.service.impl.converter.IdConverterImpl;
import com.didapinche.pay.service.thrift.TQueryPaymentResponse;
import com.didapinche.server.commons.common.aop.thrift.ResultMap;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.server.commons.common.spring.SpringUtils;
import com.didapinche.server.commons.common.util.DateUtil;
import com.didapinche.server.commons.common.util.LoadPropertyUtil;
import com.didapinche.server.commons.common.util.RedisClusterUtil;
import com.didapinche.server.commons.common.util.UserInfoUtil;
import com.didapinche.thrift.activity.platform.OrderInfoRequest;
import com.didapinche.thrift.activity.platform.OrderInfoResult;
import com.didapinche.thrift.common.util.JsonUtil;
import com.didapinche.trade.application.bean.UserOrder;
import com.didapinche.trade.application.service.impl.payment.scanpay.ErrorCode;
import com.didapinche.trade.domin.bean_new.TradeAmountDetailsMultiple;
import com.didapinche.trade.domin.bill.action.BillTradeActionService;
import com.didapinche.trade.domin.order.service.BizLineEnum;
import com.didapinche.trade.domin.payment.pau.PaymentPayAfterUseDomainService;
import com.didapinche.trade.infrastructure.common.TradeState;
import com.didapinche.trade.infrastructure.entities.TradeOrderNote;
import com.didapinche.trade.infrastructure.enums.PaymentPauCutFlageEnum;
import com.didapinche.trade.infrastructure.enums.ThirdChannelNameEnum;
import com.didapinche.trade.infrastructure.enums.order.TradeOrderStatusEnum;
import com.didapinche.trade.infrastructure.exception.TradeErrorCode;
import com.didapinche.trade.infrastructure.service.impl.TradeOrderModelService;
import com.didapinche.trade.infrastructure.tbl.TradeAmountDetailsModel;
import com.didapinche.trade.infrastructure.tbl.TradeBillModel;
import com.didapinche.trade.infrastructure.tbl.TradeCouponDetailsModel;
import com.didapinche.trade.infrastructure.tbl.TradeOrderModel;
import com.didapinche.trade.infrastructure.tbl.TradeProductDetailsModel;
import com.didapinche.trade.infrastructure.thrift.*;
import com.didapinche.trade.infrastructure.util.BigDecimalUtil;
import com.didapinche.trade.infrastructure.util.TradeDateUtil;
import com.didapinche.trade.infrastructure.vo.OrderLookupKey;
import com.didapinche.trade.thrift.entities.*;
import com.didapinche.trade.thrift.enums.TBusinessEnum;
import com.didapinche.trade.thrift.enums.TChannelEnum;
import com.didapinche.trade.thrift.enums.TPaymentWayEnum;
import com.didapinche.trade.thrift.enums.TTradeProductTypeEnum;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.velo.carpoolv3.entity.tbl.UserUser;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.didapinche.server.commons.common.util.BigDecimalUtil.toStringLeft2;
import static com.didapinche.trade.domin.bill.bizmap.PayTypePart.containsAnyOfTaxiqr;
import static com.didapinche.trade.infrastructure.constants.TradeConstants.COUPON_MALL_PRODUCT_TYPE;

/**
 * <AUTHOR>
 * @Date 2022/9/15 20:03
 * @Version 1.0
 *
 */
@Slf4j
@Service
public class TradeQueryImpl extends AbstractQueryService<QueryTradeRequest> {

    private static final LoadingCache<Integer, String> couponGuidePauResultCache = CacheBuilder.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build(new CacheLoader<Integer, String>() {
                @Override
                public String load(@NotNull Integer key) throws Exception {
                    return getCouponGuidePauResult(key);
                }
            });

    private static final String USER_SUM_PAY_MONTH = "[rc9](b)user_sum_pay_month:";

    @Resource
    protected PaymentPayAfterUseDomainService paymentPayAfterUseDomainService;

    @Autowired
    private UserInfoThriftClient userInfoThriftClient;

    @Autowired
    protected BillTradeActionService billTradeActionDate;

    private static final IdConverterImpl idConverterImpl=new IdConverterImpl(IdConverterFactory.getIdMeta(IdType.CITY_MAP));

    @Autowired
    private CarpoolReadThriftSupportService carpoolReadThriftSupportService;

    @Autowired
    private ActivityGoodsOrderThriftSupportService activityGoodsOrderThriftSupportService;

    @Resource
    private PayOrderReadThriftSupportService payOrderReadThriftSupportService;

    @ApolloJsonValue("${actual_pay_type_list:[driver,passenger,third,c_enterprise_vip,taxi,passenger_not_withdraw,t_enterprise_vip,t_enterprise]}")
    List<String> actualPayTypeList;

    @Override
    public DidaCode verifyParameter(QueryTradeRequest parameter) {
        if (parameter.getUserId() <= 0
                || CollectionUtils.isEmpty(parameter.getOrderIdList())) {
            return TradeErrorCode.PARAM_ERROR;
        }
        return SuccessCode.SUCCESS;
    }

    @Override
    public List<QueryTradeResult> doTrade(QueryTradeRequest request) {
        List<QueryTradeResult> results = new ArrayList<>();
        List<TradeOrderModel> tradeOrderModelList = tradeOrderModelService.selectByIds(request.getOrderIdList(), request.getUserId());
        for (TradeOrderModel tradeOrderModel : tradeOrderModelList) {
            QueryTradeResult paymentTrade = new QueryTradeResult();
            paymentTrade.setId(tradeOrderModel.getId());
            paymentTrade.setMajorProductId(tradeOrderModel.getMajorProductId());
            paymentTrade.setBusinessType(tradeOrderModel.getBusinessType());
            paymentTrade.setThirdTradeNo(tradeOrderModel.getThirdTradeNo());
            paymentTrade.setSource(tradeOrderModel.getSource());
            paymentTrade.setStatus(tradeOrderModel.getStatus());
            if (tradeOrderModel.getSuccessTime() != null) {
                paymentTrade.setTradeTime(DateUtil.format(tradeOrderModel.getSuccessTime(), "yyyyMMddHHmmss"));
            }
            paymentTrade.setCreateTime(DateUtil.format(tradeOrderModel.getCreateTime(), "yyyyMMddHHmmss"));

            //note备注信息
            String note = tradeOrderModel.getNote();
            if (StringUtils.isNotBlank(note)) {
                TradeOrderNote tradeOrderNote = JsonMapper.json2Bean(note, TradeOrderNote.class);
                paymentTrade.setMerchantId(tradeOrderNote.getMerchantId());
                //是否是先乘后付单
                boolean pauFlag = paymentPayAfterUseDomainService.isPauFlag(tradeOrderNote.getPaymentWay());
                boolean enterpriseVipPauFlag = paymentPayAfterUseDomainService.isEnterpriseVipPauFlag(tradeOrderNote.getPaymentWay());
                paymentTrade.setIsEnterpriseVipPauOrder(enterpriseVipPauFlag);
                //是否是扣款成功
                String pauCutFlag = tradeOrderNote.getPauCutFlag();
                PaymentPauCutFlageEnum cutFlag = PaymentPauCutFlageEnum.findByFlag(pauCutFlag);
                boolean isCutSuccess = PaymentPauCutFlageEnum.success == cutFlag;
                //扣款成功时间 yyyyMMddHHmmss
                String pauCutTime = tradeOrderNote.getPauCutTime();
                //先乘后付扣款状态
                String pauCutStatus = cutFlag != null ? cutFlag.getStatus() : PaymentPauCutFlageEnum.create.getStatus();

                //是否是先乘后付单
                paymentTrade.setIsPauOrder(pauFlag);
                //是否是扣款成功
                paymentTrade.setIsPauCutSuccess(isCutSuccess);
                //扣款成功时间 yyyyMMddHHmmss
                paymentTrade.setPauCutTime(pauCutTime);
                //先乘后付扣款状态
                paymentTrade.setPauCutStatus(pauCutStatus);
                paymentTrade.setHoldStatus(tradeOrderNote.getHold());
                paymentTrade.setSysOrderGroup(tradeOrderNote.getSysOrderGroup());

                TradeOrderNote.EnterpriseDetails enterpriseDetails = tradeOrderNote.getEnterpriseDetails();
                if (enterpriseDetails != null) {
                    TEnterpriseDetails result = new TEnterpriseDetails();
                    Integer startCityId = enterpriseDetails.getStartCityId();
                    result.setStartCityId(startCityId == null ? 0 : startCityId);
                    result.setOrderServiceFee(enterpriseDetails.getOrderServiceFee());
                    result.setPartnerOrderId(enterpriseDetails.getPartnerOrderId());
                    result.setCompanyId(tradeOrderModel.getCompanyId() == null ? 0 : tradeOrderModel.getCompanyId());
                    setEnterpriseVipDiscount(tradeOrderNote, result);
                    paymentTrade.setEnterpriseDetails(result);
                }
            }

            // 商品信息
            BigDecimal mallAmount = BigDecimal.ZERO;
            List<QueryProductResult> productDetails = new ArrayList<>();
            List<TradeProductDetailsModel> tradeProductDetailsModels = tradeProductDetailsModelService.selectByOrderId(tradeOrderModel.getId(), tradeOrderModel.getUserId());
            for (TradeProductDetailsModel tradeProductDetailsModel : tradeProductDetailsModels) {
                QueryProductResult queryProductResult = new QueryProductResult();
                queryProductResult.setPrice(tradeProductDetailsModel.getPrice().toString());
                queryProductResult.setProductId(tradeProductDetailsModel.getProductId());
                queryProductResult.setType(tradeProductDetailsModel.getType());
                if (COUPON_MALL_PRODUCT_TYPE.contains(tradeProductDetailsModel.getType())) {
                    mallAmount = mallAmount.add(tradeProductDetailsModel.getPrice());
                }
                if (request.isDetailsProductShow()) {
                    if (!request.isMallContains() && COUPON_MALL_PRODUCT_TYPE.contains(tradeProductDetailsModel.getType())) {
                        continue;
                    }
                    productDetails.add(queryProductResult);
                }

            }
            paymentTrade.setProductDetails(productDetails);
            // 支付明细
            if (request.isDetailsAmountShow()) {
                QueryAmountResult queryAmountResult = queryAmountResult(tradeOrderModel, request.isMallContains(), mallAmount);
                paymentTrade.setAmountDetails(queryAmountResult);
            }
            // 金额
            BigDecimal amount = request.isMallContains() ? tradeOrderModel.getTotalPrice() : tradeOrderModel.getTotalPrice().subtract(mallAmount);
            paymentTrade.setAmount(amount.toString());
            //优惠券明细
            if (request.isDetailsCouponShow()) {
                TradeCouponDetailsModel tradeCouponDetailsModel = tradeCouponDetailsModelService.selectCouponDetailsByOrderId(tradeOrderModel.getId(), tradeOrderModel.getUserId());
                if (tradeCouponDetailsModel != null) {
                    TCouponDetails couponDetails = new TCouponDetails();
                    couponDetails.setCouponId(tradeCouponDetailsModel.getCouponId());
                    couponDetails.setCouponType(tradeCouponDetailsModel.getCouponType());
                    couponDetails.setCouponSetId(tradeCouponDetailsModel.getCouponSetId());
                    paymentTrade.setCouponDetails(couponDetails);
                }
            }
            results.add(paymentTrade);
        }
        return results;
    }

    private static void setEnterpriseVipDiscount(TradeOrderNote tradeOrderNote, TEnterpriseDetails result) {
        if (tradeOrderNote.getEnterpriseVipExtra() != null){
            Map<String, Object> enterpriseVipExtra = tradeOrderNote.getEnterpriseVipExtra();
            Integer tencentDiscountAmount = (Integer) enterpriseVipExtra.getOrDefault("tencentDiscountAmount",0);
            result.setDiscountAmount(AmountConvertUtil.fen2yuan2(tencentDiscountAmount));
        }
    }


    public QueryActualOrderCutResponse queryActualOrderCut(QueryActualOrderCutRequest request) {
        QueryActualOrderCutResponse queryActualOrderCutResponse = new QueryActualOrderCutResponse();
        queryActualOrderCutResponse.setCode(0);
        TradeOrderModel tradeOrderModel = tradeOrderModelService.selectById(request.getOrderId(), request.getUserId());
        if (tradeOrderModel == null || !tradeOrderModel.getTradeType().equals("payment")) {
            queryActualOrderCutResponse.setOrderCutScene("old");
            return queryActualOrderCutResponse;
        }
        if (!TradeOrderStatusEnum.success.name().equals(tradeOrderModel.getStatus())) {
            queryActualOrderCutResponse.setOrderCutScene("unpaid");
            return queryActualOrderCutResponse;
        }
        String note = tradeOrderModel.getNote();
        Map<String, Object> noteMap = JsonMapper.json2Map(note);
        if (noteMap != null && noteMap.get("pauCutFlag") != null && !noteMap.get("pauCutFlag").toString().equals("success")) {
            queryActualOrderCutResponse.setOrderCutScene("unpaid");
        } else {
            queryActualOrderCutResponse.setOrderCutScene("paid");
        }
        return queryActualOrderCutResponse;
    }

    @Override
    public TradeQueryCommonResponse checkIsQuietGuide(int userId) {
        TradeQueryCommonResponse tradeQueryCommonResponse = new TradeQueryCommonResponse();
        tradeQueryCommonResponse.setCode(0);
        try {
            if (LoadPropertyUtil.getProperty("coupon_guidepau_cache_stop_test", Boolean.class, false)) {
                return tradeQueryCommonResponse.setRet(getCouponGuidePauResult(userId));
            } else {
                return tradeQueryCommonResponse.setRet(couponGuidePauResultCache.get(userId));
            }
        } catch (Exception e) {
            log.error("checkIsQuietGuide查询中异常，userId:{}", userId, e);
            return tradeQueryCommonResponse.setRet("true");
        }
    }

    private static String getCouponGuidePauResult(int userId) {
        Routing routing = new Routing().setLookupId(SpringUtils.getBean(RoutingThriftSupportService.class).getLookupKeyById("mall"));
        DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.SLAVE, DatabaseEnum.order, routing);
        Calendar silenceCal = Calendar.getInstance();
        //默认7天
        silenceCal.add(Calendar.SECOND, -LoadPropertyUtil.getProperty("coupon_guidepau_silence", Integer.class, 604800));
        List<TradeOrderModel> tradeOrderModelList = SpringUtils.getBean(TradeOrderModelService.class).queryGuideOpenCouponPauOrdersByUserId((long)userId, TBusinessEnum.mall.name(), ThirdChannelNameEnum.alipay.name(), silenceCal.getTime());
        int count = 0;
        if (tradeOrderModelList != null && !tradeOrderModelList.isEmpty()) {
            for(TradeOrderModel tradeOrderModel : tradeOrderModelList) {
                if (StringUtils.isNotBlank(tradeOrderModel.getNote()) && tradeOrderModel.getNote().contains(TPaymentWayEnum.zhima_pau_sign.name())) {
                    count ++;
                }
            }
        }
        return count >= LoadPropertyUtil.getProperty("coupon_guidepau_silence_count", Integer.class, 2) ? "true" : "false";
    }

    @Override
    public PauOrderProductInfo queryProductInfoForPauOrder(String payOrderNo) {
        PauOrderProductInfo pauOrderProductInfo = new PauOrderProductInfo();
        TQueryPaymentResponse tQueryPaymentResponse = paymentPayAfterUseDomainService.queryPaymentOrderByOrderNo(payOrderNo);
        if (tQueryPaymentResponse != null) {
            if (!StringUtils.isNumeric(tQueryPaymentResponse.getPayTradeNo()) || !NumberUtil.isLong(tQueryPaymentResponse.getPayTradeNo())) {
                log.warn("queryProductInfoForPauOrder 老订单不处理. payOrderNo:{},payTradeNo:{}", payOrderNo, tQueryPaymentResponse.getPayTradeNo());
                return pauOrderProductInfo.setCode(TradeErrorCode.NOT_ACTION_CALL_CMS_ERROR.getCode()).setMessage(TradeErrorCode.NOT_ACTION_CALL_CMS_ERROR.getMsg());
            }
            Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(tQueryPaymentResponse.getPayTradeNo()));
            DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.SLAVE, DatabaseEnum.order, routing);
            TradeOrderModel tradeOrderModel = tradeOrderModelService.selectById(Long.parseLong(tQueryPaymentResponse.getPayTradeNo()), tQueryPaymentResponse.getUserId());
            if (tradeOrderModel != null) {
                pauOrderProductInfo.setCode(0);
                pauOrderProductInfo.setMajorProductId(tradeOrderModel.getMajorProductId());
                TradeOrderNote tradeOrderNote = JsonMapper.json2Bean(tradeOrderModel.getNote(), TradeOrderNote.class);
                if (tradeOrderNote != null && StringUtils.isNotBlank(tradeOrderNote.getPauCutAmount())) {
                    pauOrderProductInfo.setNeedPayMoney(tradeOrderNote.getPauCutAmount());
                    return pauOrderProductInfo;
                }
            }
        }
        return pauOrderProductInfo.setCode(TradeErrorCode.NOT_FUND_PAY_ORDER_ERROR.getCode()).setMessage(TradeErrorCode.NOT_FUND_PAY_ORDER_ERROR.getMsg());
    }

    @Override
    public TQueryOrderThirdpartyInfoResponse queryOrderThirdpartyInfo(TQueryOrderThirdpartyInfoRequest request) {
        TQueryOrderThirdpartyInfoResponse tQueryOrderThirdpartyInfoResponse = new TQueryOrderThirdpartyInfoResponse();
        tQueryOrderThirdpartyInfoResponse.setCode(0);
        List<QueryOrderThirdpartyInfo> orderThirdpartyInfoList = new ArrayList<>();
        tQueryOrderThirdpartyInfoResponse.setOrderThirdpartyInfoList(orderThirdpartyInfoList);
        if (request.getUserId() < 1 || request.getOrderId() < 1) {
            return tQueryOrderThirdpartyInfoResponse;
        }

        UserUser userUser = userInfoThriftClient.getUserById(request.getUserId());
        if (userUser == null) {
            return tQueryOrderThirdpartyInfoResponse;
        }
        Routing routing = new Routing()
                .setLookupId(routingThriftService.getLookupKeyByUserCid(userUser.getCid()));
        DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.SLAVE, DatabaseEnum.order, routing);
        TradeOrderModel tradeOrderModel = tradeOrderModelService.selectById(request.getOrderId(), (long)request.getUserId());
        if (tradeOrderModel == null || !StringUtils.equals(TradeTypeEnum.payment.name(), tradeOrderModel.getTradeType())
                || StringUtils.isBlank(tradeOrderModel.getThirdChannel())) {
            return tQueryOrderThirdpartyInfoResponse;
        }
        QueryOrderThirdpartyInfo queryOrderThirdpartyInfo = new QueryOrderThirdpartyInfo();
        //   * 状态
        //  public String status; // required
        //   * 先乘后付扣款状态
        //  public String pauCutStatus; // optional
        try {
            queryOrderThirdpartyInfo.setPayTime(DateUtil.date2String2(tradeOrderModel.getSuccessTime()));
        } catch (Exception ignored) {}
        String payChannelNameMapStr = LoadPropertyUtil.getProperty("pay_channel_name_map", "{\n" +
                "    \"alipay\": \"支付宝支付\",\n" +
                "    \"weixin\": \"微信支付\",\n" +
                "    \"jingdong\": \"京东支付\",\n" +
                "    \"unionpay\": \"银联支付\"\n" +
                "}");
        Map<String, Object> payChannelNameMap = JsonMapper.json2Map(payChannelNameMapStr);
        queryOrderThirdpartyInfo.setPayChannel(tradeOrderModel.getThirdChannel());
        queryOrderThirdpartyInfo.setPayChannelName(payChannelNameMap.getOrDefault(tradeOrderModel.getThirdChannel(), tradeOrderModel.getThirdChannel()).toString());
        queryOrderThirdpartyInfo.setPayThirdNo(StringUtils.isNotBlank(tradeOrderModel.getThirdTradeNo()) ? tradeOrderModel.getThirdTradeNo() : "");
        queryOrderThirdpartyInfo.setType("支付");

        List<TradeAmountDetailsModel> tradeAmountDetailsModels = tradeAmountDetailsModelService.selectByTradeOrderId(request.getOrderId(), (long)request.getUserId());
        for (TradeAmountDetailsModel tradeAmountDetailsModel : tradeAmountDetailsModels) {
            if ("third".equals(tradeAmountDetailsModel.getType())) {
                queryOrderThirdpartyInfo.setPayAmount(tradeAmountDetailsModel.getAmount().toString());
                break;
            }
        }


        boolean pauFlag = false;
        TradeOrderNote tradeOrderNote = JsonMapper.json2Bean(tradeOrderModel.getNote(), TradeOrderNote.class);
        if (StringUtils.isNotBlank(tradeOrderNote.getPauCutFlag())) {
            //先乘后付订单
            if (StringUtils.equals(TChannelEnum.alipay.name(), tradeOrderModel.getThirdChannel())) {
                queryOrderThirdpartyInfo.setPayChannelName("支付宝-先享后付");
                pauFlag = true;
            } else if (StringUtils.equals(TChannelEnum.weixin.name(), tradeOrderModel.getThirdChannel())) {
                queryOrderThirdpartyInfo.setPayChannelName("微信-先享后付");
                pauFlag = true;
            }
            String pauCutFlagComment = getPauCutFlagComment(tradeOrderNote.getPauCutFlag());
            if (StringUtils.isNotBlank(tradeOrderNote.getPauCutComment())) {
                queryOrderThirdpartyInfo.setPauCutStatus(tradeOrderNote.getPauCutComment() + "-" + pauCutFlagComment);
            } else {
                queryOrderThirdpartyInfo.setPauCutStatus(pauCutFlagComment);
            }
        }



        if (tradeOrderNote.getHold()) {
            queryOrderThirdpartyInfo.setStatus("挂起");
        }

        if (request.getDriverId() > 1) {
            //查询是否存在到账订单
            List<TradeOrderModel> transferTradeOrderModels = tradeOrderModelService.selectByIdAndTradeTypes(Collections.singletonList(TradeTypeEnum.transfer.name()),
                    request.getOrderId(), (long)request.getDriverId());
            if (!transferTradeOrderModels.isEmpty()) {
                //有到账
                queryOrderThirdpartyInfo.setStatus("到账");
            }
        }

        List<TradeOrderModel> refundTradeOrderModels = tradeOrderModelService.selectByIdAndTradeTypes(Collections.singletonList(TradeTypeEnum.refund.name()),
                request.getOrderId(), (long) request.getUserId());
        if (!refundTradeOrderModels.isEmpty()) {
            queryOrderThirdpartyInfo.setStatus("退款");
        }
        if (StringUtils.isBlank(queryOrderThirdpartyInfo.getStatus())) {
            queryOrderThirdpartyInfo.setStatus("支付");
        }
        orderThirdpartyInfoList.add(queryOrderThirdpartyInfo);
        for (TradeOrderModel refundTradeOrderModel : refundTradeOrderModels) {
            if (StringUtils.isNotBlank(refundTradeOrderModel.getThirdChannel())) {
                List<TradeAmountDetailsModel> refundTradeAmountDetailsModels = tradeAmountDetailsModelService.selectByTradeOrderId(refundTradeOrderModel.getId(), (long)request.getUserId());
                for (TradeAmountDetailsModel refundTradeAmountDetailsModel : refundTradeAmountDetailsModels) {
                    if ("third".equals(refundTradeAmountDetailsModel.getType())) {
                        QueryOrderThirdpartyInfo refundQueryOrderThirdpartyInfo = new QueryOrderThirdpartyInfo();
                        refundQueryOrderThirdpartyInfo.setType("退款");
                        refundQueryOrderThirdpartyInfo.setStatus("到账");
                        refundQueryOrderThirdpartyInfo.setPayAmount(refundTradeAmountDetailsModel.getAmount().toString());
                        refundQueryOrderThirdpartyInfo.setPayThirdNo(StringUtils.isNotBlank(tradeOrderModel.getThirdTradeNo()) ? tradeOrderModel.getThirdTradeNo() : "");
                        if (pauFlag) {
                            if (StringUtils.equals(TChannelEnum.alipay.name(), tradeOrderModel.getThirdChannel())) {
                                refundQueryOrderThirdpartyInfo.setPayChannelName("支付宝-先享后付");
                            } else if (StringUtils.equals(TChannelEnum.weixin.name(), tradeOrderModel.getThirdChannel())) {
                                refundQueryOrderThirdpartyInfo.setPayChannelName("微信-先享后付");
                            } else {
                                refundQueryOrderThirdpartyInfo.setPayChannelName(tradeOrderModel.getThirdChannel());
                            }
                        } else {
                            refundQueryOrderThirdpartyInfo.setPayChannelName(payChannelNameMap.getOrDefault(tradeOrderModel.getThirdChannel(), tradeOrderModel.getThirdChannel()).toString());
                        }
                        refundQueryOrderThirdpartyInfo.setPayChannel(tradeOrderModel.getThirdChannel());
                        try {
                            refundQueryOrderThirdpartyInfo.setPayTime(DateUtil.date2String2(refundTradeOrderModel.getSuccessTime()));
                        } catch (Exception ignored){}
                        orderThirdpartyInfoList.add(refundQueryOrderThirdpartyInfo);
                    }
                }
            }
        }
        orderThirdpartyInfoList.sort(Comparator.comparing(QueryOrderThirdpartyInfo :: getPayTime));
        return tQueryOrderThirdpartyInfoResponse;
    }

    private String getPauCutFlagComment(String pauCutFlag) {
        switch (pauCutFlag) {
            case "create": return "未扣款";
            case "processing": return "扣款中";
            case "success": return "扣款成功";
            case "fail": return "扣款失败";
            case "finish": return "终止扣款";
        }
        return null;
    }

    @Override
    public TradeQueryCommonResponse queryOrderActualPaymentSum(TQueryOrderActualPaymentSumRequest request) {
        TradeQueryCommonResponse tradeQueryCommonResponse = new TradeQueryCommonResponse();
        tradeQueryCommonResponse.setCode(0);
        tradeQueryCommonResponse.setRet("0");
        if (request.getUserId() < 1) {
            return tradeQueryCommonResponse;
        }
        Date start,end;
        try {
            start = new SimpleDateFormat("yyyy-MM").parse(request.getDate());
            Calendar cal = Calendar.getInstance();
            cal.setTime(start);
            cal.add(Calendar.MONTH, 1);
            cal.add(Calendar.SECOND, -1);
            end = cal.getTime();
        } catch (Exception e) {
            return tradeQueryCommonResponse;
        }

        UserUser userUser = userInfoThriftClient.getUserById(request.getUserId());
        if (userUser == null) {
            return tradeQueryCommonResponse;
        }
        Routing routing = new Routing()
                .setLookupId(routingThriftService.getLookupKeyByUserCid(userUser.getCid()));
        DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.SLAVE, DatabaseEnum.order, routing);
        Map<String, Object> businessTypeMap = JsonMapper.json2Map(LoadPropertyUtil.getProperty("order_actual_payment_business_type_map", "{\"carpool_station\":\"carpool_station_cancel\"}"));
        List<String> businessTypeList = new ArrayList<>();
        businessTypeList.add(request.getBusinessType());
        if (businessTypeMap.get(request.getBusinessType()) != null) {
            businessTypeList.addAll(Arrays.asList(businessTypeMap.get(request.getBusinessType()).toString().split(",")));
        }
        List<TradeOrderModel> tradeOrderModels = tradeOrderModelService.selectTradedOrderListByUserId(request.getUserId(),
                businessTypeList,
                Arrays.asList(TradeTypeEnum.payment.name(), TradeTypeEnum.refund.name()),
                TradeOrderStatusEnum.success.name(), start, end);


        List<TradeOrderModel> newTradeOrderModels = new ArrayList<>();
        for (TradeOrderModel tradeOrderModel : tradeOrderModels) {
            TradeOrderNote tradeOrderNote = JsonMapper.json2Bean(tradeOrderModel.getNote(), TradeOrderNote.class);
            if (StringUtils.equals(TradeTypeEnum.payment.name(), tradeOrderModel.getTradeType())) {
                if (tradeOrderNote != null && StringUtils.isNotBlank(tradeOrderNote.getPauCutFlag()) && !StringUtils.equals(tradeOrderNote.getPauCutFlag(), PaymentPauCutFlageEnum.success.name())) {
                    continue;
                }
                newTradeOrderModels.add(tradeOrderModel);
            } else {
                boolean validRefundFlag = true;
                for (TradeOrderModel tradeOrderModel1 : tradeOrderModels) {
                    if (tradeOrderModel1.getId().equals(tradeOrderModel.getPayTradeNo())) {
                        TradeOrderNote tradeOrderNote1 = JsonMapper.json2Bean(tradeOrderModel1.getNote(), TradeOrderNote.class);
                        if (tradeOrderNote1 != null && StringUtils.isNotBlank(tradeOrderNote1.getPauCutFlag()) && !StringUtils.equals(tradeOrderNote1.getPauCutFlag(), PaymentPauCutFlageEnum.success.name())) {
                            validRefundFlag = false;
                            break;
                        }
                    }
                }
                if (validRefundFlag) {
                    newTradeOrderModels.add(tradeOrderModel);
                }
            }
        }
        if (newTradeOrderModels.isEmpty()) {
            return tradeQueryCommonResponse;
        }


        BigDecimal actualPaymentSum = BigDecimal.ZERO;
        Map<Long, TradeOrderModel> tradeOrderModelMap = new HashMap<>();
        for (TradeOrderModel tradeOrderModel : newTradeOrderModels) {
            tradeOrderModelMap.put(tradeOrderModel.getId(), tradeOrderModel);
        }
        List<TradeAmountDetailsModel> tradeAmountDetailsModels = tradeAmountDetailsModelService.selectByTradeOrderIds(new ArrayList<>(tradeOrderModelMap.keySet()), (long) request.getUserId());
        for (TradeAmountDetailsModel tradeAmountDetailsModel : tradeAmountDetailsModels) {
            if (StringUtils.equalsAny(tradeAmountDetailsModel.getType(), "driver", "passenger", "taxi", "third")) {
                if (StringUtils.equals(TradeTypeEnum.payment.name(), tradeOrderModelMap.get(tradeAmountDetailsModel.getTradeOrderId()).getTradeType())) {
                    actualPaymentSum = actualPaymentSum.add(tradeAmountDetailsModel.getAmount());
                } else {
                    actualPaymentSum = actualPaymentSum.subtract(tradeAmountDetailsModel.getAmount());
                }
            }
        }
        tradeQueryCommonResponse.setRet(actualPaymentSum.setScale(2, 4).toString());
        return tradeQueryCommonResponse;
    }

    public UserOrder queryUserOrderByUserIdAndOrderId(Long userId, Long orderId) {
        //支付单信息
        TradeOrderModel tradeOrderModel = tradeOrderModelService.selectById(orderId, userId);
        TradeOrderNote tradeOrderNote = TradeOrderNote.create(tradeOrderModel.getNote());

        //是否支付成功
        boolean isPaid = TradeOrderStatusEnum.success.name().equals(tradeOrderModel.getStatus());

        //支付金额组成
        List<TradeAmountDetailsModel> tradeAmountDetailsModels = tradeAmountDetailsModelService.selectByTradeOrderId(orderId, userId);
        TradeAmountDetailsMultiple tradeAmountDetailsMultiple = new TradeAmountDetailsMultiple(tradeAmountDetailsModels);

        //优惠券信息
        TradeCouponDetailsModel tradeCouponDetailsModel = tradeCouponDetailsModelService.selectCouponDetailsByOrderId(orderId, userId);

        //组装UserOrder结构体
        UserOrder userOrder = createUserOrder(tradeOrderModel, tradeOrderNote, tradeAmountDetailsMultiple, tradeCouponDetailsModel);

        //查询是否取消了订单
        List<TradeOrderModel> tradeOrderModels = tradeOrderModelService.selectByOriIdAndBusinessType(orderId, TradeTypeEnum.refund.name(), TBusinessEnum.carpool_cancel.name(), userId);
        if (CollectionUtils.isNotEmpty(tradeOrderModels)) {
            //订单取消
            TradeOrderModel refundOrderModel = tradeOrderModels.get(0);
            Date cancelTime = refundOrderModel.getCreateTime();
            Date refundTime = refundOrderModel.getCreateTime();

            userOrder.setCancelTime(cancelTime);
            userOrder.setRefundTime(refundTime);

            userOrder.setStatus("refunded");

            return userOrder;
        }

        Long driverId = tradeOrderNote.getDriverId();
        boolean isTransfer = false;
        if (driverId != null && driverId > 0) {
            //发生过到账
            isTransfer = true;
            List<TradeOrderModel> transferOrderModels = tradeOrderModelService.selectByOriIdAndBusinessType(orderId, TradeTypeEnum.transfer.name(), TBusinessEnum.carpool_fee.name(), driverId);
            if (CollectionUtils.isNotEmpty(transferOrderModels)) {
                TradeOrderModel transferOrderModel = transferOrderModels.get(0);
                userOrder.setTransferTime(transferOrderModel.getCreateTime());
            }
        }

        List<TradeOrderModel> refundOrderModels = tradeOrderModelService.selectRefundTradeListByOrderId(orderId, userId);
        if (CollectionUtils.isNotEmpty(refundOrderModels)) {
            //有过退款，但又不是取消
            BigDecimal totalRefundAmount = refundOrderModels.stream().map(TradeOrderModel::getTotalPrice).collect(() -> BigDecimal.ZERO, BigDecimal::add, BigDecimal::add);
            Date refundTime = refundOrderModels.get(0).getCreateTime();

            if (totalRefundAmount.compareTo(tradeOrderModel.getTotalPrice()) >= 0) {
                //支付金额全部退款了，那么变更为退款状态
                userOrder.setCancelTime(refundTime);
                userOrder.setRefundTime(refundTime);

                userOrder.setStatus("refunded");
            } else {
                //部分退款
                if (isTransfer) {
                    userOrder.setStatus("transferred");
                } else {
                    userOrder.setStatus("refunded");
                }
            }
        } else {
            //没有发生过退款
            if (isPaid) {
                if (isTransfer) {
                    // 支付后到账
                    userOrder.setStatus("transferred");
                } else {
                    // 支付后挂起
                    userOrder.setStatus(tradeOrderNote.getHold() != null && tradeOrderNote.getHold() ? "hold" : "paid");
                }
            } else {
                //未支付
                userOrder.setStatus("new");
            }
        }

        return userOrder;
    }


    private UserOrder createUserOrder(TradeOrderModel tradeOrderModel, TradeOrderNote tradeOrderNote, TradeAmountDetailsMultiple tradeAmountDetailsMultiple, TradeCouponDetailsModel tradeCouponDetailsModel) {
        UserOrder userOrder = new UserOrder();
        userOrder.setId(tradeOrderModel.getId());
        userOrder.setOrderCid(userOrder.getId() + "");
        userOrder.setUserId(tradeOrderModel.getUserId().intValue());
        userOrder.setProductType("booking_ride");
        long rideId = Long.parseLong(tradeOrderModel.getMajorProductId());
        userOrder.setProductId(rideId);
        userOrder.setUnitPrice(tradeOrderModel.getTotalPrice());
        BigDecimal zero = new BigDecimal("0.0");
        userOrder.setUnitServiceFee(zero);
        userOrder.setQuantity(1);
        userOrder.setOrderServiceFee(zero);
        userOrder.setUnitSubsidy(zero);
        userOrder.setOrderSubsidy(zero);

        //服务费
        BigDecimal carpoolServiceFee = carpoolReadThriftSupportService.getCarpoolServiceFee(rideId);
        userOrder.setInsuranceFee(carpoolServiceFee);
        userOrder.setSum(tradeOrderModel.getTotalPrice());

        BigDecimal sumReceivable = userOrder.getUnitPrice().subtract(userOrder.getUnitServiceFee()).multiply(new BigDecimal(userOrder.getQuantity()))
                .subtract(userOrder.getOrderServiceFee()).subtract(userOrder.getInsuranceFee()).add(userOrder.getOrderSubsidy())
                .add(userOrder.getUnitSubsidy().multiply(new BigDecimal(userOrder.getQuantity()))).setScale(2, RoundingMode.HALF_UP);
        userOrder.setSumReceivable(sumReceivable);

        userOrder.setPaymentChannelCid(tradeOrderModel.getThirdChannel());
        userOrder.setPaymentTradeCid(StringUtils.defaultIfBlank(tradeOrderModel.getThirdTradeNo(),""));
        userOrder.setMoney(tradeAmountDetailsMultiple.getThird());
        userOrder.setUserBalanceCredit(tradeAmountDetailsMultiple.getPassenger());
        userOrder.setDriverBalanceCredit(tradeAmountDetailsMultiple.getPassengerNotWithdraw());
        userOrder.setCouponId(String.valueOf(tradeCouponDetailsModel != null ? tradeCouponDetailsModel.getCouponId() : 0L));
        userOrder.setCouponCredit(tradeAmountDetailsMultiple.getCoupon());
        userOrder.setStatus("new");
        userOrder.setCreateTime(tradeOrderModel.getCreateTime());
        userOrder.setPayTime(tradeOrderModel.getSuccessTime());
        userOrder.setCancelTime(null);
        userOrder.setRefundTime(null);
        userOrder.setAttribute(0);
        userOrder.setTransferTime(null);
        userOrder.setSysOrderGroup(tradeOrderNote.getSysOrderGroup() != null ? tradeOrderNote.getSysOrderGroup() : 0);
        return userOrder;
    }

    @SneakyThrows
    public QueryOrderCouponGoodsInfoResponse queryOrderCouponGoodsInfo(QueryOrderCouponGoodsInfoRequest request){
        OrderInfoRequest request2 = new OrderInfoRequest();
        request2.setUserId(request.getUserId());
        request2.setPayCode(String.valueOf(request.getOrderId()));
        request2.setSourceCid(request.getSourceCid());
        OrderInfoResult goodsRel = activityGoodsOrderThriftSupportService.getOrderInfo(request2);
        if (goodsRel == null || goodsRel.getCode() != 0) {
            log.error("queryOrderCouponGoodsInfo goodsRel不存在. orderId={}", request.getOrderId());
            QueryOrderCouponGoodsInfoResponse response = new QueryOrderCouponGoodsInfoResponse();
            response.setCode(TradeErrorCode.PARAM_ERROR.getCode());
            response.setMessage(TradeErrorCode.PARAM_ERROR.getMsg());
            return response;
        }

        //支付单信息
        TradeOrderModel tradeOrderModel = tradeOrderModelService.selectById(request.getOrderId(), request.getUserId());
        if(tradeOrderModel == null){
            log.error("queryOrderCouponGoodsInfo订单不存在. orderId={}", request.getOrderId());
            QueryOrderCouponGoodsInfoResponse response = new QueryOrderCouponGoodsInfoResponse();
            response.setCode(TradeErrorCode.PARAM_ERROR.getCode());
            response.setMessage(TradeErrorCode.PARAM_ERROR.getMsg());
            return response;
        }

        //支付产品组成
        List<TradeProductDetailsModel> tradeProductDetailsModels = tradeProductDetailsModelService.selectByOrderId(request.getOrderId(), request.getUserId());
        if (CollectionUtils.isEmpty(tradeProductDetailsModels)) {
            log.error("queryOrderCouponGoodsInfo.tradeProductDetailsModels产品组成不存在. orderId={}", request.getOrderId());
            QueryOrderCouponGoodsInfoResponse response = new QueryOrderCouponGoodsInfoResponse();
            response.setCode(TradeErrorCode.PARAM_ERROR.getCode());
            response.setMessage(TradeErrorCode.PARAM_ERROR.getMsg());
            return response;
        }

        tradeProductDetailsModels = tradeProductDetailsModels.stream()
                .filter(productDetail -> Lists.newArrayList(TTradeProductTypeEnum.couponPackage.name(), TTradeProductTypeEnum.singleCouponPackage.name()).contains(productDetail.getType()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(tradeProductDetailsModels)) {
            log.info("queryOrderCouponGoodsInfo.tradeProductDetailsModels产品组成不含券包. orderId={}", request.getOrderId());
            QueryOrderCouponGoodsInfoResponse response = new QueryOrderCouponGoodsInfoResponse();
            response.setCode(0);
            response.setMessage("");
            return response;
        }

        QueryOrderCouponGoodsInfoResponse response = new QueryOrderCouponGoodsInfoResponse();
        response.setCode(0);
        response.setMessage("");
        response.setGoodsTitle(goodsRel.getData().getGoodsTitle());
        response.setGoodsPrice(tradeProductDetailsModels.get(0).getPrice().toString());
        return response;


    }



    public TradeOrderRecordData queryTradeOrderRecord(TradeOrderRecordRequest request) {

        long userId = request.getUserId();
        long orderId = request.getOrderId();

        if(userId<=0){
            throw new DException(TradeErrorCode.USER_ID_ERROR) ;
        }
        if(orderId<=0){
            throw new DException(TradeErrorCode.TRADE_ID_ERROR) ;
        }
        Routing routing = new Routing()
                .setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getOrderId())));
        DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.SLAVE, DatabaseEnum.order, routing);
        //查询Order信息
        TradeOrderRecordData recordData = this.selectTradeOrderRecordData(orderId, userId);
        if(Objects.nonNull(recordData) && recordData.getBillId()>0){
            TradeOrderBillData billData= this.selectTradeOrderBillData(recordData.getBillId());//billTradeActionDate.billOrderById(tradeOrderModel.getBillId());
            recordData.setBillData(billData);
        }
        XXXOrderMapping.statusMap(recordData);//映射状态码
        return recordData;

    }


    public List<TradeOrderRecordData> queryTradeOrderRecord(List<TradeOrderRecordRequest> requestList) {
        if(CollectionUtils.isEmpty(requestList)){
            return Collections.EMPTY_LIST;
        }
        return requestList.stream().map(this::queryTradeOrderRecord).collect(Collectors.toList());
    }


    /**
     * @param driverId
     * @param orderIds
     * @param beginDate
     * @param endDate
     * @return
     */
    public BigDecimal queryDriverIncome(Long driverId , List<Long> orderIds, String beginDate, String endDate) {

        if(Objects.isNull(driverId) || driverId<=0){
            throw new DException(TradeErrorCode.USER_ID_ERROR);
        }
        if(CollectionUtils.isEmpty(orderIds)){
            throw new DException(TradeErrorCode.TRADE_ID_ERROR);
        }

        Map<String, List<Long>> orderIdLookupMap = orderIds.stream().map(id -> {
            OrderLookupKey orderLookupKey = new OrderLookupKey();
            orderLookupKey.setOrderId(id);
            orderLookupKey.setLookupKey(String.valueOf(idConverterImpl.convert(id).getCityMap()));
            return orderLookupKey;
        }).collect(Collectors.toMap(OrderLookupKey::getLookupKey,
                s -> Lists.newArrayList(s.getOrderId()),
                (List<Long> v1, List<Long> v2) -> {
                    v1.addAll(v2);
                    return v1;
                }
        ));
        if(log.isDebugEnabled()){
            log.debug("司机:{}的收入统计分步情况为:{}",driverId, JSONUtil.toJsonStr(orderIdLookupMap));
        }
        Set<String> lookups = orderIdLookupMap.keySet();
        BigDecimal income = BigDecimal.ZERO;
        for (String lookup : lookups) {
            List<Long> payTradeNoList = orderIdLookupMap.get(lookup);
            //设置库的路由信息
            DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.SLAVE, DatabaseEnum.order, new Routing().setLookupId(lookup));
            BigDecimal incomeSingle = queryDriverIncomeAmountForTaxi(driverId, lookup, payTradeNoList);
            //查询支出部分
            BigDecimal outSingle = queryDriverOutAmountForTaxi(driverId, payTradeNoList);
            log.debug("司机:{}在库{}的收入为:{}  支出为:{}",driverId,lookup,incomeSingle,outSingle);
            income = income.add(incomeSingle.subtract(outSingle));
        }
        log.debug("司机:{}总的收入为:{}",driverId,income);
        return income;
    }

    private BigDecimal queryDriverOutAmountForTaxi(Long driverId, List<Long> payTradeNoList) {
        List<TradeOrderModel> tradeOrderModelsOut = tradeOrderModelService.selectByOriIds(Lists.newArrayList(TradeTypeEnum.confiscate.name()),
                payTradeNoList,
                driverId,
                Lists.newArrayList(TBusinessEnum.taxi_commission.name()), null);
        BigDecimal outSingle = tradeOrderModelsOut.stream()
                .filter(o -> TradeOrderStatusEnum.success.name().equals(o.getStatus()))
                .map(TradeOrderModel::getTotalPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return outSingle;
    }

    private BigDecimal queryDriverIncomeAmountForTaxi(Long driverId, String lookup, List<Long> ids) {
        List<TradeOrderModel> tradeOrderModels = tradeOrderModelService.selectByOriIds(Lists.newArrayList(TradeTypeEnum.transfer.name()),
                ids,
                driverId,
                Lists.newArrayList(TBusinessEnum.taxi_fee.name()), null);
        if(log.isDebugEnabled()){
            log.debug("司机:{}在库:{}的订单个数为:{}", driverId, lookup, tradeOrderModels.size());
        }
        BigDecimal incomeSingle = tradeOrderModels.stream()
                .filter(o -> TradeOrderStatusEnum.success.name().equals(o.getStatus()))
                .map(TradeOrderModel::getTotalPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return incomeSingle;
    }


    private TradeOrderBillData selectTradeOrderBillData(long billId) {
        TradeBillModel tradeBillModel = billTradeActionDate.billOrderById(billId);
        if(Objects.isNull(tradeBillModel)){
            log.info("查询不到bill:{}的记录",billId);
            return null;
        }
        TradeOrderBillData billData=new TradeOrderBillData();
        billData.setId(billId);
        billData.setPaymentAmount(toStringLeft2(tradeBillModel.getPaymentAmount()));
        billData.setRefundAmount(toStringLeft2(tradeBillModel.getPayerRefundAmount()));
        billData.setReceiptsAmount(toStringLeft2(tradeBillModel.getReceiptsAmount()));
        billData.setPayState(tradeBillModel.getPayState());
        billData.setTransferState(tradeBillModel.getTransferState());
        if(BizLineEnum.TAXI.getName().equals(tradeBillModel.getProductBiz() )&& TradeState.BillState.paid.name().equals(tradeBillModel.getPayState())
                && StringUtils.isBlank(billData.getTransferState())
                && containsAnyOfTaxiqr(tradeBillModel.getPayTypeTag())){
            billData.setTransferState("transfer");
        }
        billData.setRiskTag(StringUtils.isEmpty(tradeBillModel.getRiskTag())?"0":tradeBillModel.getRiskTag());
        billData.setFreeTag(tradeBillModel.getFreeTag());
        billData.setPend(tradeBillModel.getPend());//挂起状态
        BigDecimal paymentAmount = tradeBillModel.getPaymentAmount();//实付金额
        String refundMark= "";
        if(TradeState.BillState.paid.name().equals(tradeBillModel.getPayState()) && BigDecimalUtil.geZero(paymentAmount)){;//退款状态
            refundMark=BigDecimalUtil.eqZero(paymentAmount)? TradeState.RefundState.refundAll.name(): TradeState.RefundState.refundPart.name();
        }
        billData.setRefundState(refundMark);

        log.debug("billID:{}的记录为{}",billId,billData.toString());
        return billData;
    }

    private TradeOrderRecordData selectTradeOrderRecordData(long orderId, long userId) {
        TradeOrderModel tradeOrderModel = tradeOrderModelService.selectById(orderId, userId);
        if(Objects.isNull(tradeOrderModel)){
            throw  new DException(TradeErrorCode.NOT_FUND_PAY_ORDER_ERROR);
        }
        if(log.isDebugEnabled()){
            log.debug("userId:{} orderId:{} model:{}",userId,orderId, JsonUtil.toJson(tradeOrderModel));
        }
        TradeOrderRecordData tradeOrderRecordData=new TradeOrderRecordData();
        tradeOrderRecordData.setUserId(userId);
        tradeOrderRecordData.setOrderId(orderId);
        tradeOrderRecordData.setBusinessType(tradeOrderModel.getBusinessType());
        tradeOrderRecordData.setTradeType(tradeOrderModel.getTradeType());
        tradeOrderRecordData.setStatus(tradeOrderModel.getStatus());
        tradeOrderRecordData.setAmount(toStringLeft2(tradeOrderModel.getTotalPrice()));
        tradeOrderRecordData.setThirdChannel(tradeOrderModel.getThirdChannel());
        if(Objects.nonNull(tradeOrderModel.getBillId())){
            tradeOrderRecordData.setBillId(tradeOrderModel.getBillId());
        }
        if (StringUtils.isNotBlank(tradeOrderModel.getNote())) {
            TradeOrderNote tradeOrderNote = TradeOrderNote.create(tradeOrderModel.getNote());
            TradeOrderNote.SharingNote sharingNote = tradeOrderNote.getSharingNote();
            if (Objects.nonNull(sharingNote)) {
                tradeOrderRecordData.setOpenId(sharingNote.getDriverOpenId());
            }
        }
        return tradeOrderRecordData;
    }


    public QueryUserPaymentResponse queryUserPayment(QueryUserPaymentRequest request){
        log.info("queryUserPayment.request={}", JsonMapper.toJson(request));
        if (request.getUserId() <= 0) {
            QueryUserPaymentResponse response = new QueryUserPaymentResponse();
            response.setCode(TradeErrorCode.PARAM_ERROR.getCode());
            response.setMessage(TradeErrorCode.PARAM_ERROR.getMsg());
            return response;
        }

        //查询userCid，路由到库
        UserUser user = userInfoThriftClient.getUserById((int) request.getUserId());

        if (user == null) {
            log.error("queryUserPayment查询用户cid为空. request={}", JsonMapper.toJson(request));
            QueryUserPaymentResponse response = new QueryUserPaymentResponse();
            response.setCode(TradeErrorCode.PARAM_ERROR.getCode());
            response.setMessage(TradeErrorCode.PARAM_ERROR.getMsg());
            return response;
        }

        Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyByUserCid2(user.getCid()));
        DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.SLAVE, DatabaseEnum.order, routing);

        List<TradeOrderModel> tradeOrderModels = tradeOrderModelService.selectUserPaymentsByUserId(request.getUserId(),
                request.getBusinessTypes(), request.getThirdChannels(),
                Math.min(request.getSize(), LoadPropertyUtil.getProperty("queryUserPayment.max.size", Integer.class, 10)));

        if(CollectionUtils.isEmpty(tradeOrderModels)){
            QueryUserPaymentResponse response = new QueryUserPaymentResponse();
            response.setCode(0);
            response.setMessage("");
            return response;
        }

        // 如果查到了数据，处理数据并返回
        QueryUserPaymentResponse response = new QueryUserPaymentResponse();
        response.setCode(0);
        response.setMessage("success");

        List<UserPaymentItem> userPaymentItems = tradeOrderModels.stream().map(tradeOrderModel -> {
            UserPaymentItem userPaymentItem = new UserPaymentItem();
            userPaymentItem.setTradeOrderId(tradeOrderModel.getId());
            userPaymentItem.setMajorProductId(tradeOrderModel.getMajorProductId());
            userPaymentItem.setTotalPrice(tradeOrderModel.getTotalPrice().toString());
            userPaymentItem.setBusinessType(tradeOrderModel.getBusinessType());
            userPaymentItem.setStatus(tradeOrderModel.getStatus());
            userPaymentItem.setThirdChannel(tradeOrderModel.getThirdChannel());
            userPaymentItem.setCreateTime(date2String(tradeOrderModel.getCreateTime()));
            userPaymentItem.setSuccessTime(date2String(tradeOrderModel.getSuccessTime()));
            return userPaymentItem;
        }).collect(Collectors.toList());
        response.setUserPayments(userPaymentItems);

        return response;
    }

    private String date2String(Date date){
        try {
            return DateUtil.date2String(date);
        }catch (Exception ex){
            return "";
        }
    }


    public RefundableDetails getRefundDetail(long orderId, long userId, String compensation) throws TException {
        Routing routing = new Routing()
                .setLookupId(routingThriftService.getLookupKeyById(String.valueOf(orderId)));
        DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.SLAVE, DatabaseEnum.order, routing);
        TradeOrderModel tradeOrderModel = tradeOrderModelService.selectById(orderId, userId);
        if (tradeOrderModel == null){
            throw new DException(TradeErrorCode.NOT_FUND_PAY_ORDER_ERROR);
        }
        BigDecimal goodsAmount = queryGoodsAmount(tradeOrderModel.getUserId(), tradeOrderModel.getId());
        QueryAmountResult queryAmountResult = queryAmountResult(tradeOrderModel, false, goodsAmount);
        BigDecimal highwayAmount = queryHighwayAmount(tradeOrderModel.getUserId(), tradeOrderModel.getId());
        // 只查询行程的钱，不需要高速费和券包的钱
        BigDecimal thirdMoney = AmountConvertUtil.yuan2BigDecimal(queryAmountResult.getThird()).subtract(highwayAmount);
        BigDecimal balance = AmountConvertUtil.yuan2BigDecimal(queryAmountResult.getPassenger()).add(AmountConvertUtil.yuan2BigDecimal(queryAmountResult.getPassengerNotWithdraw()));
        BigDecimal compensationB = AmountConvertUtil.yuan2BigDecimal(compensation);
        String payChannel = queryAmountResult.getThirdChannel();
        RefundableDetails result = new RefundableDetails();

        /**
         *  扣除补偿金后应退多少钱，优先扣除余额支付的钱
         *  以写为原逻辑 ：
         *  if (StringUtils.isNotBlank(compensation)) {
         *             try {
         *                 BigDecimal compensationB = new BigDecimal(compensation).setScale(2, RoundingMode.HALF_UP);
         *                 if (compensationB.compareTo(BigDecimal.ZERO) > 0) {
         *                     if (balance.compareTo(compensationB) > 0) {
         *                         result.setBalance(balance.subtract(compensationB).setScale(2, RoundingMode.HALF_UP).toString());
         *                         if (thirdMoney.compareTo(BigDecimal.ZERO) > 0) {
         *                             result.setThirdMoney(thirdMoney.toString());
         *                             result.setPayChannel(queryAmountResult.getThirdChannel());
         *                         }
         *                     } else if (thirdMoney.add(balance).compareTo(compensationB) > 0) {
         *                         result.setThirdMoney(thirdMoney.add(balance).subtract(compensationB).setScale(2, RoundingMode.HALF_UP).toString());
         *                         result.setPayChannel(queryAmountResult.getThirdChannel());
         *                     }
         *                     return result;
         *                 }
         *             } catch (Exception e) {
         *             }
         *         }
         *         if (balance.compareTo(BigDecimal.ZERO) > 0) {
         *             result.setBalance(balance.toString());
         *         }
         *         if (thirdMoney.compareTo(BigDecimal.ZERO) > 0) {
         *             result.setThirdMoney(thirdMoney.toString());
         *             result.setPayChannel(queryAmountResult.getThirdChannel());
         *         }
         */

        if (compensationB.compareTo(balance.add(thirdMoney)) >= 0){
            return result;
        }
        result.setBalance(BigDecimal.ZERO.max(balance.subtract(compensationB)).toString());
        result.setThirdMoney(thirdMoney.min(thirdMoney.add(balance).subtract(compensationB)).toString());
        result.setPayChannel(payChannel);
        return result;

    }

    public TradeQueryCommonResponse querySumPaymentByRideAndMonth(TQuerySumPaymentParamRequest param) {
        TradeQueryCommonResponse tradeQueryCommonResponse = new TradeQueryCommonResponse();
        tradeQueryCommonResponse.setCode(0);
        tradeQueryCommonResponse.setRet("0");
        if ("dingtalk".equals(param.getScene())) {
            Integer userSumPayMonth = RedisClusterUtil.get(USER_SUM_PAY_MONTH, param.getUserId() + "_" + param.getMonth() + "_" + param.getScene(), Integer.class);
            if (userSumPayMonth != null) {
                tradeQueryCommonResponse.setRet(userSumPayMonth.toString());
                log.debug("querySumPaymentByRideAndMonth 返回结果（缓存）.param:{}, result:{}", JsonMapper.toJson(param), JsonMapper.toJson(tradeQueryCommonResponse));
                return tradeQueryCommonResponse;
            }
            AtomicInteger sumMoney = new AtomicInteger(0);
            String startTime,endTime;
            try {
                Calendar cal = Calendar.getInstance();
                cal.setTime(new SimpleDateFormat("yyyyMM").parse(param.getMonth()));
                cal.set(Calendar.DAY_OF_MONTH, 1);
                cal.set(Calendar.HOUR_OF_DAY, 0);
                cal.set(Calendar.MINUTE, 0);
                cal.set(Calendar.SECOND, 0);
                cal.set(Calendar.MILLISECOND, 0);
                startTime = DateUtil.date2String(cal.getTime());
                cal.add(Calendar.MONTH, 1);
                cal.add(Calendar.MILLISECOND, -1);
                endTime = DateUtil.date2String(cal.getTime());
            } catch (Exception e) {
                log.debug("querySumPaymentByRideAndMonth 查询时间参数错误.param:{}, result:{}", JsonMapper.toJson(param), JsonMapper.toJson(tradeQueryCommonResponse));
                return tradeQueryCommonResponse;
            }

            List<Map> rideMapList = carpoolReadThriftSupportService.selectPassengerOnRideList(param.getUserId(), startTime, endTime);
            if (org.springframework.util.CollectionUtils.isEmpty(rideMapList)) {
                log.debug("querySumPaymentByRideAndMonth 本月无行程.param:{}, result:{}", JsonMapper.toJson(param), JsonMapper.toJson(tradeQueryCommonResponse));
                return tradeQueryCommonResponse;
            }
            List<String> rideIdList = rideMapList
                    .stream()
                    .filter(rideMap -> rideMap != null && rideMap.get("sourceCid") != null && "dingtalk_carpool".equals(rideMap.get("sourceCid").toString()))
                    .map(map -> map.get("id").toString())
                    .collect(Collectors.toList());
            if (rideIdList.isEmpty()) {
                log.debug("querySumPaymentByRideAndMonth 本月无钉钉顺风车订单.param:{}, result:{}", JsonMapper.toJson(param), JsonMapper.toJson(tradeQueryCommonResponse));
                return tradeQueryCommonResponse;
            }
            //钉钉存在完单行程则查询数据库，此时开始做路由信息
            try {
                Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyByUserCid2(UserInfoUtil.getUsercidById((int)param.getUserId())));
                DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.SLAVE, DatabaseEnum.order, routing);
            } catch (Exception e) {
                log.error("querySumPaymentByRideAndMonth 路由失败，默认返回金额0.param:{}, result:{}", JsonMapper.toJson(param), JsonMapper.toJson(tradeQueryCommonResponse));
                return tradeQueryCommonResponse;
            }
            List<TradeOrderModel> payTradeOrderList = tradeOrderModelService.querySuccessByMajorProductIdList(rideIdList, TradeTypeEnum.payment.name(), null, param.getUserId());
            if (!org.springframework.util.CollectionUtils.isEmpty(payTradeOrderList)) {
                List<Long> tradeNoList = payTradeOrderList.stream().map(TradeOrderModel::getId).collect(Collectors.toList());
                List<TradeAmountDetailsModel> tradeAmountDetailsModels = tradeAmountDetailsModelService.selectByTradeOrderIds(tradeNoList, param.getUserId());
                //统计总支付金额
                tradeAmountDetailsModels.forEach(tradeAmountDetailsModel -> {
                    if (actualPayTypeList.contains(tradeAmountDetailsModel.getType())) {
                        sumMoney.addAndGet(tradeAmountDetailsModel.getAmount().multiply(BigDecimal.valueOf(100)).setScale(0, 4).intValue());
                    }
                });

                List<TradeOrderModel> refundTradeOrderModels = tradeOrderModelService.selectRefundTradeList(tradeNoList, null, null, param.getUserId());
                if (!org.springframework.util.CollectionUtils.isEmpty(refundTradeOrderModels)) {
                    List<Long> refundTradeNoList = refundTradeOrderModels.stream().map(TradeOrderModel::getId).collect(Collectors.toList());
                    List<TradeAmountDetailsModel> refundTradeAmountDetailsModels = tradeAmountDetailsModelService.selectByTradeOrderIds(refundTradeNoList, param.getUserId());
                    //减去已退款金额
                    refundTradeAmountDetailsModels.forEach(refundTradeAmountDetailsModel -> {
                        if (actualPayTypeList.contains(refundTradeAmountDetailsModel.getType())) {
                            sumMoney.addAndGet(-refundTradeAmountDetailsModel.getAmount().multiply(BigDecimal.valueOf(100)).setScale(0, 4).intValue());
                        }
                    });
                }
            }
            RedisClusterUtil.setAndExpire(USER_SUM_PAY_MONTH, param.getUserId() + "_" + param.getMonth() + "_" + param.getScene(), sumMoney.toString(), 60, false);
            tradeQueryCommonResponse.setRet(sumMoney.toString());
            log.debug("querySumPaymentByRideAndMonth 返回结果（缓存）.param:{}, result:{}", JsonMapper.toJson(param), JsonMapper.toJson(tradeQueryCommonResponse));
            return tradeQueryCommonResponse;

        } else if ("common".equals(param.getScene())) {
            String resultSum = payOrderReadThriftSupportService.getPolytypeOrderMoneyByParam(param.getUserId(), param.getMonth());
            if (resultSum == null) {
                tradeQueryCommonResponse.setCode(TradeErrorCode.SYSTEM_104_ERROR.getCode()).setMessage(TradeErrorCode.SYSTEM_104_ERROR.getMsg());
            } else {
                tradeQueryCommonResponse.setRet(resultSum);
            }
        }
        return tradeQueryCommonResponse;
    }

    /**
     * 查询是否为作弊订单（根据订单号、userId）
     * @param request 需要传订List<Long> orderIdList、userId
     * @return TradeQueryCommonResponse
     */
    public TradeQueryCommonResponse isCheatingOrder(QueryTradeRequest request) {

        TradeQueryCommonResponse tradeQueryCommonResponse = new TradeQueryCommonResponse();
        tradeQueryCommonResponse.setCode(0);
        tradeQueryCommonResponse.setRet(Boolean.FALSE.toString());

        Long orderId = request.getOrderIdList().get(0);

        Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(String.valueOf(orderId)));
        DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.SLAVE, DatabaseEnum.order, routing);

        TradeOrderModel tradeOrderModel = tradeOrderModelService.selectById(request.getOrderIdList().get(0), request.getUserId());

        if (null == tradeOrderModel) {
            log.error("isCheatingOrder中selectById订单不存在 orderId:{}，userId:{}", request.getOrderIdList().get(0), request.getUserId());
            tradeQueryCommonResponse.setCode(ErrorCode.RT_PARAMS_ERROR);
            tradeQueryCommonResponse.setMessage(ErrorCode.ERR_MSG_PARAMERR);
            return tradeQueryCommonResponse;
        }

        String note = tradeOrderModel.getNote();
        if (StringUtils.isNotBlank(note)) {
            TradeOrderNote tradeOrderNote = JsonMapper.json2Bean(note, TradeOrderNote.class);
            boolean ret = tradeOrderNote.getSysOrderGroup() == 2;
            tradeQueryCommonResponse.setRet(ret ? Boolean.TRUE.toString() : Boolean.FALSE.toString());
        }
        return tradeQueryCommonResponse;
    }


    /**
     * 查询是否为挂起订单（根据订单号、userId）
     * @param request 需要传订List<Long> orderIdList、userId
     * @return TradeQueryCommonResponse
     */
    public TradeQueryCommonResponse isHoldOrder(QueryTradeRequest request) {

        TradeQueryCommonResponse tradeQueryCommonResponse = new TradeQueryCommonResponse();
        tradeQueryCommonResponse.setCode(0);
        tradeQueryCommonResponse.setRet(Boolean.FALSE.toString());

        Long orderId = request.getOrderIdList().get(0);

        Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(String.valueOf(orderId)));
        DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.SLAVE, DatabaseEnum.order, routing);

        TradeOrderModel tradeOrderModel = tradeOrderModelService.selectById(request.getOrderIdList().get(0), request.getUserId());

        if (null == tradeOrderModel) {
            log.error("isHoldOrder中selectById订单不存在 orderId:{}，userId:{}", request.getOrderIdList().get(0), request.getUserId());
            tradeQueryCommonResponse.setCode(ErrorCode.RT_PARAMS_ERROR);
            tradeQueryCommonResponse.setMessage(ErrorCode.ERR_MSG_PARAMERR);
            return tradeQueryCommonResponse;
        }

        String note = tradeOrderModel.getNote();
        if (StringUtils.isNotBlank(note)) {
            TradeOrderNote tradeOrderNote = JsonMapper.json2Bean(note, TradeOrderNote.class);
            if (tradeOrderNote.getHold()){
                tradeQueryCommonResponse.setRet(Boolean.TRUE.toString());
            }else{
                tradeQueryCommonResponse.setRet(Boolean.FALSE.toString());
            }
        }
        return tradeQueryCommonResponse;
    }
}
