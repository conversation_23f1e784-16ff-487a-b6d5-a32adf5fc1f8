package com.didapinche.trade.application.service.support;

import cn.hutool.core.text.CharSequenceUtil;
import com.didapinche.agaue.common.exception.DException;
import com.didapinche.server.commons.common.ReplyMap;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.server.commons.common.metrics.InterfaceMonitor;
import com.didapinche.server.commons.common.util.LoadPropertyUtil;
import com.didapinche.server.commons.common.util.RedisClusterUtil;
import com.didapinche.trade.application.service.support.platform.TradePlatformSupportService;
import com.didapinche.trade.domin.payment.calculate.calculateordercharge.CalculateOrder;
import com.didapinche.trade.infrastructure.constants.RedisKeyConstants;
import com.didapinche.trade.infrastructure.entities.calcilateorder.CalculateOrderChargePayInfoEntity;
import com.didapinche.trade.infrastructure.entities.calcilateorder.CalculateOrderChargeReq;
import com.didapinche.trade.infrastructure.enums.CalculateRideTypeEnum;
import com.didapinche.trade.infrastructure.exception.TradeErrorCode;
import com.didapinche.trade.infrastructure.util.ChatbotSendUtil;
import com.didapinche.trade.infrastructure.util.TradePlatformGrayUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class CalculateService {

    private static final Logger calculateLogger = LoggerFactory.getLogger("CalculateLogger");

    private static final String DING_TALK_FORMAT = "计算价格捕获型异常:{}.rideId:[{}],错误code:{},错误msg:{}";
    private static final String DING_TALK_NOT_CATCH_FORMAT = "计算价格非捕获型异常:{}.rideId:[{}],错误msg:{}";
    @Resource
    private Map<String, CalculateOrder> calculateOrderMap;
    @Resource
    private TradePlatformSupportService tradePlatformSupportService;

    public CalculateService(Map<String, CalculateOrder> calculateOrderMap) {
        this.calculateOrderMap = calculateOrderMap;
    }

    /**
     * 价格计算
     *
     * @return ReplyMap
     */
    public ReplyMap calculateOrderCharge(CalculateOrderChargeReq req) {
        calculateLogger.info("calculateOrderCharge start. req:{}", JsonMapper.toJson(req));
        //参数校验
        ReplyMap replyMap = new ReplyMap();
        if (!req.isNewGoodsFlag()) {
            if (req.getRideId() < 0 || req.getCouponId() < 0 || req.getGoodsId() < 0) {
                calculateLogger.warn("calculateOrderCharge 参数错误. req:{}", JsonMapper.toJson(req));
                return replyMap.fail(TradeErrorCode.RE_PAY_ERROR.getCode(), TradeErrorCode.RE_PAY_ERROR.getMsg());
            }
        }
        InterfaceMonitor.getInstance().addTotal("calculateOrderCharge", InterfaceMonitor.TYPE_CUSTOM);
        Boolean sendDing = LoadPropertyUtil.getProperty("trade.calculate.order.send.ding.warn", Boolean.class, true);
        try {
            //从redis取出信息进行计算
            CalculateOrderChargePayInfoEntity payInfoEntity = getPayInfoByCalculateOrderCharge(req.getRideId(), req.getRideLineIds(), req.getRideType());
            if (payInfoEntity == null) {
                calculateLogger.error("calculateOrderCharge 缓存为空. req:{}", JsonMapper.toJson(req));
                return replyMap.fail(TradeErrorCode.RE_PAY_ERROR.getCode(), TradeErrorCode.RE_PAY_ERROR.getMsg());
            }
            //灰度判断 是否调用交易前置系统算价
//            if (TradePlatformGrayUtil.gray("calc", Long.valueOf(payInfoEntity.getUserId()), req.getRideId(), false)) {
//                return tradePlatformSupportService.calculateOrderCharge(req);
//            }
            long rideId = req.getRideId();
            calculateLogger.info("calculateOrderCharge 缓存内容. rideId:{}, payInfoEntity:{}", rideId, JsonMapper.toJson(payInfoEntity));
            CalculateOrder calculateOrder = calculateOrderMap.get(req.getRideType());
            if (calculateOrder == null) {
                calculateLogger.error("calculateOrderCharge 获取计算方法策略为空. type:{}, req:{}", req.getRideType(), JsonMapper.toJson(req));
                return replyMap.fail(TradeErrorCode.RE_PAY_ERROR.getCode(), TradeErrorCode.RE_PAY_ERROR.getMsg());
            }
            //保留原始請求数据 防止被改
            CalculateOrderChargeReq oldReq = new CalculateOrderChargeReq();
            BeanUtils.copyProperties(req, oldReq);
            replyMap = calculateOrder.orderCharge(req, payInfoEntity);
            tradePlatformSupportService.callTradePlatformCalc(oldReq, payInfoEntity, replyMap.toJson());
            calculateLogger.info("calculateOrderCharge end. req:{}, replyMap:{}", JsonMapper.toJson(req), replyMap.toJson());
        } catch (DException e) {
            //埋点
            InterfaceMonitor.getInstance().addFail("calculateOrderCharge", InterfaceMonitor.TYPE_CUSTOM);
            if (Boolean.TRUE.equals(sendDing)) {
                sendMsg(CharSequenceUtil.format(DING_TALK_FORMAT, req.getRideType(), req.getRideId(), e.getErrorCode().getCode(), e.getErrorCode().getMsg()));
            }
            return replyMap.fail(e.getErrorCode().getCode(), e.getErrorCode().getMsg());
        } catch (Exception e) {
            calculateLogger.error("calculateOrderCharge error. req:{}", JsonMapper.toJson(req), e);
            InterfaceMonitor.getInstance().addFail("calculateOrderCharge", InterfaceMonitor.TYPE_CUSTOM);
            if (Boolean.TRUE.equals(sendDing)) {
                sendMsg(CharSequenceUtil.format(DING_TALK_NOT_CATCH_FORMAT, req.getRideType(), req.getRideId(), e.getMessage()));
            }
            return replyMap.fail(TradeErrorCode.RE_PAY_ERROR.getCode(), TradeErrorCode.RE_PAY_ERROR.getMsg());
        }
        return replyMap;
    }

    /**
     * 发送钉钉报警
     *
     * @param message 信息
     */
    public static void sendMsg(String message) {
        try {
            Boolean dingSwitch = LoadPropertyUtil.getProperty("calculate.order.charge.dingding.robot.switch", Boolean.class, true);
            if (Boolean.FALSE.equals(dingSwitch)) {
                return;
            }
            String url = LoadPropertyUtil.getProperty("calculate.order.charge.dingding.robot.url", String.class,
                    "https://oapi.dingtalk.com/robot/send?access_token=49415c6cb2fe95f6275925f39d76e601b792525fdb02637fd4f37b4d90706156");
            ChatbotSendUtil.sendText(url, message);
        } catch (Exception e) {
            calculateLogger.error("CalculateService.sendMsgCount error message {} ", message, e);
        }
    }

    /**
     * 行程支付信息
     */
    public static CalculateOrderChargePayInfoEntity getPayInfoByCalculateOrderCharge(long rideId, String rideLineIds, String rideType) {
        try {
            String groupKey = StringUtils.equalsAny(rideType, CalculateRideTypeEnum.carpoolOrder.getType(), CalculateRideTypeEnum.carpoolMix.getType()) ? RedisKeyConstants.CARPOOL_CALCULATE_ORDER_CHARGE_PAY_INFO : RedisKeyConstants.TAXI_CALCULATE_ORDER_CHARGE_PAY_INFO;
            String key = rideId + rideLineIds;
            CalculateOrderChargePayInfoEntity entity = RedisClusterUtil.get(groupKey, key, CalculateOrderChargePayInfoEntity.class);
            if (entity != null) {
                entity.setParam();
            }
            return entity;
        } catch (Exception e) {
            calculateLogger.error("获取行程支付信息失败.rideId:{},rideLineIds:{},rideType:{}", rideId, rideLineIds, rideType, e);
            throw new DException(TradeErrorCode.REQUEST_ERROR);
        }
    }
}
