package com.didapinche.trade.application.service.support.traderefund;

import com.didapinche.pay.arch.common.retry.RetryUtils;
import com.didapinche.pay.service.thrift.TQueryRefundResponse;
import com.didapinche.pay.service.thrift.TRefundDetail;
import com.didapinche.pay.service.thrift.TRefundRequest;
import com.didapinche.trade.infrastructure.tbl.TradeRefundModel;
import com.didapinche.trade.thrift.enums.TOrderAccountTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class RefundCompensationService extends AbstractTradeRefundService {


    @Override
    protected void visitOneRefundStruct(RefundStruct refundStruct) {
        refundStruct.setAccountType(TOrderAccountTypeEnum.third.name());
    }

    @Override
    protected void executeOneRefund(TradeRefundModel tradeRefundModel) {
        TRefundRequest refundRequest = new TRefundRequest();
        refundRequest.setUserId(tradeRefundModel.getUserId().longValue());
        refundRequest.setPayTradeNo(String.valueOf(tradeRefundModel.getTradeOrderId()));
        String refundTradeNo = getRefundTradeNo(tradeRefundModel.getTradeOrderId());
        refundRequest.setRefundTradeNo(refundTradeNo);
        //单位：元
        refundRequest.setRefundAmount(tradeRefundModel.getAmount());
        RetryUtils.syncExecWithRetry(() -> payThriftClient.refund(refundRequest), 3, 100, TimeUnit.MILLISECONDS);
    }

    @Override
    protected boolean queryOneRefund(TradeRefundModel tradeRefundModel) {
        String payTradeNo = String.valueOf(tradeRefundModel.getTradeOrderId());
        Integer payUserId = tradeRefundModel.getUserId();
        TQueryRefundResponse tQueryRefundResponse = RetryUtils.syncExecWithRetryAndReturnValue(() -> payThriftClient.queryThirdRefundOrder(payTradeNo, payUserId, false), 3, 10, TimeUnit.MILLISECONDS);
        for (TRefundDetail refundDetail : tQueryRefundResponse.getRefundDetails()) {
            if (refundDetail.getRefundTradeNo().equals(getRefundTradeNo(tradeRefundModel.getTradeOrderId()))) {
                if (refundDetail.isStatus()) {
                    return true;
                }
            }
        }
        return false;
    }

    private String getRefundTradeNo(Long tradeOrderId) {
        return tradeOrderId + "-refund";
    }


}
