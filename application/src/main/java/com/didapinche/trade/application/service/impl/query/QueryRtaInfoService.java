package com.didapinche.trade.application.service.impl.query;

import com.didapinche.agaue.common.exception.DException;
import com.didapinche.server.commons.common.ddc.DdcHelper;
import com.didapinche.server.commons.common.ddc.DdcinfoEntity;
import com.didapinche.server.commons.common.util.LoadPropertyUtil;
import com.didapinche.trade.domin.payment.CouponDomainService;
import com.didapinche.trade.infrastructure.constants.AbTestConstants;
import com.didapinche.trade.infrastructure.constants.RtaConstants;
import com.didapinche.trade.infrastructure.constants.TradeConstants;
import com.didapinche.trade.infrastructure.entities.coupon.CouponInfoDO;
import com.didapinche.trade.infrastructure.entities.coupon.CouponResultDO;
import com.didapinche.trade.infrastructure.entities.coupon.CouponSetInfoDO;
import com.didapinche.trade.infrastructure.entities.coupon.DefaultCouponV2Request;
import com.didapinche.trade.infrastructure.exception.TradeErrorCode;
import com.didapinche.trade.infrastructure.thrift.BcaABTestDistributeThriftSupportService;
import com.didapinche.trade.infrastructure.thrift.PayQueryThriftSupportService;
import com.didapinche.trade.infrastructure.thrift.UserInfoThriftClient;
import com.didapinche.trade.infrastructure.thrift.bean.CouponDetailsDto;
import com.didapinche.trade.infrastructure.util.MatchUtil;
import com.didapinche.trade.thrift.entities.EcoPriceEntity;
import com.didapinche.trade.thrift.entities.QueryConfirmUserProfitRequest;
import com.didapinche.trade.thrift.entities.QueryUserProfitByRideInfoRequest;
import com.didapinche.trade.thrift.entities.QueryUserProfitByRideInfoResponse;
import com.didapinche.trade.thrift.entities.RtaInfoResult;
import com.didapinche.trade.thrift.enums.TOrderSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.AbstractMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class QueryRtaInfoService {

    private static final ThreadPoolExecutor workerThreadPool = new ThreadPoolExecutor(4, 4,
            1, TimeUnit.HOURS, new LinkedBlockingQueue<>(12), new ThreadPoolExecutor.CallerRunsPolicy());

    private static final String ALI_EXPOSE_PRIORITY_0 = "首单";

    @Autowired
    private CouponDomainService couponDomainService;
    @Autowired
    private PayQueryThriftSupportService payQueryThriftSupportService;
    @Autowired
    private BcaABTestDistributeThriftSupportService bcaABService;
    @Autowired
    private UserInfoThriftClient userInfoThriftClient;

    /**
     * 首页及收银台查询用户权益
     * @param request 请求参数
     * @return 响应
     */
    public RtaInfoResult queryUserProfit(QueryConfirmUserProfitRequest request) {
        String userCid = request.getUserCid();
        String deviceType = request.getDeviceType();
        String deviceId = request.getDeviceId();
        String identifier = request.getIdentifier();
        int querySource = request.getQuerySource();
        String thirdPrice = request.getThirdPrice();
        //首页进来
        if (querySource == RtaConstants.QuerySource.S1.getSource()) {
            Integer userId = userInfoThriftClient.getUserIdByCid(userCid);
            return this.dealWhenQuerySourceOne(userId);
        }
        //收银台进来
        else if (querySource == RtaConstants.QuerySource.S3.getSource()) {
            return this.dealWhenQuerySourceThree(userCid, deviceType, deviceId, identifier, thirdPrice, querySource);
        }
        return new RtaInfoResult();
    }


    /**
     * 首页
     */
    private RtaInfoResult dealWhenQuerySourceOne(Integer userId) {
        RtaInfoResult result = new RtaInfoResult();
        com.didapinche.trade.thrift.entities.BankInfo tradeBankInfo = new com.didapinche.trade.thrift.entities.BankInfo();
        CouponDetailsDto couponUserGuide = couponDomainService.getCouponUserGuide(userId);
        //没有内部优惠券
        if (StringUtils.isEmpty(couponUserGuide.getCarpoolRet())) {
            result.setBankInfo(tradeBankInfo);
            return result;
        }
        result.setCarpoolCouponDealsContent(couponUserGuide.getCarpoolRet());
        result.setCarpoolDeductionType(couponUserGuide.getCarpoolDeductionType());
        result.setBankInfo(tradeBankInfo);
        return result;
    }

    private RtaInfoResult dealWhenQuerySourceTwo(String userCid, String deviceType, String deviceId, String identifier, Integer querySource) {

        RtaInfoResult result = new RtaInfoResult();
        com.didapinche.trade.thrift.entities.BankInfo tradeBankInfo = new com.didapinche.trade.thrift.entities.BankInfo();

        if (StringUtils.isNotEmpty(deviceId) && StringUtils.isNotEmpty(identifier)) {
            List<String> group = bcaABService.getHitExpGroupByUserId(userCid, AbTestConstants.RTA_CONTROL_AB, AbTestConstants.AB_SYS, identifier, AbTestConstants.RTA_USER_ROLE_PASSENGER);
            if (!CollectionUtils.isEmpty(group) && group.contains(AbTestConstants.RTA_TEST) && RtaConstants.QueryRtaSwitchOpen.P2.getOpenValue().equals(LoadPropertyUtil.getProperty(RtaConstants.QueryRtaSwitchOpen.P2.getSwitchKey()))) {
                com.didapinche.pay.service.query.thrift.RtaInfoResult payRtaInfo = payQueryThriftSupportService.queryRtaInfoByDeviceInfo(userCid, deviceType, deviceId, TradeConstants.createUUid(), querySource);
                if (payRtaInfo != null && payRtaInfo.getBankInfo() != null) {
                    tradeBankInfo.setOverAmount(payRtaInfo.getBankInfo().getOverAmount());
                    tradeBankInfo.setReductionAmount(payRtaInfo.getBankInfo().getReductionAmount());
                    tradeBankInfo.setExposeText(payRtaInfo.getBankInfo().getExposeText());
                }
            }
        }
        result.setBankInfo(tradeBankInfo);
        return result;
    }

    private RtaInfoResult dealWhenQuerySourceThree(String userCid, String deviceType, String deviceId, String identifier, String thirdPrice, Integer querySource) {

        RtaInfoResult result = new RtaInfoResult();
        com.didapinche.trade.thrift.entities.BankInfo tradeBankInfo = new com.didapinche.trade.thrift.entities.BankInfo();

        List<String> group = bcaABService.getHitExpGroupByUserId(userCid, AbTestConstants.RTA_CONTROL_AB, AbTestConstants.AB_SYS, identifier, AbTestConstants.RTA_USER_ROLE_PASSENGER);
        if (!CollectionUtils.isEmpty(group) && group.contains(AbTestConstants.RTA_TEST) && RtaConstants.QueryRtaSwitchOpen.P3.getOpenValue().equals(LoadPropertyUtil.getProperty(RtaConstants.QueryRtaSwitchOpen.P3.getSwitchKey()))) {
            com.didapinche.pay.service.query.thrift.RtaInfoResult payRtaInfo = payQueryThriftSupportService.queryRtaInfoByDeviceInfo(userCid, deviceType, deviceId, TradeConstants.createUUid(), querySource);
            if (payRtaInfo != null && payRtaInfo.getBankInfo() != null) {
                BigDecimal aliOverAmount = new BigDecimal(payRtaInfo.getBankInfo().getOverAmount());
                BigDecimal thirdPriceBigDecimal = new BigDecimal(thirdPrice);
                if (thirdPriceBigDecimal.compareTo(aliOverAmount) >= 0) {
                    String reductionAmount = payRtaInfo.getBankInfo().getReductionAmount();
                    tradeBankInfo.setExposeText("立减" + reductionAmount + "元");
                    tradeBankInfo.setReductionAmount(reductionAmount);
                }
            }
        }
        result.setBankInfo(tradeBankInfo);
        return result;
    }

    /**
     * 询价页查询优惠
     * @param request 请求参数
     * @return 响应
     */
    public QueryUserProfitByRideInfoResponse queryUserProfitByRideInfo(QueryUserProfitByRideInfoRequest request) {

        String suggestPrice = request.getSuggestPrice();
        String multiPrice = request.getMultiPrice();
        String cosyFen = request.getCosyFen();
        String singlePrice = request.getSinglePrice();
        String ecoSuggestPrice = request.getEcoSuggestPrice();
        String ecoMultiPrice = request.getEcoMultiPrice();
        String ecoCosyFen = request.getEcoCosyFen();
        String ecoSinglePrice = request.getEcoSinglePrice();
        String groupFen = request.getGroupFen();
        String deviceType = request.getDeviceType();
        String deviceId = request.getDeviceId();
        String ip = request.getIp();
        String userCid = request.getUserCid();
        String distanceType = request.getDistanceType();
        //单位：元
        String stationPrice = request.getStationPrice();

        if(StringUtils.isEmpty(userCid) || StringUtils.isEmpty(suggestPrice)) {
            throw new DException(TradeErrorCode.QUERY_PARAMS_CHECK);
        }

        Map<String, String> priceMap = new HashMap<>();
        priceMap.put(RtaConstants.SUGGEST_PRICE, suggestPrice);
        //计算优惠券时，拼成价的优惠券也根据未拼成价计算
        priceMap.put(RtaConstants.MULTI_PRICE, suggestPrice);
        BigDecimal cosyPriceDecimal = StringUtils.isEmpty(cosyFen) ? null : new BigDecimal(cosyFen).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
        priceMap.put(RtaConstants.COSY_FEN, cosyPriceDecimal == null ? null : cosyPriceDecimal.toString());
        priceMap.put(RtaConstants.SINGLE_PRICE, singlePrice);
        priceMap.put(RtaConstants.ECO_SUGGEST_PRICE, ecoSuggestPrice);
        priceMap.put(RtaConstants.ECO_MULTI_PRICE, ecoMultiPrice);
        BigDecimal ecoCosyFenBigDecimal = StringUtils.isEmpty(ecoCosyFen) ? null : new BigDecimal(ecoCosyFen).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
        priceMap.put(RtaConstants.ECO_COSY_FEN, ecoCosyFenBigDecimal == null ? null : ecoCosyFenBigDecimal.toString());
        priceMap.put(RtaConstants.ECO_SINGLE_PRICE, ecoSinglePrice);
        BigDecimal groupFenBigDecimal = StringUtils.isEmpty(groupFen) ? null : new BigDecimal(groupFen).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
        priceMap.put(RtaConstants.GROUP_FEN, groupFenBigDecimal == null ? null : groupFenBigDecimal.toString());
        priceMap.put(RtaConstants.STATION_PRICE, stationPrice);

        Integer userIdByCid = userInfoThriftClient.getUserIdByCid(userCid);
        //查询内部优惠券
        List<CompletableFuture<AbstractMap.SimpleEntry<String, CouponResultDO>>> completableFutureList = priceMap.entrySet()
                .stream()
                .filter(entry -> StringUtils.isNotEmpty(entry.getValue()))
                .map(entry -> {
                    DefaultCouponV2Request couponV2Request = new DefaultCouponV2Request();
                    couponV2Request.setPassengerId(String.valueOf(userIdByCid));
                    couponV2Request.setIp(ip);
                    couponV2Request.setPlanStartTime(request.getPlanStartTime());
                    couponV2Request.setOnLon(request.getStartLongitude());
                    couponV2Request.setOnLat(request.getStartLatitude());
                    couponV2Request.setOffLon(request.getEndLongitude());
                    couponV2Request.setOffLat(request.getEndLatitude());
                    couponV2Request.setRideType(request.getInnerRideType());
                    couponV2Request.setTicketPrice(entry.getValue());
                    CompletableFuture<CouponResultDO> future = this.asyncGetCouponResult(couponV2Request);
                    if (future == null) {
                        return null;
                    }
                    return future.thenApply(result -> new AbstractMap.SimpleEntry<>(entry.getKey(), result));
                })
                .collect(Collectors.toList());

        QueryUserProfitByRideInfoResponse result = new QueryUserProfitByRideInfoResponse();
        Map<String, CouponResultDO> calResultMap = new HashMap<>();
        for (CompletableFuture<AbstractMap.SimpleEntry<String, CouponResultDO>> completableFuture : completableFutureList) {
            if (completableFuture == null) {
                continue;
            }
            AbstractMap.SimpleEntry<String, CouponResultDO> entry = null;
            try {
                entry = completableFuture.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("completableFuture get异常");
            }
            if (entry == null) {
                continue;
            }
            calResultMap.put(entry.getKey(), entry.getValue());
        }
        //查询外部优惠权益
        RtaInfoResult rtaInfo = this.dealWhenQuerySourceTwo(userCid, deviceType, deviceId, request.getIdentifier(), RtaConstants.QuerySource.S2.getSource());
        result.setOverAmount(rtaInfo.getBankInfo().getOverAmount());
        result.setDeductionAmount(rtaInfo.getBankInfo().getReductionAmount());

        //组装响应结构体
        this.genResponseByMap(distanceType, priceMap, calResultMap, result);
        return result;
    }


    private void genResponseByMap(String distanceType, Map<String, String> priceMap, Map<String, CouponResultDO> calResultMap, QueryUserProfitByRideInfoResponse targetResult) {
        EcoPriceEntity ecoPriceEntity = new EcoPriceEntity();

        for (Map.Entry<String, String> entry : priceMap.entrySet()) {
            if (StringUtils.isEmpty(entry.getValue())) {
                continue;
            }
            // 获取键值对的键和值
            String key = entry.getKey();
            BigDecimal price = new BigDecimal(entry.getValue());
            CouponResultDO couponResultDO = calResultMap.get(key);
            float couponPrice = 0.0f;

            if (couponResultDO != null) {
                CouponInfoDO couponInfo = couponResultDO.getData();
                if (couponInfo != null) {
                    CouponSetInfoDO couponSetInfo = couponInfo.getCouponSetInfo();
                    if (couponSetInfo != null) {
                        BigDecimal needPay;
                        if (RtaConstants.DistanceType.P1.getCode().equals(distanceType)) {
                            couponPrice = Float.parseFloat(new BigDecimal(couponInfo.getReal_unit_price()).stripTrailingZeros().toPlainString());
                        } else if (RtaConstants.DistanceType.P2.getCode().equals(distanceType)) {
                            //2 折扣券；1 立减券
                            if ("2".equals(couponSetInfo.getCoupon_type())) {
                                needPay = caleDiscountCouponPrice(price, new BigDecimal(couponSetInfo.getDiscount()).setScale(2, RoundingMode.HALF_UP), new BigDecimal(couponSetInfo.getUnit_price()));
                                couponPrice = Float.parseFloat(price.subtract(needPay).stripTrailingZeros().toPlainString());
                            } else {
                                couponPrice = Float.parseFloat(new BigDecimal(couponInfo.getPrice()).stripTrailingZeros().toPlainString());
                            }
                        }
                    }
                }
            }


            if (RtaConstants.SUGGEST_PRICE.equals(key)) {
                targetResult.setCouponPrice(String.valueOf(couponPrice));
            } else if (RtaConstants.MULTI_PRICE.equals(key)) {
                targetResult.setCouponPriceMulti(String.valueOf(couponPrice));
            } else if (RtaConstants.COSY_FEN.equals(key)) {
                targetResult.setCouponPriceCosy(String.valueOf(couponPrice));
            }  else if (RtaConstants.SINGLE_PRICE.equals(key)) {
                targetResult.setCouponPriceSingle(String.valueOf(couponPrice));
            } else if (RtaConstants.GROUP_FEN.equals(key)) {
                targetResult.setCouponPriceGroup(String.valueOf(couponPrice));
            } else if(RtaConstants.STATION_PRICE.equals(key)){
                targetResult.setCouponPriceStation(String.valueOf(couponPrice));
            } else {
                BigDecimal obj = this.calCouponPrice(couponResultDO, price);
                if (obj != null) {
                    if (RtaConstants.ECO_SUGGEST_PRICE.equals(key)) {
                        ecoPriceEntity.setCouponPrice(String.valueOf(obj));
                    } else if (RtaConstants.ECO_MULTI_PRICE.equals(key)) {
                        ecoPriceEntity.setCouponPriceMulti(String.valueOf(obj));
                    } else if (RtaConstants.ECO_COSY_FEN.equals(key)) {
                        ecoPriceEntity.setCouponPriceCosy(String.valueOf(obj));
                    } else if (RtaConstants.ECO_SINGLE_PRICE.equals(key)) {
                        ecoPriceEntity.setCouponPriceSingle(String.valueOf(obj));
                    }
                }
            }
        }
        targetResult.setEcoPriceEntity(ecoPriceEntity);
    }

    /**
     * 电车计算
     * @param couponResult 优惠券返回结果
     * @param ticketPrice 建议价格
     * @return
     */
    private BigDecimal calCouponPrice(CouponResultDO couponResult, BigDecimal ticketPrice) {
        if(couponResult == null){
            return null;
        }
        if(ticketPrice == null){
            return null;
        }
        CouponInfoDO data = couponResult.getData();
        if(data == null){
            return null;
        }
        CouponSetInfoDO couponSetInfo = data.getCouponSetInfo();
        if(couponSetInfo == null){
            return null;
        }
        BigDecimal credit = this.calPrice(Integer.parseInt(couponSetInfo.getCoupon_type()),
                new BigDecimal(couponSetInfo.getDiscount()), new BigDecimal(data.getPrice()), ticketPrice);
        return credit;
    }

    /**
     * 优惠券优惠价格计算
     * @param couponType 优惠券类型
     * @param discount 折扣 （折扣券）
     * @param unitPrice 面值 （现金券）
     * @param ticketPrice 行程价
     * @return
     */
    public BigDecimal calPrice(int couponType, BigDecimal discount, BigDecimal unitPrice,  BigDecimal ticketPrice) {
        BigDecimal couponPrice;
        if (couponType == 2) {    //计算折扣券价格
            couponPrice = (new BigDecimal(1).subtract(discount)).multiply(ticketPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
            if (unitPrice.compareTo(new BigDecimal(0)) > 0) {
                if (couponPrice.compareTo(unitPrice) > 0) {
                    couponPrice = unitPrice;
                }
            }
        } else { // 现金券
            couponPrice = unitPrice;
        }
        return couponPrice;
    }

    private BigDecimal caleDiscountCouponPrice(BigDecimal old_price, BigDecimal discount, BigDecimal max_reduce) {
        BigDecimal couponPrice = discount.multiply(old_price); //.setScale(0, BigDecimal.ROUND_HALF_UP); //打折后支付价格
        BigDecimal reduce = old_price.subtract(couponPrice); //抵扣价格
        if (reduce.compareTo(max_reduce) == 1) {
            couponPrice = couponPrice.add(reduce.subtract(max_reduce));
        }
        return couponPrice.setScale(1, RoundingMode.HALF_UP);
    }

    public CompletableFuture<CouponResultDO> asyncGetCouponResult(DefaultCouponV2Request request) {
        if(request.getTicketPrice() == null){
            return CompletableFuture.completedFuture(null);
        }
        DdcinfoEntity currentDdcInfo = DdcHelper.getCurrentDdcInfo();
        if (currentDdcInfo != null && "dingtalk_carpool.mini".equals(currentDdcInfo.getIdentifier())) {
            request.setSourceCid(TOrderSourceEnum.dingtalk_carpool.name());
        } else {
            request.setSourceCid(TOrderSourceEnum.didachuxing_app.name());
        }
        return CompletableFuture.supplyAsync(() -> couponDomainService.getDefaultCouponV2(request), workerThreadPool);
    }

}
