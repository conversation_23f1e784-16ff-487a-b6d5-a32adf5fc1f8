package com.didapinche.trade.application.service.impl.tencent;

import com.didapinche.agaue.common.exception.DException;
import com.didapinche.agaue.tools.convert.AmountConvertUtil;
import com.didapinche.finance.mq.context.enums.TheaExtraInfoKeyEnum;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.server.commons.common.util.DateUtil;
import com.didapinche.trade.application.service.impl.query.AbstractQueryService;
import com.didapinche.trade.domin.DO.tencent.TencentDO;
import com.didapinche.trade.domin.tencent.dto.TencentBillInfoDTO;
import com.didapinche.trade.domin.tencent.service.TencentBillNotifyTheaService;
import com.didapinche.trade.domin.thea.message.PauCutOrRefundTheaMessage;
import com.didapinche.trade.infrastructure.entities.TradeOrderNote;
import com.didapinche.trade.infrastructure.enums.PauBusinessTypeEnum;
import com.didapinche.trade.infrastructure.enums.PaymentPauCutFlageEnum;
import com.didapinche.trade.infrastructure.enums.order.TradeOrderStatusEnum;
import com.didapinche.trade.infrastructure.exception.TradeErrorCode;
import com.didapinche.trade.infrastructure.tbl.TradeOrderModel;
import com.didapinche.trade.thrift.entities.TencentBillRequest;
import com.didapinche.trade.thrift.entities.TencentBillResponse;
import com.didapinche.trade.thrift.entities.TencentPauOrderNotifyRequest;
import com.didapinche.trade.thrift.entities.TencentPauOrderNotifyResponse;
import com.didapinche.trade.thrift.enums.TChannelEnum;
import com.didapinche.trade.thrift.enums.TPaymentWayEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 外输支付/退款通知
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TencentBillNotifyService extends AbstractQueryService<TencentBillRequest> {


    @Autowired
    private TencentBillNotifyTheaService tencentBillNotifyTheaService;

    @Override
    public TencentBillResponse doTrade(TencentBillRequest request) {
        TencentDO tencentDO = new TencentDO();
        tencentDO.setRequestId(request.getRequestId());
        tencentDO.setUserId(request.getUserId());
        tencentDO.setPayTradeNo(request.getPayTradeNo());
        tencentDO.setRefundSource(request.getRefundSource());
        tencentDO.setTradeType(request.getTradeType());
        List<TencentBillInfoDTO> tencentBillInfoDTOS = JsonMapper.json2List(request.getBillInfo(), TencentBillInfoDTO.class);
        tencentDO.setTencentBillInfoDTOS(tencentBillInfoDTOS);
        tencentDO.setTransactionId(request.getTransactionId());
        tencentBillNotifyTheaService.theaSend(tencentDO);
        return new TencentBillResponse().setCode(0).setMessage("success");
    }

    public TencentPauOrderNotifyResponse tencentPauOrderNotify(TencentPauOrderNotifyRequest request) {
        TencentPauOrderNotifyResponse response = new TencentPauOrderNotifyResponse();
        TradeOrderModel tradeOrderModel = pauNotifyQueryOrder(request);
        TradeOrderNote tradeOrderNote = TradeOrderNote.create(tradeOrderModel.getNote());
        //1.发起扣款
        String notifyType = request.getNotifyType();
        if ("1".equals(notifyType)) {
            return processingCutNotify(request, tradeOrderModel, tradeOrderNote);
        }
        //2.取消扣款通知
        if ("2".equals(notifyType)) {
            return finishCutNotify(request, tradeOrderModel, tradeOrderNote);
        }
        return response.setCode(TradeErrorCode.PARAM_ERROR.getCode()).setMessage(TradeErrorCode.PARAM_ERROR.getMsg());
    }

    private TencentPauOrderNotifyResponse finishCutNotify(TencentPauOrderNotifyRequest request, TradeOrderModel order, TradeOrderNote note) {
        String pauCutFlag = note.getPauCutFlag();
        TencentPauOrderNotifyResponse response = new TencentPauOrderNotifyResponse();
        if (StringUtils.equals(pauCutFlag, PaymentPauCutFlageEnum.success.name())) {
            log.warn("finishCutNotify. 已扣款成功，无法终止");
            throw new DException(TradeErrorCode.PAU_ORDER_STATUS_ERROR);
        }
        if (StringUtils.equals(pauCutFlag, PaymentPauCutFlageEnum.finish.name())) {
            log.info("finishCutNotify. 状态已改为终止");
            return response;
        }
        Date notifyDate = null;
        try {
            notifyDate = DateUtil.String2Date3(request.getNotifyTime());
        } catch (Exception e) {
            //ignore
        }
        //可改为扣款终止状态
        if (StringUtils.equalsAny(pauCutFlag, PaymentPauCutFlageEnum.create.name(), PaymentPauCutFlageEnum.processing.name(), PaymentPauCutFlageEnum.fail.name())) {
            TradeOrderModel updateOrder = new TradeOrderModel();
            updateOrder.setId(order.getId());
            note.setPauCutFlag(PaymentPauCutFlageEnum.finish.name());
            String time = null;
            Date payLaunchTime = null;
            try {
                time = DateUtil.date2String(notifyDate);
                payLaunchTime = note.getPauStartCutTime() != null ? DateUtil.String2Date3(note.getPauStartCutTime()) : new Date();
            } catch (Exception e) {
                //ignore
            }
            note.setPauFinishTime(time);
            updateOrder.setNote(note.toJson());
            tradeOrderModelService.updateById(updateOrder);
            //更新amount记录
            tradeAmountDetailsModelService.updatePauTencentAmountCancel(order.getId(), order.getUserId(), payLaunchTime, notifyDate);
        }
        order = tradeOrderModelService.selectById(order.getId(), order.getUserId());
        //发thea
        theaPau(order, BigDecimal.ZERO, true, notifyDate);
        return response;
    }

    private TencentPauOrderNotifyResponse processingCutNotify(TencentPauOrderNotifyRequest request, TradeOrderModel order, TradeOrderNote note) {
        String pauCutFlag = note.getPauCutFlag();
        TencentPauOrderNotifyResponse response = new TencentPauOrderNotifyResponse();
        if (PaymentPauCutFlageEnum.processing.name().equals(pauCutFlag)) {
            return response;
        }
        Date notifyDate = null;
        try {
            notifyDate = DateUtil.String2Date3(request.getNotifyTime());
        } catch (Exception e) {
            //ignore
        }
        //只有create可改为发起扣款状态
        if (StringUtils.equals(pauCutFlag, PaymentPauCutFlageEnum.create.name())) {
            TradeOrderModel updateOrder = new TradeOrderModel();
            updateOrder.setId(order.getId());
            note.setPauCutFlag(PaymentPauCutFlageEnum.processing.name());
            String time = null;
            try {
                time = DateUtil.date2String(notifyDate);
            } catch (Exception e) {
                //ignore
            }
            note.setPauStartCutTime(time);
            note.setPauCutAmount(request.getCutAmount());
            updateOrder.setNote(note.toJson());
            tradeOrderModelService.updateById(updateOrder);
            tradeAmountDetailsModelService.updateTencentAmountLaunchTime(order.getId(), order.getUserId(), notifyDate, AmountConvertUtil.yuan2BigDecimal(request.getCutAmount()));
        }
        order = tradeOrderModelService.selectById(order.getId(), order.getUserId());
        //发thea
        theaPau(order, AmountConvertUtil.yuan2BigDecimal(note.getPauCutAmount()), false, notifyDate);
        return response;
    }

    public void theaPau(TradeOrderModel tradeOrderModel, BigDecimal thirdAmount, boolean finish, Date date) {
        PauCutOrRefundTheaMessage theaMessage = new PauCutOrRefundTheaMessage();
        theaMessage.setTradeOrder(tradeOrderModel)
                .setPauBusinessType(finish ? PauBusinessTypeEnum.pau_cancel.name() : PauBusinessTypeEnum.pau_deducted.name())
                .setAfterTrade(theaMessageContext -> {
                    theaMessageContext.setOutTradeNo(String.valueOf(tradeOrderModel.getId()));
                    theaMessageContext.setTradeNo(tradeOrderModel.getId() + "");
                    theaMessageContext.setOrderPrice(thirdAmount);
                    theaMessageContext.setThirdPay(thirdAmount);
                    theaMessageContext.setPayTime(date);
                    theaMessageContext.setPayChannleStr(TChannelEnum.weixin.name());
                    theaMessageContext.setPayType("ten_wx_pau");
                    Map<String, Object> financeInfo = theaMessageContext.getFinanceInfo();
                    if (CollectionUtils.isEmpty(financeInfo)) {
                        financeInfo = new HashMap<>();
                    }
                    financeInfo.put(TheaExtraInfoKeyEnum.ride_id.name(), tradeOrderModel.getMajorProductId());
                    theaMessageContext.setFinanceInfo(financeInfo);
                }).send();
    }

    private TradeOrderModel pauNotifyQueryOrder(TencentPauOrderNotifyRequest request) {
        if (request.getUserId() <= 0 || request.getPayTradeNo() <= 0 || StringUtils.isAnyBlank(request.getNotifyType(), request.getNotifyTime())) {
            throw new DException(TradeErrorCode.PARAM_ERROR);
        }
        TradeOrderModel tradeOrderModel = tradeOrderModelService.selectById(request.getPayTradeNo(), request.getUserId());
        if (tradeOrderModel == null) {
            throw new DException(TradeErrorCode.NOT_FUND_PAY_ORDER_ERROR);
        }
        if (!TradeOrderStatusEnum.success.name().equals(tradeOrderModel.getStatus())) {
            throw new DException(TradeErrorCode.NO_PAY_ERROR);
        }
        TradeOrderNote tradeOrderNote = TradeOrderNote.create(tradeOrderModel.getNote());
        if (tradeOrderNote == null || !TPaymentWayEnum.tencent_weixin_pau.name().equals(tradeOrderNote.getPaymentWay())) {
            throw new DException(TradeErrorCode.NOT_PAU_PAYMENT_ORDER_ERROR);
        }
        return tradeOrderModel;
    }
}
