package com.didapinche.trade.application.service.support.traderefund;

import lombok.Data;

@Data
public class TradeResult {
    private int code;
    private String message;

    public static TradeResult create(int code, String message) {
        TradeResult result = new TradeResult();
        result.setCode(code);
        result.setMessage(message);
        return result;
    }

    public static TradeResult createSuccess() {
        TradeResult result = new TradeResult();
        result.setCode(0);
        result.setMessage("success");
        return result;
    }

    public boolean isSuccess() {
        return code == 0;
    }
}
