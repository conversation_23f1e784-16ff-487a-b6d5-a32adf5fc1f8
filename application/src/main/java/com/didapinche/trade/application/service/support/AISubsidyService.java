package com.didapinche.trade.application.service.support;

import com.didapinche.server.commons.common.ddc.DdcinfoEntity;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.server.commons.common.util.UserInfoUtil;
import com.didapinche.thrift.api.Location;
import com.didapinche.thrift.api.OrderSubsidyRecRequest;
import com.didapinche.thrift.api.OrderSubsidyRecResult;
import com.didapinche.thrift.api.Price;
import com.didapinche.trade.application.service.impl.query.QueryRtaInfoService;
import com.didapinche.trade.infrastructure.thrift.OrderSubsidyServiceSupportService;
import com.didapinche.trade.infrastructure.util.CurrencyUtil;
import com.didapinche.trade.thrift.entities.PriceRequest;
import com.didapinche.trade.thrift.entities.QuerySubsidyFromAIRequest;
import com.didapinche.trade.thrift.entities.QuerySubsidyFromAIResponse;
import com.didapinche.trade.thrift.entities.QueryUserProfitByRideInfoRequest;
import com.didapinche.trade.thrift.entities.QueryUserProfitByRideInfoResponse;
import com.didapinche.trade.thrift.entities.SubsidyResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class AISubsidyService {

    private final static Logger aiLogger = LoggerFactory.getLogger("aiRequestLogger");

    @Autowired
    private QueryRtaInfoService queryRtaInfoService;

    @Autowired
    private OrderSubsidyServiceSupportService orderSubsidyServiceSupportService;

    /**
     * 工具类
     *
     * @param request
     * @param priceType
     * @return
     */
    public Optional<PriceRequest> getPriceFen(QuerySubsidyFromAIRequest request, String priceType) {
        return request.getPriceRequest().stream()
                .filter(priceRequest -> priceRequest.getPriceType().equals(priceType))
                .findFirst();
    }

    /**
     * 工具类
     *
     * @param response
     * @param priceType
     * @return
     */
    private Optional<SubsidyResult> getSubsidyPriceFen(QuerySubsidyFromAIResponse response, String priceType) {
        return response.getSubsidyResults().stream()
                .filter(subsidyInfo -> subsidyInfo.getPriceType().equals(priceType))
                .findFirst();
    }

    /**
     * 工具类
     * <p>
     * 从结果集中提取指定项的补贴金额，如果补贴值不存在，默认为0。
     *
     * @param subsidyFromAIResponse
     * @param priceTypeEnum
     * @return 补贴金额，单位：分
     */
    public Integer extractSubsidyPriceFen(QuerySubsidyFromAIResponse subsidyFromAIResponse, PriceTypeEnum priceTypeEnum) {
        return getSubsidyPriceFen(subsidyFromAIResponse, priceTypeEnum.getType())
                .map(subsidyResult -> {
                    if (StringUtils.isNotBlank(subsidyResult.getSubsidyPrice())) {
                        return Integer.parseInt(subsidyResult.getSubsidyPrice());
                    }
                    return 0;
                }).orElse(0);
    }


    /**
     * 获取AI补贴，thrift用
     * 金额单位是分
     *
     * @param request
     * @return
     */
    public QuerySubsidyFromAIResponse getSubsidyFromAI(QuerySubsidyFromAIRequest request) {
        aiLogger.info("getSubsidyFromAI request={}", JsonMapper.toJson(request));
        PriceRequest noPrice = new PriceRequest();
        noPrice.setOriginPrice(null);
        noPrice.setCouponPrice(null);
        noPrice.setPriceType(null);
        //单位都是分
        String suggest_price = getPriceFen(request, PriceTypeEnum.suggest_price.getType()).orElse(noPrice).getOriginPrice();
        String multi_price = getPriceFen(request, PriceTypeEnum.multi_price.getType()).orElse(noPrice).getOriginPrice();
        String cosy_price = getPriceFen(request, PriceTypeEnum.cosy_price.getType()).orElse(noPrice).getOriginPrice();
        String single_price = getPriceFen(request, PriceTypeEnum.single_price.getType()).orElse(noPrice).getOriginPrice();
        String group_fen = getPriceFen(request, PriceTypeEnum.group_fen.getType()).orElse(noPrice).getOriginPrice();
        String eco_suggest_price = getPriceFen(request, PriceTypeEnum.eco_suggest_price.getType()).orElse(noPrice).getOriginPrice();
        String eco_multi_price = getPriceFen(request, PriceTypeEnum.eco_multi_price.getType()).orElse(noPrice).getOriginPrice();
        String eco_cosy_price = getPriceFen(request, PriceTypeEnum.eco_cosy_price.getType()).orElse(noPrice).getOriginPrice();
        String eco_single_price = getPriceFen(request, PriceTypeEnum.eco_single_price.getType()).orElse(noPrice).getOriginPrice();


        QueryUserProfitByRideInfoRequest profitByRideInfoRequest = new QueryUserProfitByRideInfoRequest();
        String userCid = UserInfoUtil.getUsercidById(request.getUserId());
        profitByRideInfoRequest.setUserCid(userCid);
        String suggestPrice = CurrencyUtil.fen2YuanMaybeNull(suggest_price);
        //queryRtaInfoService 对 suggestPrice进行了校验，这里默认为0
        profitByRideInfoRequest.setSuggestPrice(StringUtils.defaultIfBlank(suggestPrice, "0"));
        profitByRideInfoRequest.setMultiPrice(CurrencyUtil.fen2YuanMaybeNull(multi_price));
        //这个单位是:分，其他单位是：元
        profitByRideInfoRequest.setCosyFen(cosy_price);
        profitByRideInfoRequest.setSinglePrice(CurrencyUtil.fen2YuanMaybeNull(single_price));
        profitByRideInfoRequest.setGroupFen(group_fen);
        profitByRideInfoRequest.setEcoSuggestPrice(CurrencyUtil.fen2YuanMaybeNull(eco_suggest_price));
        profitByRideInfoRequest.setEcoMultiPrice(CurrencyUtil.fen2YuanMaybeNull(eco_multi_price));
        //这个单位是:分，其他单位是：元
        profitByRideInfoRequest.setEcoCosyFen(eco_cosy_price);
        profitByRideInfoRequest.setEcoSinglePrice(CurrencyUtil.fen2YuanMaybeNull(eco_single_price));
        profitByRideInfoRequest.setDeviceType(null);
        profitByRideInfoRequest.setIp(request.getIp());
        profitByRideInfoRequest.setStartLongitude(request.getStartLongitude());
        profitByRideInfoRequest.setStartLatitude(request.getStartLatitude());
        profitByRideInfoRequest.setEndLongitude(request.getEndLongitude());
        profitByRideInfoRequest.setEndLatitude(request.getEndLatitude());
        profitByRideInfoRequest.setPlanStartTime(request.getPlanStartTime());
        profitByRideInfoRequest.setInnerRideType(request.getRideType());
        profitByRideInfoRequest.setDistanceType(request.getCityType());

        aiLogger.debug("getSubsidyFromAI coupon request={}", JsonMapper.toJson(profitByRideInfoRequest));
        QueryUserProfitByRideInfoResponse queryUserProfitByRideInfoResponse = queryRtaInfoService.queryUserProfitByRideInfo(profitByRideInfoRequest);
        aiLogger.debug("getSubsidyFromAI coupon response={}", JsonMapper.toJson(queryUserProfitByRideInfoResponse));

        //组装AI请求参数
        OrderSubsidyRecRequest aiRequest = createAIRequestForThrift(request.getUserId(), userCid, request, queryUserProfitByRideInfoResponse);
        //请求AI
        OrderSubsidyRecResult orderSubsidyRecResult = orderSubsidyServiceSupportService.orderSubsidyRecWithRetry(aiRequest);
        //转换AI结果
        QuerySubsidyFromAIResponse response = transformAIResult(orderSubsidyRecResult);
        aiLogger.info("getSubsidyFromAI response={}", JsonMapper.toJson(response));
        return response;
    }

    private static QuerySubsidyFromAIResponse transformAIResult(OrderSubsidyRecResult orderSubsidyRecResult) {
        QuerySubsidyFromAIResponse response = new QuerySubsidyFromAIResponse();
        response.setCode(orderSubsidyRecResult.getCode());
        response.setMessage(orderSubsidyRecResult.getMsg());
        response.setSubsidyResults(orderSubsidyRecResult.getSubsidyInfos().stream().map(subsidyInfo -> {
            SubsidyResult subsidyResult = new SubsidyResult();
            subsidyResult.setPriceType(subsidyInfo.getPrice_type());
            subsidyResult.setSubsidyPrice(CurrencyUtil.formatFen(new BigDecimal(subsidyInfo.getSubsidy_price())));
            return subsidyResult;
        }).collect(Collectors.toList()));
        return response;
    }

    /**
     * 请求AI，并做结果转换
     *
     * @param aiRequest
     * @return
     */
    public QuerySubsidyFromAIResponse requestSubsidyFromAI(OrderSubsidyRecRequest aiRequest) {
        //请求AI
        OrderSubsidyRecResult orderSubsidyRecResult = orderSubsidyServiceSupportService.orderSubsidyRecWithRetry(aiRequest);
        //转换AI结果
        QuerySubsidyFromAIResponse response = transformAIResult(orderSubsidyRecResult);

        aiLogger.info("requestSubsidyFromAI userId={} userCid={} response={}", aiRequest.getUser_id(), aiRequest.getUser_cid(), JsonMapper.toJson(response));
        return response;
    }

    /**
     * 为RS接口组装AI参数
     *
     * @param userCid
     * @param startLongitude
     * @param startLatitude
     * @param endLongitude
     * @param endLatitude
     * @param planStartTime
     * @param suggestPrice    单位：元
     * @param multiPrice      单位：元
     * @param cosyFen         单位：分
     * @param singlePrice     单位：元
     * @param ecoSuggestPrice 单位：元
     * @param ecoMultiPrice   单位：元
     * @param ecoCosyFen      单位：分
     * @param ecoSinglePrice  单位：元
     * @param currentDdcInfo
     * @param resp
     * @return
     */
    public OrderSubsidyRecRequest createAIRequestForRS(String userCid, String startLongitude, String startLatitude,
                                                       String endLongitude, String endLatitude, String planStartTime,
                                                       String suggestPrice, String multiPrice, String cosyFen, String singlePrice,
                                                       String ecoSuggestPrice, String ecoMultiPrice, String ecoCosyFen, String ecoSinglePrice,
                                                       DdcinfoEntity currentDdcInfo, QueryUserProfitByRideInfoResponse resp, String groupFen) {
        OrderSubsidyRecRequest aiRequest = new OrderSubsidyRecRequest();
        aiRequest.setUser_id(UserInfoUtil.getUseridByCid(userCid));
        aiRequest.setUser_cid(userCid);
        Location start_location = new Location();
        start_location.setLongitude(startLongitude);
        start_location.setLatitude(startLatitude);
        aiRequest.setStart_location(start_location);

        Location end_location = new Location();
        end_location.setLongitude(endLongitude);
        end_location.setLatitude(endLatitude);
        aiRequest.setEnd_location(end_location);

        aiRequest.setGo_time(planStartTime);

        List<Price> priceList = Lists.newArrayList();

        Map<String, Float> couponPriceMap = couponPriceMap(resp);
        //未拼成价
        appendPriceYuan(PriceTypeEnum.suggest_price, suggestPrice, couponPriceMap, priceList);
        //拼成价的补贴按照未拼成价计算，优惠券部分也是这样处理的
        appendPriceYuan(PriceTypeEnum.multi_price, suggestPrice, couponPriceMap, priceList);
        appendPriceFen(PriceTypeEnum.cosy_price, cosyFen, couponPriceMap, priceList);
        appendPriceFen(PriceTypeEnum.group_fen, groupFen, couponPriceMap, priceList);
        appendPriceYuan(PriceTypeEnum.single_price, singlePrice, couponPriceMap, priceList);
        //未拼成价
        appendPriceYuan(PriceTypeEnum.eco_suggest_price, ecoSuggestPrice, couponPriceMap, priceList);
        //拼成价
        appendPriceYuan(PriceTypeEnum.eco_multi_price, ecoMultiPrice, couponPriceMap, priceList);
        appendPriceFen(PriceTypeEnum.eco_cosy_price, ecoCosyFen, couponPriceMap, priceList);
        appendPriceYuan(PriceTypeEnum.eco_single_price, ecoSinglePrice, couponPriceMap, priceList);

        aiRequest.setPrices(priceList);

        aiRequest.setDdc_info(JsonMapper.toJson(currentDdcInfo));

        return aiRequest;
    }

    /**
     * 工具方法
     *
     * @param priceType
     * @param priceYuan
     * @param couponPriceMap
     * @param priceList
     */
    private void appendPriceYuan(PriceTypeEnum priceType, String priceYuan, Map<String, Float> couponPriceMap, List<Price> priceList) {
        if (StringUtils.isNotBlank(priceYuan)) {
            //元转分
            int origin_price = new BigDecimal(priceYuan).multiply(BigDecimal.valueOf(100)).intValue();
            appendPriceFen(priceType, String.valueOf(origin_price), couponPriceMap, priceList);
        }
    }

    /**
     * 工具方法
     *
     * @param priceType
     * @param priceFen
     * @param couponPriceMap
     * @param priceList
     */
    private void appendPriceFen(PriceTypeEnum priceType, String priceFen, Map<String, Float> couponPriceMap, List<Price> priceList) {
        if (StringUtils.isNotBlank(priceFen)) {
            //分
            int origin_price = new BigDecimal(priceFen).intValue();
            if (origin_price > 0) {
                Price price = new Price();
                price.setPrice_type(priceType.getType());
                price.setOrigin_price(origin_price);
                //元转分
                price.setCoupon_price((int) (couponPriceMap.getOrDefault(price.getPrice_type(), 0f) * 100));
                priceList.add(price);
            }

        }
    }

    /**
     * 为thrift组装AI参数，AI的金额单位都是分
     *
     * @param request                           单位是分
     * @param queryUserProfitByRideInfoResponse 优惠券的单位是元
     * @return
     */
    private OrderSubsidyRecRequest createAIRequestForThrift(int userId, String userCid, QuerySubsidyFromAIRequest request, QueryUserProfitByRideInfoResponse queryUserProfitByRideInfoResponse) {
        Map<String, Float> couponPriceMap = couponPriceMap(queryUserProfitByRideInfoResponse);

        OrderSubsidyRecRequest aiRequest = new OrderSubsidyRecRequest();
        aiRequest.setUser_id(userId);
        aiRequest.setUser_cid(userCid);
        Location start_location = new Location();
        start_location.setLongitude(request.getStartLongitude());
        start_location.setLatitude(request.getStartLatitude());
        aiRequest.setStart_location(start_location);

        Location end_location = new Location();
        end_location.setLongitude(request.getEndLongitude());
        end_location.setLatitude(request.getEndLatitude());
        aiRequest.setEnd_location(end_location);

        aiRequest.setGo_time(request.getPlanStartTime());

        List<Price> priceList = request.getPriceRequest().stream().map(priceRequest -> {
            Price price = new Price();
            price.setPrice_type(priceRequest.getPriceType());
            //原价是：分
            price.setOrigin_price(new BigDecimal(priceRequest.getOriginPrice()).intValue());
            //优惠券的单位是元，需要转成分
            price.setCoupon_price((int) (couponPriceMap.getOrDefault(priceRequest.getPriceType(), 0f) * 100));
            return price;
        }).collect(Collectors.toList());
        aiRequest.setPrices(priceList);

        aiRequest.setDdc_info(request.getDdcInfo());
        return aiRequest;
    }

    private Map<String, Float> couponPriceMap(QueryUserProfitByRideInfoResponse resp) {
        Map<String, Float> couponPriceMap = Maps.newHashMap();
        if (resp != null) {
            //设置内部油车
            String couponPrice = resp.getCouponPrice();
            if (StringUtils.isNotBlank(couponPrice)) {
                couponPriceMap.put(PriceTypeEnum.suggest_price.getType(), Float.valueOf(couponPrice));
            }
            if (StringUtils.isNotBlank(resp.getCouponPriceMulti())) {
                couponPriceMap.put(PriceTypeEnum.multi_price.getType(), Float.valueOf(resp.getCouponPriceMulti()));
            }
            if (StringUtils.isNotBlank(resp.getCouponPriceCosy())) {
                couponPriceMap.put(PriceTypeEnum.cosy_price.getType(), Float.valueOf(resp.getCouponPriceCosy()));
            }
            if (StringUtils.isNotBlank(resp.getCouponPriceSingle())) {
                couponPriceMap.put(PriceTypeEnum.single_price.getType(), Float.valueOf(resp.getCouponPriceSingle()));
            }
            if (StringUtils.isNotBlank(resp.getCouponPriceGroup())) {
                couponPriceMap.put(PriceTypeEnum.group_fen.getType(), Float.valueOf(resp.getCouponPriceGroup()));
            }
            //设置内部电车
            if (resp.getEcoPriceEntity() != null) {
                com.didapinche.trade.thrift.entities.EcoPriceEntity inner = resp.getEcoPriceEntity();

                if (StringUtils.isNotBlank(inner.getCouponPrice())) {
                    couponPriceMap.put(PriceTypeEnum.eco_suggest_price.getType(), Float.valueOf(inner.getCouponPrice()));
                }
                if (StringUtils.isNotBlank(inner.getCouponPriceMulti())) {
                    couponPriceMap.put(PriceTypeEnum.eco_multi_price.getType(), Float.valueOf(inner.getCouponPriceMulti()));
                }
                if (StringUtils.isNotBlank(inner.getCouponPriceCosy())) {
                    couponPriceMap.put(PriceTypeEnum.eco_cosy_price.getType(), Float.valueOf(inner.getCouponPriceCosy()));
                }
                if (StringUtils.isNotBlank(inner.getCouponPriceSingle())) {
                    couponPriceMap.put(PriceTypeEnum.eco_single_price.getType(), Float.valueOf(inner.getCouponPriceSingle()));
                }
            }
        }
        return couponPriceMap;
    }

}
