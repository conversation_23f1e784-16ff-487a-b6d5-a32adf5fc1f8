package com.didapinche.trade.application.service.support;

import com.didapinche.agaue.common.exception.DException;
import com.didapinche.agaue.datasource.common.Routing;
import com.didapinche.agaue.datasource.enums.DatabaseEnum;
import com.didapinche.agaue.datasource.enums.DatabaseTypeEnum;
import com.didapinche.agaue.datasource.toolkit.DynamicDataSourceContextHolder;
import com.didapinche.payment.async.localmsg.LaunchHelper;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.server.commons.common.util.LoadPropertyUtil;
import com.didapinche.trade.domin.async.datas.UpdateTradeAmountStatusData;
import com.didapinche.trade.domin.async.events.BizEventTypeEnum;
import com.didapinche.trade.infrastructure.database.LocalDataBaseRoutingService;
import com.didapinche.trade.infrastructure.exception.TradeErrorCode;
import com.didapinche.trade.infrastructure.transaction.TransactionService;
import com.didapinche.trade.infrastructure.util.TradeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class UpdateTradeAmountStatusService {

    @Resource
    private TransactionService transactionService;
    @Resource
    private LocalDataBaseRoutingService localDataBaseRoutingService;

    public void updateStatus(UpdateTradeAmountStatusData data) {
        try {
            if (data.getPaymentOrderId() == null
                    || data.getPaymentOrderId() <= 0
                    || data.getOrderId() == null
                    || data.getOrderId() <= 0
                    || data.getUserId() == null
                    || data.getUserId() <= 0
                    || StringUtils.isAnyBlank(data.getReceiptStatus(), data.getAmountType())) {
                log.error("更新支付组成状态请求参数错误. data:{}", JsonMapper.toJson(data));
                return;
            }
            Routing routing = new Routing().setLookupId(localDataBaseRoutingService.getLookupIdById(data.getPaymentOrderId()));
            DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
            transactionService.executeAccount(() -> LaunchHelper.RPC()
                    .bizType(BizEventTypeEnum.UpdateTradeAmountStatusEvent)
                    .bizId(TradeUtil.genLMBizId2(String.valueOf(data.getPaymentOrderId())))
                    .data(data)
                    .launch());
            log.info("发送更新支付组成状态请求end. paymentOrderId:{}, orderId:{}, userId{}", data.getPaymentOrderId(), data.getOrderId(), data.getUserId());
        } catch (Exception e) {
            log.error("发送更新支付组成状态事件 error. paymentOrderId:{}, orderId:{}, userId{}", data.getPaymentOrderId(), data.getOrderId(), data.getUserId(), e);
            if (LoadPropertyUtil.getProperty("trade.update.amount.throw.ex.enable", Boolean.class, false)) {
                throw new DException(TradeErrorCode.SEND_UPDATE_AMOUNT_DETAIL_STATUS_EVENT_ERROR);
            }
        }
    }
}
