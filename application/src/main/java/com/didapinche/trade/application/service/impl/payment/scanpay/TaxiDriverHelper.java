package com.didapinche.trade.application.service.impl.payment.scanpay;

import com.didapinche.agaue.common.exception.DException;
import com.didapinche.server.commons.common.enums.CertificationStateEnum;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.server.commons.common.spring.SpringUtils;
import com.didapinche.server.commons.common.util.LoadPropertyUtil;
import com.didapinche.taxi.usercar.UserCar2cThriftService;
import com.didapinche.thrift.taxidriver.TaxidriverThriftService;
import com.didapinche.thrift.user.UserThriftService;
import com.didapinche.thrift.userpermission.PermissionEnum;
import com.didapinche.thrift.userpermission.PermissionResult;
import com.didapinche.thrift.userpermission.TResult;
import com.didapinche.thrift.userpermission.UserpermissionThriftService;
import com.velo.carpoolv3.entity.tbl.UserUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

@Slf4j
public class TaxiDriverHelper {
    private static final UserpermissionThriftService.Iface userpermissionThriftService = SpringUtils.getBean(UserpermissionThriftService.Iface.class);
    private static final UserThriftService.Iface userThriftService = SpringUtils.getBean(UserThriftService.Iface.class);
    private static final UserCar2cThriftService.Iface userCar2cThriftService = SpringUtils.getBean(UserCar2cThriftService.Iface.class);

    private static final TaxidriverThriftService.Iface taxidriverThriftService = SpringUtils.getBean(TaxidriverThriftService.Iface.class);

    public static boolean checkUserEnable(Long userId) {
        com.didapinche.thrift.user.TResult result;
        try {
            result = userThriftService.getTaxiUserById(Math.toIntExact(userId));
        } catch (Exception e) {
            log.error("getUserUser error driverId={}", userId, e);
            throw new DException(ErrorCode.C_201, ErrorCode.C_201_MSG);
        }
        if (result == null || result.getCode() != 0 || result.getUserJson() == null) {
            log.error("getUserUser failed driverId={}", userId);
            throw new DException(ErrorCode.C_201, ErrorCode.C_201_MSG);
        }
        UserUser user = JsonMapper.json2Bean(result.getUserJson(), UserUser.class);
        if (user == null) {
            log.error("getUserUser failed user is null. driverId={}", userId);
            throw new DException(ErrorCode.C_201, ErrorCode.C_201_MSG);
        }
        try {
            com.didapinche.thrift.user.TResult taxiUserUserBlack = userThriftService.getTaxiUserUserBlackByPhone(user.getAccountId());
            if (taxiUserUserBlack != null && StringUtils.isNotBlank(taxiUserUserBlack.getUserJson())) {
                log.info("扫码支付获取黑名单信息,匹配到有黑名单记录.禁止使用. driverId:{}", userId);
                return false;
            }
        } catch (Exception e) {
            log.error("扫码支付获取黑名单信息失败...driverId:{}", userId, e);
        }
        Integer certificationState = get(userId);
        log.info("扫码支付司机认证状态. userId={} certificationState={}", userId, certificationState);
        if (certificationState == null || certificationState == CertificationStateEnum.UNSUBMIT.getState() || certificationState == CertificationStateEnum.UNDERREVIEW.getState() || certificationState == CertificationStateEnum.FAIL.getState()) {
            //0-未提交审核 1-审核中 2-审核失败 3-审核通过)
            log.info("userId:{},认证转态不对,certificationState:{}", userId, certificationState);
            return false;
        }
        return true;
    }

    public static boolean isBanned(Long userId) {
        try {
            TResult result = userpermissionThriftService.isBanned(userId, PermissionEnum.OFFLINE_PAY_CODE);
            if (result != null && result.getCode() == 0 && result.getPermissionResult() == PermissionResult.BANNED.getValue()) {
                log.info("收款码权限被禁止driverId:{},banned:{}", userId, result.getPermissionResult());
                return true;
            }
        } catch (Exception e) {
            log.error("扫码支付获取isBanned信息失败...driverId:{}", userId, e);
        }
        return false;
    }

    /**
     * 通过调服务
     *
     * @param userId
     * @return
     */
    public static Integer get(long userId) {
        if ("true".equals(LoadPropertyUtil.getProperty("taxi.driver.new.api.switch", "old"))) {
            log.info("扫码支付认证状态切换新接口. userId={}", userId);
            return getNew(userId);
        } else {
            try {
                com.didapinche.taxi.usercar.TaxiDriver result = userCar2cThriftService.getTaxiDriverByUserId((int) userId);
                return result.getCertificationState();
            } catch (Exception e) {
                log.info("调用getTaxiDriverByUserId异常.userId:{}", userId, e);
                return null;
            }
        }

    }

    public static Integer getNew(long userId) {
        try {
            com.didapinche.thrift.taxidriver.TResult result = taxidriverThriftService.getTaxiDriverByUserId((int) userId);
            if (result != null && StringUtils.isNotBlank(result.getData())) {
                TaxiDriver taxiDriver = JsonMapper.json2Bean(result.getData(), TaxiDriver.class);
                return taxiDriver.getCertificationState();
            }
            return null;
        } catch (Exception e) {
            log.info("调用getTaxiDriverByUserId异常.userId:{}", userId, e);
            return null;
        }
    }
}
