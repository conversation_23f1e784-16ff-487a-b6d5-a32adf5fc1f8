package com.didapinche.trade.application.service.impl.payment.scanpay;

import lombok.Getter;

import java.util.Date;

@Getter
public class TaxiDriver {
    private Long userId;

    private String avatar;

    private String firstName;

    private String lastName;

    private Short gender;

    private String idCardNo;

    private Date getLicenseDate;

    private Integer taxiCompanyId;

    private String taxiCarNo;

    private Date taxiRegDate;

    private String owner;

    private String licenseImg;

    private String drivingLicenseImg;

    private String carPhoto;

    private String driverPermitNo;

    private String driverPermitImg;

    private Integer certificationState;

    private String certificationComment;

    private Date certificationTime;

    private Date certificationOpTime;

    private Integer sysOpId;

    private Integer sysCuser;

    private Short sysStatus;

    private Date updateTime;

    private Boolean taxiCategory;

    private String rejectReason;

    private Byte withdrawState;

    private Date reviewCertificationOpTime;

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar == null ? null : avatar.trim();
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName == null ? null : firstName.trim();
    }

    public void setLastName(String lastName) {
        this.lastName = lastName == null ? null : lastName.trim();
    }

    public void setGender(Short gender) {
        this.gender = gender;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo == null ? null : idCardNo.trim();
    }

    public void setGetLicenseDate(Date getLicenseDate) {
        this.getLicenseDate = getLicenseDate;
    }

    public void setTaxiCompanyId(Integer taxiCompanyId) {
        this.taxiCompanyId = taxiCompanyId;
    }

    public void setTaxiCarNo(String taxiCarNo) {
        this.taxiCarNo = taxiCarNo == null ? null : taxiCarNo.trim();
    }

    public void setTaxiRegDate(Date taxiRegDate) {
        this.taxiRegDate = taxiRegDate;
    }

    public void setOwner(String owner) {
        this.owner = owner == null ? null : owner.trim();
    }

    public void setLicenseImg(String licenseImg) {
        this.licenseImg = licenseImg == null ? null : licenseImg.trim();
    }

    public void setDrivingLicenseImg(String drivingLicenseImg) {
        this.drivingLicenseImg = drivingLicenseImg == null ? null : drivingLicenseImg.trim();
    }

    public void setCarPhoto(String carPhoto) {
        this.carPhoto = carPhoto == null ? null : carPhoto.trim();
    }

    public void setDriverPermitNo(String driverPermitNo) {
        this.driverPermitNo = driverPermitNo == null ? null : driverPermitNo.trim();
    }

    public void setDriverPermitImg(String driverPermitImg) {
        this.driverPermitImg = driverPermitImg == null ? null : driverPermitImg.trim();
    }

    public void setCertificationState(Integer certificationState) {
        this.certificationState = certificationState;
    }

    public void setCertificationComment(String certificationComment) {
        this.certificationComment = certificationComment == null ? null : certificationComment.trim();
    }

    public void setCertificationTime(Date certificationTime) {
        this.certificationTime = certificationTime;
    }

    public void setCertificationOpTime(Date certificationOpTime) {
        this.certificationOpTime = certificationOpTime;
    }

    public void setSysOpId(Integer sysOpId) {
        this.sysOpId = sysOpId;
    }

    public void setSysCuser(Integer sysCuser) {
        this.sysCuser = sysCuser;
    }

    public void setSysStatus(Short sysStatus) {
        this.sysStatus = sysStatus;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public void setTaxiCategory(Boolean taxiCategory) {
        this.taxiCategory = taxiCategory;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason == null ? null : rejectReason.trim();
    }

    public void setWithdrawState(Byte withdrawState) {
        this.withdrawState = withdrawState;
    }

    public void setReviewCertificationOpTime(Date reviewCertificationOpTime) {
        this.reviewCertificationOpTime = reviewCertificationOpTime;
    }
}