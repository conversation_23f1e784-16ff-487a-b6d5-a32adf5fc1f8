package com.didapinche.trade.application.service.impl.payment;

import com.didapinche.agaue.common.exception.DException;
import com.didapinche.trade.application.service.impl.AbstractTradeService;
import com.didapinche.trade.domin.doublewrite.DoubleWriteDomainService;
import com.didapinche.trade.domin.payment.CheckPaymentDomainService;
import com.didapinche.trade.domin.payment.CouponDomainService;
import com.didapinche.trade.domin.payment.pau.PaymentPayAfterUseDomainService;
import com.didapinche.trade.infrastructure.entities.TradeOrderNote;
import com.didapinche.trade.infrastructure.enums.PaymentPauCutFlageEnum;
import com.didapinche.trade.infrastructure.exception.TradeErrorCode;
import com.didapinche.trade.infrastructure.service.impl.TradeAmountDetailsModelService;
import com.didapinche.trade.infrastructure.service.impl.TradeCouponDetailsModelService;
import com.didapinche.trade.infrastructure.service.impl.TradeOrderModelService;
import com.didapinche.trade.infrastructure.service.impl.TradeProductDetailsModelService;
import com.didapinche.trade.infrastructure.tbl.TradeAmountDetailsModel;
import com.didapinche.trade.infrastructure.tbl.TradeCouponDetailsModel;
import com.didapinche.trade.infrastructure.tbl.TradeOrderModel;
import com.didapinche.trade.infrastructure.tbl.TradeProductDetailsModel;
import com.didapinche.trade.infrastructure.thrift.AccountThriftSupportService;
import com.didapinche.trade.infrastructure.thrift.RoutingThriftSupportService;
import com.didapinche.trade.thrift.entities.QueryAmountResult;
import com.didapinche.trade.thrift.entities.QueryProductResult;
import com.didapinche.trade.thrift.enums.TOrderAccountTypeEnum;
import com.didapinche.trade.thrift.enums.TradeOrderStatusEnum;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static com.didapinche.trade.infrastructure.constants.TradeConstants.COUPON_MALL_PRODUCT_TYPE;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractPaymentService<T> extends AbstractTradeService<T> {

    @Resource
    protected TradeOrderModelService tradeOrderModelService;
    @Resource
    protected TradeAmountDetailsModelService tradeAmountDetailsModelService;
    @Resource
    protected TradeCouponDetailsModelService tradeCouponDetailsModelService;
    @Resource
    protected TradeProductDetailsModelService tradeProductDetailsModelService;
    @Resource
    protected RoutingThriftSupportService routingThriftService;
    @Resource
    protected CheckPaymentDomainService checkPaymentDomainService;
    @Resource
    protected CouponDomainService couponDomainService;
    @Resource
    protected PaymentPayAfterUseDomainService paymentPayAfterUseDomainService;
    @Resource
    protected DoubleWriteDomainService doubleWriteDomainService;
    @Resource
    protected AccountThriftSupportService accountThriftSupportService;

    protected TradeOrderModel checkPauOrderStatusWithLock(Long orderId, Long userId) {
        TradeOrderModel tradeOrderModel = tradeOrderModelService.selectWithLock(orderId, userId);
        if (tradeOrderModel == null) {
            payLogger.error("先享后付补充支付. 未查到支付单");
            throw new DException(TradeErrorCode.NOT_FUND_PAY_ORDER_ERROR);
        }
        if (!TradeOrderStatusEnum.success.name().equals(tradeOrderModel.getStatus())) {
            payLogger.error("先享后付补充支付. 状态不对. status：{}", tradeOrderModel.getStatus());
            throw new DException(TradeErrorCode.NO_PAY_ERROR);
        }
        TradeOrderNote tradeOrderNote = TradeOrderNote.create(tradeOrderModel.getNote());
        if (!paymentPayAfterUseDomainService.isPauFlag(tradeOrderNote.getPaymentWay())) {
            payLogger.error("先享后付补充支付. 不是先享后付单.");
            throw new DException(TradeErrorCode.NOT_PAU_PAYMENT_ORDER_ERROR);
        }
        if (!PaymentPauCutFlageEnum.create.name().equals(tradeOrderNote.getPauCutFlag())) {
            payLogger.info("先享后付补充支付. 已发起扣款.");
            throw new DException(TradeErrorCode.ALREADY_PAU_START_CUT_ERROR);
        }
        return tradeOrderModel;
    }

    protected List<QueryProductResult> queryProductResult(TradeOrderModel tradeOrderModel) {
        List<QueryProductResult> productDetails = new ArrayList<>();
        List<TradeProductDetailsModel> tradeProductDetailsModels = tradeProductDetailsModelService.selectByOrderId(tradeOrderModel.getId(), tradeOrderModel.getUserId());
        for (TradeProductDetailsModel tradeProductDetailsModel : tradeProductDetailsModels) {
            QueryProductResult queryProductResult = new QueryProductResult();
            queryProductResult.setPrice(tradeProductDetailsModel.getPrice().toString());
            queryProductResult.setType(tradeProductDetailsModel.getType());
            queryProductResult.setProductId(tradeProductDetailsModel.getProductId());
            productDetails.add(queryProductResult);
        }
        return productDetails;
    }

    protected BigDecimal queryGoodsAmount(Long userId, Long tradeOrderId) {
        BigDecimal mallAmount = BigDecimal.ZERO;
        List<TradeProductDetailsModel> tradeProductDetailsModels = tradeProductDetailsModelService.selectByOrderId(tradeOrderId, userId);
        for (TradeProductDetailsModel tradeProductDetailsModel : tradeProductDetailsModels) {
            if (COUPON_MALL_PRODUCT_TYPE.contains(tradeProductDetailsModel.getType())) {
                mallAmount = mallAmount.add(tradeProductDetailsModel.getPrice());
            }
        }
        return mallAmount;
    }

    protected QueryAmountResult queryAmountResult(TradeOrderModel tradeOrderModel, Boolean mallContains, BigDecimal mallAmount) {
        QueryAmountResult queryAmountResult = new QueryAmountResult();
        List<TradeAmountDetailsModel> tradeAmountDetailsModelList = tradeAmountDetailsModelService.selectByTradeOrderId(tradeOrderModel.getId(), tradeOrderModel.getUserId());
        for (TradeAmountDetailsModel tradeAmountDetailsModel : tradeAmountDetailsModelList) {
            if (TOrderAccountTypeEnum.third.name().equals(tradeAmountDetailsModel.getType())) {
                BigDecimal amount = mallContains ? tradeAmountDetailsModel.getAmount() : tradeAmountDetailsModel.getAmount().subtract(mallAmount);
                queryAmountResult.setThird(amount.toString());
                queryAmountResult.setThirdChannel(tradeOrderModel.getThirdChannel());
                continue;
            }
            if (TOrderAccountTypeEnum.passenger.name().equals(tradeAmountDetailsModel.getType())) {
                queryAmountResult.setPassenger(tradeAmountDetailsModel.getAmount().toString());
                continue;
            }
            if (TOrderAccountTypeEnum.bonus.name().equals(tradeAmountDetailsModel.getType())) {
                queryAmountResult.setBonus(tradeAmountDetailsModel.getAmount().toString());
                continue;
            }
            if (TOrderAccountTypeEnum.coupon.name().equals(tradeAmountDetailsModel.getType())) {
                queryAmountResult.setCoupon(tradeAmountDetailsModel.getAmount().toString());
                TradeCouponDetailsModel tradeCouponDetailsModel = tradeCouponDetailsModelService.selectCouponDetailsByOrderId(tradeOrderModel.getId(), tradeOrderModel.getUserId());
                if (tradeCouponDetailsModel == null) {
                    log.error("coupon not found tradeOrderId:{}", tradeOrderModel.getId());
                    continue;
                }
                queryAmountResult.setCouponId(tradeCouponDetailsModel.getCouponId());
                continue;
            }
            if (TOrderAccountTypeEnum.passenger_not_withdraw.name().equals(tradeAmountDetailsModel.getType())) {
                queryAmountResult.setPassengerNotWithdraw(tradeAmountDetailsModel.getAmount().toString());
                continue;
            }
            if (TOrderAccountTypeEnum.c_enterprise_vip.name().equals(tradeAmountDetailsModel.getType())) {
                queryAmountResult.setCEnterpriseVip(tradeAmountDetailsModel.getAmount().toString());
                continue;
            }
            if (TOrderAccountTypeEnum.t_enterprise_vip.name().equals(tradeAmountDetailsModel.getType())) {
                queryAmountResult.setTEnterpriseVip(tradeAmountDetailsModel.getAmount().toString());
                continue;
            }
            if (TOrderAccountTypeEnum.t_enterprise.name().equals(tradeAmountDetailsModel.getType())) {
                queryAmountResult.setTEnterprise(tradeAmountDetailsModel.getAmount().toString());
                continue;
            }
            if (TOrderAccountTypeEnum.recover_bonus.name().equals(tradeAmountDetailsModel.getType())) {
                queryAmountResult.setBonusRecover(tradeAmountDetailsModel.getAmount().toString());
                continue;
            }
            if (TOrderAccountTypeEnum.recover_coupon.name().equals(tradeAmountDetailsModel.getType())) {
                queryAmountResult.setCouponRecover(tradeAmountDetailsModel.getAmount().toString());
            }
        }
        return queryAmountResult;
    }
}
