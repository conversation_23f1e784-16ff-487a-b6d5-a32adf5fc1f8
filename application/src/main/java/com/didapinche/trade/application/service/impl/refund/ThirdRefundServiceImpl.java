package com.didapinche.trade.application.service.impl.refund;

import com.didapinche.agaue.common.exception.DidaCode;
import com.didapinche.agaue.common.exception.SuccessCode;
import com.didapinche.agaue.common.result.Result;
import com.didapinche.agaue.tools.convert.AmountConvertUtil;
import com.didapinche.doublewritecheck.DoubleWriteCheckParam;
import com.didapinche.doublewritecheck.entity.activity.GoodsRefundParam;
import com.didapinche.finance.mq.context.enums.TradeTypeEnum;
import com.didapinche.payment.frigate.Frigate;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.trade.application.BO.RefundBO;
import com.didapinche.trade.domin.order.DO.OrderRefundCaseEnum;
import com.didapinche.trade.domin.payment.pau.PaymentPayAfterUseDomainService;
import com.didapinche.trade.domin.repository.TradeOrderRepository;
import com.didapinche.trade.infrastructure.entities.TradeOrderNote;
import com.didapinche.trade.infrastructure.exception.TradeErrorCode;
import com.didapinche.trade.infrastructure.mq.RocketMqSendUtils;
import com.didapinche.trade.infrastructure.tbl.TradeOrderModel;
import com.didapinche.trade.thrift.entities.RefundResult;
import com.didapinche.trade.thrift.entities.ThirdRefundRequest;
import com.didapinche.trade.thrift.enums.TBusinessEnum;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/14 14:57
 * @Version 1.0
 *
 * 纯三方退款
 */
@Service
@Slf4j
public class ThirdRefundServiceImpl extends AbstractRefundService<ThirdRefundRequest> {


    @Override
    public DidaCode verifyParameter(ThirdRefundRequest parameter){
        if (parameter.getUserId() <= 0
                || parameter.getOrderId() <= 0
                || AmountConvertUtil.yuan2BigDecimal(parameter.getRefundFee()).compareTo(BigDecimal.ZERO) < 0){
            return TradeErrorCode.PARAM_ERROR;
        }
        return SuccessCode.SUCCESS;
    }
    /**
     *  第三方返款
     */
    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class,isolation = Isolation.READ_COMMITTED)
    @Frigate(name = "第三方返款")
    public Result<List<RefundResult>> doTrade(ThirdRefundRequest request) {

        RefundBO refundBO = new RefundBO();
        refundBO.setUserId(request.getUserId())
                .setRefunDate(new Date())
                .setBusinessType(request.getBusinessType())
                .setDeductFee(AmountConvertUtil.yuan2BigDecimal(request.getRefundFee()))
                .setMajorOrderId(request.getOrderId())
                .setOrderDesc(request.getOrderDesc())
                .setOrderIdList(Lists.newArrayList(request.getOrderId()))
                .setCouponBack(false)
                .setProductId(request.getProductId())//HPCDO 在此处有使用productId
                .setSource(request.getSource());
        if (TBusinessEnum.mall.name().equals(request.getBusinessType())) {
            refundBO.setRefundStatusSuccess(false);
        }
        Result<List<RefundResult>> refund = refund(refundBO);
        if (refund.getCode() == 0){
            orderStatusDomainService.syncOrderRefundStatus(request.getBusinessType(),refund.getData(),request.getUserId(),request.getProductId(), OrderRefundCaseEnum.refund,refundBO.getRefunDate());
            doubleWrite(request,refund.getData());
        }
        return refund;
    }

    @Autowired
    private TradeOrderRepository tradeOrderRepository;
    @Resource
    private PaymentPayAfterUseDomainService paymentPayAfterUseDomainService;

    private void doubleWrite(ThirdRefundRequest request, List<RefundResult> refundResults){

        if (!TBusinessEnum.mall.name().equals(request.getBusinessType())){
            return;
        }
        TradeOrderModel tradeOrderModel = tradeOrderRepository.queryById(request.getOrderId(), request.getUserId());
        TradeOrderNote note = TradeOrderNote.create(tradeOrderModel.getNote());
        boolean pauFlag = paymentPayAfterUseDomainService.isPauFlag(note.getPauCutFlag());
        for (RefundResult refundResult:refundResults){
            GoodsRefundParam refundParam = new GoodsRefundParam();
            refundParam.setProductType(TBusinessEnum.mall.name().equals(tradeOrderModel.getBusinessType())?"activity":tradeOrderModel.getBusinessType());
            refundParam.setOrderCid(TBusinessEnum.mall.name().equals(tradeOrderModel.getBusinessType())?tradeOrderModel.getMajorProductId():tradeOrderModel.getId().toString());
            refundParam.setRefundCid(refundResult.getRefundOrderId()+"");
            refundParam.setPayChannel(tradeOrderModel.getThirdChannel());
            refundParam.setSourceId("cms".equals(request.getSource())?0L:1L);
            refundParam.setUserId(request.getUserId());
            if (!TBusinessEnum.mall.name().equals(tradeOrderModel.getBusinessType()) &&
                    tradeOrderModel.getMajorProductId().matches("[0-9]+")){
                refundParam.setBusinessOrderId(Long.parseLong(tradeOrderModel.getMajorProductId()));
            }
            refundParam.setRefundPrice(AmountConvertUtil.yuan2penny(refundResult.getTotalPrice()));
            refundParam.setPauOrder(pauFlag);
            refundParam.setPauFinishOrder(note.getPauFinishOrder());

            DoubleWriteCheckParam doubleWriteCheckParam = new DoubleWriteCheckParam();
            doubleWriteCheckParam.setBusinessType(request.getBusinessType());
            doubleWriteCheckParam.setSource("active");
            doubleWriteCheckParam.setTradeType(TradeTypeEnum.refund.name());
            doubleWriteCheckParam.setCheckEntityMap("");
            doubleWriteCheckParam.setDwcParam(JsonMapper.toJson(refundParam));
            doubleWriteCheckParam.setCheckFlag(false);
            doubleWriteCheckParam.setRouteId(tradeOrderModel.getMajorProductId() + "");
            RocketMqSendUtils.doubleWrite(doubleWriteCheckParam);
        }
    }

}
