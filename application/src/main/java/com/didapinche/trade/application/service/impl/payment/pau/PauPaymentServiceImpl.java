package com.didapinche.trade.application.service.impl.payment.pau;

import cn.hutool.core.collection.CollUtil;
import com.didapinche.agaue.common.exception.DException;
import com.didapinche.agaue.common.exception.DidaCode;
import com.didapinche.agaue.common.exception.SuccessCode;
import com.didapinche.agaue.datasource.common.Routing;
import com.didapinche.agaue.datasource.enums.DatabaseEnum;
import com.didapinche.agaue.datasource.enums.DatabaseTypeEnum;
import com.didapinche.agaue.datasource.toolkit.DynamicDataSourceContextHolder;
import com.didapinche.agaue.tools.convert.AmountConvertUtil;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.trade.application.service.impl.payment.AbstractPaymentService;
import com.didapinche.trade.application.service.impl.payment.PaymentServiceImpl;
import com.didapinche.trade.domin.payment.PauGuideService;
import com.didapinche.trade.domin.payment.pau.PaymentPauSystemRetryPaymentDomainService;
import com.didapinche.trade.infrastructure.constants.TradeConstants;
import com.didapinche.trade.infrastructure.exception.TradeErrorCode;
import com.didapinche.trade.infrastructure.util.BigDecimalUtil;
import com.didapinche.trade.infrastructure.util.OrderDetailsVerify;
import com.didapinche.trade.thrift.entities.PaymentRequest;
import com.didapinche.trade.thrift.entities.PaymentResult;
import com.didapinche.trade.thrift.entities.QueryPauGrayRequest;
import com.didapinche.trade.thrift.entities.QueryPauGrayResponse;
import com.didapinche.trade.thrift.entities.TOrderAmountDetails;
import com.didapinche.trade.thrift.entities.TPauPaymentRequest;
import com.didapinche.trade.thrift.entities.TPauPaymentResult;
import com.didapinche.trade.thrift.entities.TRideInfo;
import com.didapinche.trade.thrift.entities.ThirdPayRequest;
import com.didapinche.trade.thrift.enums.TBusinessModelEnum;
import com.didapinche.trade.thrift.enums.TChannelEnum;
import com.didapinche.trade.thrift.enums.TOrderAccountTypeEnum;
import com.didapinche.trade.thrift.enums.TOrderSourceEnum;
import com.didapinche.trade.thrift.enums.TPaymentWayEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Service
@Slf4j
public class PauPaymentServiceImpl extends AbstractPaymentService<TPauPaymentRequest> {

    @Resource
    private PaymentServiceImpl paymentService;
    @Resource
    private PaymentPauSystemRetryPaymentDomainService paymentPauSystemRetryPaymentDomainService;
    @Resource
    private PauGuideService pauGuideService;

    @Override
    public TPauPaymentResult doTrade(TPauPaymentRequest request) {
        Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(request.majorProductId));
        DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
        TPauPaymentResult result = new TPauPaymentResult();
        if (checkPaymentDomainService.checkPaymentAlreadySuccess(request.getMajorProductId(), request.getBusinessType(), request.getUserId(), false)) {
            log.info("PauPaymentServiceImpl.checkPaymentAlreadySuccess. productId={} businessType={} userId={}", request.getMajorProductId(), request.getBusinessType(), request.getUserId());
            return result;
        }
        //兼容顺风车上送app来源
        if (TradeConstants.DIDA.equals(request.getSource())) {
            request.setSource(TOrderSourceEnum.didachuxing_app.name());
        }
        //ab实验
        if (!TOrderSourceEnum.didachuxing_app.name().equals(request.getSource())
                && !TOrderSourceEnum.booking_driver_app.name().equals(request.getSource())
                && StringUtils.isNotBlank(request.getUserCid())) {
            QueryPauGrayRequest queryPauGrayRequest = new QueryPauGrayRequest()
                    .setUserId(request.getUserId())
                    .setMinipg(true)
                    .setUserCid(request.getUserCid())
                    .setChannel(request.getChannelType());
            QueryPauGrayResponse queryPauGrayResponse = pauGuideService.queryPauGray(queryPauGrayRequest);
            if (!queryPauGrayResponse.isGray()) {
                if (log.isDebugEnabled()) {
                    log.debug("doPauPayment 暂时无法使用先乘后付. 灰度校验未通过. userId:{}", request.getUserId());
                }
                throw new DException(TradeErrorCode.PAU_SWITCH_OFF_PAYMENT_ERROR);
            }
        }
        PaymentRequest paymentRequest;
        if (request.isSetOldTradeOrderId() && request.getOldTradeOrderId() > 0) {
            //系统取消重新下单
            paymentRequest = paymentPauSystemRetryPaymentDomainService.systemRetryDoPauPayment(request);
        } else {
            //下单支付组装参数
            paymentRequest = toGetPaymentRequest(request);
        }
        if (paymentRequest == null) {
           return result;
        }
        payLogger.info("pauPayment start. paymentRequest:{}", JsonMapper.toJson(paymentRequest));
        DidaCode didaCode = paymentService.verifyParameter(paymentRequest);
        if (didaCode.getCode() != 0) {
            throw new DException(didaCode.getCode(), didaCode.getMsg());
        }
        PaymentResult paymentResult = paymentService.doTrade(paymentRequest);
        return result.setTradeOrderId(paymentResult.getPayTradeNo()).setBillId(paymentResult.getBillId());
    }

    private PaymentRequest toGetPaymentRequest(TPauPaymentRequest request) {
        String majorProductId = request.getMajorProductId();
        PaymentRequest paymentRequest = new PaymentRequest()
                .setUserId(request.getUserId())
                .setMajorProductId(majorProductId)
                .setTotalPrice(request.getTotalPrice())
                .setBusinessType(request.getBusinessType())
                .setSource(request.getSource())
                .setDesc(request.getDesc())
                .setPaymentWay(TChannelEnum.weixin.name().equals(request.getChannelType()) ? TPaymentWayEnum.weixin_pau.name() : (TChannelEnum.alipay.name().equals(request.getChannelType()) ? TPaymentWayEnum.zhima_pau.name() : TPaymentWayEnum.douyinpay_pau.name()))
                .setTradeProductDetails(request.getTradeProductDetails())
                .setThirdPayDetails(new ThirdPayRequest().setChannelType(request.getChannelType()))
                .setPaymentNote(request.getPaymentNote())
                .setDdcInfo(request.getDdcInfo())
                .setPrimaryProductId(majorProductId);
        //支付金额
        BigDecimal thirdAmount = AmountConvertUtil.yuan2BigDecimal(request.getTotalPrice());
        List<TOrderAmountDetails> orderAmountDetails = new ArrayList<>();
        //三方
        if (thirdAmount.compareTo(BigDecimal.ZERO) > 0) {
            orderAmountDetails.add(new TOrderAmountDetails()
                    .setAmount(thirdAmount.toString())
                    .setType(TOrderAccountTypeEnum.third.name()));
        }
        paymentRequest.setOrderAmountDetails(orderAmountDetails);
        return paymentRequest;
    }

    @Override
    public DidaCode verifyParameter(TPauPaymentRequest parameter) {
        if (parameter.getUserId() < 1) {
            return TradeErrorCode.PARAM_ERROR;
        }
        if (parameter.getMajorProductId().length() < 1) {
            return TradeErrorCode.PARAM_ERROR;
        }
        if (parameter.getDesc().length() > 60) {
            return TradeErrorCode.PARAM_ERROR;
        }
        if (AmountConvertUtil.yuan2BigDecimal(parameter.getTotalPrice()).compareTo(BigDecimal.ZERO) <= 0) {
            return TradeErrorCode.PARAM_ERROR;
        }
        if (StringUtils.isBlank(parameter.getChannelType())
                || !parameter.isSetRideInfo()
                || !parameter.isSetTradeProductDetails()) {
            return TradeErrorCode.PARAM_ERROR;
        }
        TRideInfo rideInfo = parameter.getRideInfo();
        if (StringUtils.isAnyBlank(rideInfo.getRideMoney(), rideInfo.getSinglePrice(), rideInfo.getPlanStartTime(), rideInfo.getRideType())) {
            return TradeErrorCode.PARAM_ERROR;
        }
        if (StringUtils.equals(parameter.getBusinessModel(), TBusinessModelEnum.carpool_mix.name())){
            if (!parameter.isSetRideInfo() || CollUtil.isEmpty(parameter.getRideInfo().getSinglePriceList())) {
                return TradeErrorCode.PARAM_ERROR;
            }
        }
        OrderDetailsVerify.checkTradeProductDetails(parameter.getTradeProductDetails(), AmountConvertUtil.yuan2BigDecimal(parameter.getTotalPrice()));
        return SuccessCode.SUCCESS;
    }

}
