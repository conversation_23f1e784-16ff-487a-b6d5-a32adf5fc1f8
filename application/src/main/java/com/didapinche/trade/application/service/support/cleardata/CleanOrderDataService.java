package com.didapinche.trade.application.service.support.cleardata;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.didapinche.agaue.datasource.autoconfigure.DynamicDataSourceProperties;
import com.didapinche.agaue.datasource.common.Routing;
import com.didapinche.agaue.datasource.enums.DatabaseEnum;
import com.didapinche.agaue.datasource.enums.DatabaseTypeEnum;
import com.didapinche.agaue.datasource.toolkit.DynamicDataSourceContextHolder;
import com.didapinche.agaue.tools.convert.AmountConvertUtil;
import com.didapinche.agaue.tools.idbuilder.TableSuffixUtil;
import com.didapinche.finance.mq.context.enums.TradeTypeEnum;
import com.didapinche.payment.async.localmsg.core.config.InteractionResult;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.server.commons.common.util.DateUtil;
import com.didapinche.server.commons.common.util.LoadPropertyUtil;
import com.didapinche.thrift.offlineread.AdditionInfo;
import com.didapinche.thrift.offlineread.OfflinereadThriftService;
import com.didapinche.thrift.offlineread.RideInfoParam;
import com.didapinche.thrift.offlineread.TQueryRideInfoByClearDataRequest;
import com.didapinche.thrift.offlineread.TQueryRideInfoByClearDataResponse;
import com.didapinche.trade.application.BO.ClearPaymentOrder;
import com.didapinche.trade.domin.DO.RemittanceOrderModelDO;
import com.didapinche.trade.domin.DO.TradeOrderDO;
import com.didapinche.trade.domin.async.custom.PayTypeTagEventCustom;
import com.didapinche.trade.domin.async.datas.PayTypeTagData;
import com.didapinche.trade.domin.liquidation.message.ConficateLiquidationMessage;
import com.didapinche.trade.domin.order.service.BizLineMapper;
import com.didapinche.trade.domin.repository.RemittanceOrderRepository;
import com.didapinche.trade.infrastructure.common.TradeState;
import com.didapinche.trade.infrastructure.constants.RedisKeyConstants;
import com.didapinche.trade.infrastructure.entities.TradeOrderNote;
import com.didapinche.trade.infrastructure.exception.TradeErrorCode;
import com.didapinche.trade.infrastructure.service.impl.TradeAmountDetailsModelService;
import com.didapinche.trade.infrastructure.service.impl.TradeBillModelService;
import com.didapinche.trade.infrastructure.service.impl.TradeBillOrderMappingService;
import com.didapinche.trade.infrastructure.service.impl.TradeOrderModelService;
import com.didapinche.trade.infrastructure.service.impl.TradeProductDetailsModelService;
import com.didapinche.trade.infrastructure.tbl.TradeBillModel;
import com.didapinche.trade.infrastructure.tbl.TradeBillOrderMappingModel;
import com.didapinche.trade.infrastructure.tbl.TradeOrderModel;
import com.didapinche.trade.infrastructure.thread.SleepContinueCallLate;
import com.didapinche.trade.infrastructure.thread.CustomerThreadFactory;
import com.didapinche.trade.infrastructure.thrift.RoutingThriftSupportService;
import com.didapinche.trade.infrastructure.transaction.TransactionService;
import com.didapinche.trade.infrastructure.util.BigDecimalUtil;
import com.didapinche.trade.infrastructure.util.TradeDateUtil;
import com.didapinche.trade.thrift.entities.TCleanOrderDataRequest;
import com.didapinche.trade.thrift.entities.TradeQueryCommonResponse;
import com.didapinche.trade.thrift.enums.TBusinessEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.LongAdder;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.didapinche.trade.infrastructure.constants.TradeConstants.CARPOOL_ORDER_FREE_BUSINESS_TYPE;
import static com.didapinche.trade.infrastructure.util.BigDecimalUtil.defValue;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CleanOrderDataService {

    private static final String CLEAN_ORDER_DATA_KEY = "clean_order_data_key";
    public static final String TRADE_BILL_CLEAR_ALL_SWITCH = "trade.bill.clear.all.switch";

    private static final LongAdder successCount = new LongAdder();
    private static final LongAdder failCount = new LongAdder();
    private static final LongAdder ignoreCount = new LongAdder();

    public static final List<String> NotBus = Arrays.asList(TBusinessEnum.carpool_highway.name(), TBusinessEnum.carpool_addition.name(), TBusinessEnum.carpool_additional.name());
    public static final List<String> RePayBus = Arrays.asList(TBusinessEnum.carpool_repeatpay.name(), TBusinessEnum.taxi_repeatpay.name(), TBusinessEnum.taxipooling_repeatpay.name());

    private static final Integer CORE_POOL_SIZE = Runtime.getRuntime().availableProcessors() * 2;
    private static final Integer MAX_POOL_SIZE = CORE_POOL_SIZE;

//    private static final List<String> dev_lookupId = Arrays.asList("0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "14", "15", "16", "17", "19", "20", "21", "23", "24", "26", "26", "30", "33", "34", "35", "38", "40", "43", "49");
//    private static final List<String> online_lookupId = Arrays.asList("0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "14", "15", "16", "17", "19", "20", "21", "23", "24", "26", "26", "30", "33", "34", "35", "38", "40", "43", "49");
//    private static final List<String> aliyun_lookupId = Arrays.asList("0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "14", "15", "16", "17", "19", "20", "21", "23", "24", "26", "26", "30", "33", "34", "35", "38", "40", "41", "43", "49");

    ThreadPoolExecutor pool = new ThreadPoolExecutor(CORE_POOL_SIZE,
            MAX_POOL_SIZE,
            0L,
            TimeUnit.SECONDS, // 时间单位
            new LinkedBlockingQueue<>(1000), // 等待队列
            new CustomerThreadFactory("clear"), // 线程工厂
            new SleepContinueCallLate() // 拒绝策略
    );

    @Value("${clean.test.enable:false}")
    boolean testEnable;


    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RoutingThriftSupportService routingThriftService;
    @Resource
    protected TradeOrderModelService tradeOrderModelService;
    @Resource
    protected TradeProductDetailsModelService tradeProductDetailsModelService;
    @Resource
    protected TradeAmountDetailsModelService tradeAmountDetailsModelService;
    @Resource
    private RemittanceOrderRepository remittanceOrderRepository;
    @Resource
    private TradeBillModelService tradeBillModelService;
    @Resource
    private TradeBillOrderMappingService tradeBillOrderMappingService;
    @Resource
    private TransactionService transactionService;
    @Resource
    private OfflinereadThriftService.Iface offlineReadThriftService;
    @Autowired
    PayTypeTagEventCustom payTypeTagEventCustom;

    List<String> dbRoutingList;

    public CleanOrderDataService(@Qualifier("dynamicDataSourceProperties") DynamicDataSourceProperties properties) {
        Map<String, List<String>> datasourceShardingSuffix = properties.getDatasourceShardingSuffix();
        dbRoutingList = datasourceShardingSuffix.entrySet().stream().filter(ds -> "order".equals(ds.getKey())).map(ds -> ds.getValue()).flatMap(List::stream).collect(Collectors.toList());
        log.info("dbRoutingList:{}", dbRoutingList);
    }

    /**
     * 清洗数据
     * 清洗类型
     * 1.时间区间
     * 2.商品维度
     * 3.用户维度+时间区间
     */
    public TradeQueryCommonResponse cleanOrderData(TCleanOrderDataRequest request) {
        log.info("cleanOrderData start. request:{}", JsonMapper.toJson(request));
        TradeQueryCommonResponse response = new TradeQueryCommonResponse().setCode(0);
        if (!validateRequest(request)) {
            return response.setCode(TradeErrorCode.PARAM_ERROR.getCode()).setMessage(TradeErrorCode.PARAM_ERROR.getMsg());
        }
        int type = request.getType();
        log.info("cleanOrderData 参数校验通过,进行下一步");
        //判断是否正在操作标识
        RBucket<Object> bucket = redissonClient.getBucket(RedisKeyConstants.BASE_GROUP_KEY + CLEAN_ORDER_DATA_KEY);
        if (bucket.isExists()) {
            return response.setCode(TradeErrorCode.PARAM_ERROR.getCode()).setMessage("正在操作中，请稍后重试");
        }
        try {
            //增加正在操作标识
            bucket.set(1, 3, TimeUnit.DAYS);
            log.info("cleanOrderData 并发控制判断通过,进行下一步");
            //通过类型走不同清洗方法
            if (type == 1) {
                cleanByDate(request.getStartDate(), request.getEndDate());
            } else if (type == 2) {
                cleanByMajorProductId(request.getMajorProductId(), request.getBusinessType(), request.getUserId(), false);
            } else if (type == 3) {
                cleanByUserCid(request.getStartDate(), request.getEndDate(), request.getUserId(), request.getUserCid());
            } else if (type == 4) {
                cleanTest();
            } else if (type == 5) {
                cleanByDateAndBill(request.getStartDate(), request.getEndDate());
            } else if (type == 6) {
                cleanByMajorProductId(request.getMajorProductId(), request.getBusinessType(), request.getUserId(), true);
            } else if (type == 7) {
                cleanByDateAndBusinessType(request.getStartDate(), request.getEndDate());
            }
            HashMap<String, Long> retMap = new HashMap<>();
            retMap.put("successCount", successCount.longValue());
            retMap.put("failCount", failCount.longValue());
            retMap.put("ignoreCount", ignoreCount.longValue());
            log.info("cleanOrderData end. request:{},retMap:{}", JsonMapper.toJson(request), JsonMapper.toJson(retMap));
            response.setRet(JsonMapper.toJson(retMap));
        } finally {
            //删除正在操作标识
            bucket.delete();
        }
        return response;
    }

    /**
     *
     * @param startDate yyyy-MM-dd HH:mm:ss
     * @param endDate yyyy-MM-dd HH:mm:ss
     */
    private void cleanByDateAndBusinessType(String startDate, String endDate) {
        log.info("cleanByDateAndBusinessType.时间区间 start. startDate:{},endDate:{}", startDate, endDate);
        //转换日期
        Date start;
        Date end;
        try {
            start = DateUtil.String2Date3(startDate);
            end = DateUtil.String2Date3(endDate);
        } catch (Exception e) {
            log.error("cleanByDateAndBusinessType.时间区间 start. startDate:{},endDate:{}", startDate, endDate, e);
            return;
        }
        Long minId;
        String size;
        long count = 0;
        long tableCount;
        List<TradeOrderModel> tradeOrderModels = new ArrayList<>(1000);
        Long sleepTime = LoadPropertyUtil.getProperty("trade.bill.clear.sleep.time", Long.class, 0L);
        String property = LoadPropertyUtil.getProperty("clear_include_biz_type", "c_vio_multi_deduction");
        List<String> bizTypeList = Arrays.asList(property.split(","));
        //需要遍历32个库，16张order表 多线程
        for (String dbRouting : dbRoutingList) {
            if (Boolean.TRUE.equals(LoadPropertyUtil.getProperty(TRADE_BILL_CLEAR_ALL_SWITCH, Boolean.class, false))) {
                log.info("清洗总开关关闭. 停止清洗 cleanByDate");
                return;
            }
            Routing routing = new Routing().setLookupId(dbRouting);
            for (int j = 0; j < 16; j++) {
                if (Boolean.TRUE.equals(LoadPropertyUtil.getProperty(TRADE_BILL_CLEAR_ALL_SWITCH, Boolean.class, false))) {
                    log.info("清洗总开关关闭. 停止清洗 cleanByDate2");
                    return;
                }
                //重置数据
                minId = 0L;
                tableCount = 0;
                routing.setDynamicTableSuffix(j == 0 ? "" : "_" + j);
                DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.SLAVE, DatabaseEnum.order, routing);
                log.info("cleanByDateAndBusinessType:routing:{}", JsonMapper.toJson(routing));
                //查询时间范围内的订单 分页遍历
                //查业务类型不包含carpool_highway carpool_addition carpool_additional的订单（这种都属于顺风车的补充支付）
                while (true) {
                    if (Boolean.TRUE.equals(LoadPropertyUtil.getProperty(TRADE_BILL_CLEAR_ALL_SWITCH, Boolean.class, false))) {
                        log.info("清洗总开关关闭. 停止清洗 while-true循环.cleanByDate3");
                        return;
                    }
                    size = LoadPropertyUtil.getProperty("clear.data.limit.size", String.class, "100");
                    tradeOrderModels.clear();
                    tradeOrderModels = tradeOrderModelService.selectListByCreateTimeAndBusinessTypes(start, end, bizTypeList, size, minId);
                    if (CollectionUtils.isEmpty(tradeOrderModels)) {
                        //跳出循环
                        break;
                    }
                    count += tradeOrderModels.size();
                    tableCount += tradeOrderModels.size();
                    minId = tradeOrderModels.get(tradeOrderModels.size() - 1).getId();
                    //通过线程池处理order
                    for (TradeOrderModel order : tradeOrderModels) {
                        log.debug("清洗数据. userId:{},orderId:{},dbRouting:{},suffix:{}", order.getUserId(), order.getId(), dbRouting, j);
                        pool.submit(() -> {
                                    DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.SLAVE, DatabaseEnum.order, routing);
                                    TradeOrderDO tradeOrderDO = new TradeOrderDO(order);
                                    ConficateLiquidationMessage conficateLiquidationMessage = new ConficateLiquidationMessage();
                                    try {
                                        conficateLiquidationMessage.setTradeOrderDO(tradeOrderDO)
                                                .send();
                                    } catch (Exception e) {
                                        log.warn("清洗数据失败. userId:{},orderId:{},dbRouting:{}", order.getUserId(), order.getId(), dbRouting);
                                    }
                                }
                                );
                    }
                    if (sleepTime > 0) {
                        try {
                            TimeUnit.MILLISECONDS.sleep(sleepTime);
                        } catch (InterruptedException e) {
                            log.error("error. dbRouting:{},suffix:{}", dbRouting, j, e);
                            Thread.currentThread().interrupt();
                        }
                    }
                }
                log.info("单表已处理完数据. routing:{},tableCount:{}", JsonMapper.toJson(routing), tableCount);
            }
        }
        log.info("cleanByDateAndBusinessType.时间区间 end. count:{}", count);
    }


    private void cleanByDateAndBill(String startDate, String endDate) {
        //查询order表没有billId的
        //查bill表里面是否存在，不存在，直接插入
        //存在且只有一条，查询mapping表中记录，更新order表
        //存在多条，直接全删除，重新生成bill记录
        log.info("cleanByDateAndBill start. startDate:{},endDate:{}", startDate, endDate);
        //转换日期
        Date start;
        Date end;
        try {
            start = DateUtil.String2Date3(startDate);
            end = DateUtil.String2Date3(endDate);
        } catch (Exception e) {
            log.error("cleanByDateAndBill start. startDate:{},endDate:{}", startDate, endDate, e);
            return;
        }
        Long minId;
        String size;
        long count = 0;
        long tableCount;
        List<TradeOrderModel> tradeOrderModels = new ArrayList<>(1000);
        Long sleepTime = LoadPropertyUtil.getProperty("trade.bill.clear.sleep.time", Long.class, 0L);
        Boolean billNullSwitch = LoadPropertyUtil.getProperty("trade.bill.clear.query.bill.null.switch", Boolean.class, true);
        //需要遍历32个库，16张order表 多线程
        for (String dbRouting : dbRoutingList) {
            if (Boolean.TRUE.equals(LoadPropertyUtil.getProperty(TRADE_BILL_CLEAR_ALL_SWITCH, Boolean.class, false))) {
                log.info("清洗总开关关闭. 停止清洗 cleanByDate");
                return;
            }
            Routing routing = new Routing().setLookupId(dbRouting);
            for (int j = 0; j < 16; j++) {
                if (Boolean.TRUE.equals(LoadPropertyUtil.getProperty(TRADE_BILL_CLEAR_ALL_SWITCH, Boolean.class, false))) {
                    log.info("清洗总开关关闭. 停止清洗 cleanByDate2");
                    return;
                }
                //重置数据
                minId = 0L;
                tableCount = 0;
                routing.setDynamicTableSuffix(j == 0 ? "" : "_" + j);
                DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.SLAVE, DatabaseEnum.order, routing);
                log.info("clearData:routing:{}", JsonMapper.toJson(routing));
                //查询时间范围内的订单 分页遍历
                //查业务类型不包含carpool_highway carpool_addition carpool_additional的订单（这种都属于顺风车的补充支付）
                while (true) {
                    if (Boolean.TRUE.equals(LoadPropertyUtil.getProperty(TRADE_BILL_CLEAR_ALL_SWITCH, Boolean.class, false))) {
                        log.info("清洗总开关关闭. 停止清洗 while-true循环.cleanByDate3");
                        return;
                    }
                    size = LoadPropertyUtil.getProperty("clear.data.limit.size", String.class, "100");
                    tradeOrderModels.clear();
                    if (billNullSwitch) {
                        tradeOrderModels = tradeOrderModelService.selectListByCreateTimeAndNotBillId(start, end, NotBus, size, minId);
                    } else {
                        tradeOrderModels = tradeOrderModelService.selectListByCreateTime(start, end, NotBus, size, minId);
                    }
                    if (CollectionUtils.isEmpty(tradeOrderModels)) {
                        //跳出循环
                        break;
                    }
                    count += tradeOrderModels.size();
                    tableCount += tradeOrderModels.size();
                    minId = tradeOrderModels.get(tradeOrderModels.size() - 1).getId();
                    for (TradeOrderModel updateOrder : tradeOrderModels) {
                        log.debug("清洗数据. orderId:{},dbRouting:{},suffix:{}", updateOrder.getMajorProductId(), dbRouting, j);
                        pool.submit(() -> clearByPaymentOrderNotBillId(updateOrder, dbRouting));
                    }
                    if (sleepTime > 0) {
                        try {
                            TimeUnit.MILLISECONDS.sleep(sleepTime);
                        } catch (InterruptedException e) {
                            log.error("error. dbRouting:{},suffix:{}", dbRouting, j, e);
                            Thread.currentThread().interrupt();
                        }
                    }
                }
                log.info("单表已处理完数据. routing:{},tableCount:{}", JsonMapper.toJson(routing), tableCount);
            }
        }
        log.info("cleanByDateAndBill end. count:{}", count);
    }

    private void cleanTest() {
        for (int i = 0; i < 100; i++) {
            int finalI = i;
            pool.submit(() -> {
                try {
                    long threadId = getThreadId(Thread.currentThread().getName());
                    log.info("cleanOrderData test:{}, threadId:{},name:{}", finalI, threadId, Thread.currentThread().getName());
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
    }


    private boolean validateRequest(TCleanOrderDataRequest request) {
        // 定义校验逻辑的函数式接口
        Function<TCleanOrderDataRequest, Boolean> type1Validator = r ->
                StringUtils.isNotBlank(r.getStartDate()) && StringUtils.isNotBlank(r.getEndDate());

        Function<TCleanOrderDataRequest, Boolean> type2Validator = r ->
                StringUtils.isNotBlank(r.getMajorProductId())
                        && StringUtils.isNotBlank(r.getBusinessType()) && r.getUserId() >= 0;

        Function<TCleanOrderDataRequest, Boolean> type3Validator = r ->
                StringUtils.isNotBlank(r.getStartDate()) && StringUtils.isNotBlank(r.getEndDate())
                        && StringUtils.isNotBlank(r.getUserCid()) && r.getUserId() >= 0;

        Function<TCleanOrderDataRequest, Boolean> type4Validator = r -> true;

        // 根据类型应用不同的校验逻辑
        Function<TCleanOrderDataRequest, Boolean> validator;
        switch (request.getType()) {
            case 1:
            case 5:
            case 7:
                validator = type1Validator;
                break;
            case 2:
            case 6:
                validator = type2Validator;
                break;
            case 3:
                validator = type3Validator;
                break;
            case 4:
                validator = type4Validator;
                break;
            default:
                return false;
        }
        // 执行校验，如果校验失败则返回错误响应
        return validator.apply(request);
    }

    private void cleanByUserCid(String startDate, String endDate, long userId, String userCid) {
        log.info("cleanOrderData.用户维度+时间区间 start. userCid:{}", userCid);
        //用户库和0库（周卡订单）
    }


    /**
     * 时间区间
     *
     * @param startDate -
     * @param endDate   -
     */
    private void cleanByDate(String startDate, String endDate) {
        log.info("cleanOrderData.时间区间 start. startDate:{},endDate:{}", startDate, endDate);
        //转换日期
        Date start;
        Date end;
        try {
            start = DateUtil.String2Date3(startDate);
            end = DateUtil.String2Date3(endDate);
        } catch (Exception e) {
            log.error("cleanOrderData.时间区间 start. startDate:{},endDate:{}", startDate, endDate, e);
            return;
        }
        Long minId;
        String size;
        long count = 0;
        long tableCount;
        List<TradeOrderModel> tradeOrderModels = new ArrayList<>(1000);
        Long sleepTime = LoadPropertyUtil.getProperty("trade.bill.clear.sleep.time", Long.class, 0L);
        //需要遍历32个库，16张order表 多线程
        for (String dbRouting : dbRoutingList) {
            if (Boolean.TRUE.equals(LoadPropertyUtil.getProperty(TRADE_BILL_CLEAR_ALL_SWITCH, Boolean.class, false))) {
                log.info("清洗总开关关闭. 停止清洗 cleanByDate");
                return;
            }
            Routing routing = new Routing().setLookupId(dbRouting);
            for (int j = 0; j < 16; j++) {
                if (Boolean.TRUE.equals(LoadPropertyUtil.getProperty(TRADE_BILL_CLEAR_ALL_SWITCH, Boolean.class, false))) {
                    log.info("清洗总开关关闭. 停止清洗 cleanByDate2");
                    return;
                }
                //重置数据
                minId = 0L;
                tableCount = 0;
                routing.setDynamicTableSuffix(j == 0 ? "" : "_" + j);
                DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.SLAVE, DatabaseEnum.order, routing);
                log.info("clearData:routing:{}", JsonMapper.toJson(routing));
                //查询时间范围内的订单 分页遍历
                //查业务类型不包含carpool_highway carpool_addition carpool_additional的订单（这种都属于顺风车的补充支付）
                while (true) {
                    if (Boolean.TRUE.equals(LoadPropertyUtil.getProperty(TRADE_BILL_CLEAR_ALL_SWITCH, Boolean.class, false))) {
                        log.info("清洗总开关关闭. 停止清洗 while-true循环.cleanByDate3");
                        return;
                    }
                    size = LoadPropertyUtil.getProperty("clear.data.limit.size", String.class, "100");
                    tradeOrderModels.clear();
                    tradeOrderModels = tradeOrderModelService.selectListByCreateTime(start, end, NotBus, size, minId);
                    if (CollectionUtils.isEmpty(tradeOrderModels)) {
                        //跳出循环
                        break;
                    }
                    count += tradeOrderModels.size();
                    tableCount += tradeOrderModels.size();
                    minId = tradeOrderModels.get(tradeOrderModels.size() - 1).getId();
                    List<ClearPaymentOrder> clearPaymentOrders = createClearPaymentOrder(tradeOrderModels);
                    //通过线程池处理order
                    for (ClearPaymentOrder order : clearPaymentOrders) {
                        log.debug("清洗数据. orderId:{},dbRouting:{},suffix:{}", order.getMajorProductId(), dbRouting, j);
                        pool.submit(() -> clearByPaymentOrder(order, dbRouting));
                    }
                    if (sleepTime > 0) {
                        try {
                            TimeUnit.MILLISECONDS.sleep(sleepTime);
                        } catch (InterruptedException e) {
                            log.error("error. dbRouting:{},suffix:{}", dbRouting, j, e);
                            Thread.currentThread().interrupt();
                        }
                    }
                }
                log.info("单表已处理完数据. routing:{},tableCount:{}", JsonMapper.toJson(routing), tableCount);
            }
        }
        log.info("cleanOrderData.时间区间 end. count:{}", count);
    }

    private void cleanByMajorProductId(String majorProductId, String businessType, long userId, boolean delBill) {
        log.info("cleanOrderData.商品维度 start. majorProductId:{}, userId:{}, businessType:{}", majorProductId, userId, businessType);
        if (Boolean.TRUE.equals(LoadPropertyUtil.getProperty(TRADE_BILL_CLEAR_ALL_SWITCH, Boolean.class, false))) {
            log.info("清洗总开关关闭. 停止清洗 createClearPaymentOrder");
            return;
        }
        long start = System.currentTimeMillis();
        //根据商品id查询路由分库id
        Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(majorProductId));
        DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.SLAVE, DatabaseEnum.order, routing);
        //根据路由分库id查询支付订单表
        List<TradeOrderModel> paymentTradeOrders = tradeOrderModelService.querySuccessByMajorProductId(majorProductId, TradeTypeEnum.payment.name(), businessType, userId);
        //判断订单
        if (CollectionUtils.isEmpty(paymentTradeOrders)) {
            log.info("cleanOrderData.商品维度. 没查到支付成功的订单. end. majorProductId:{}", majorProductId);
            return;
        }
        //清洗数据
        if (delBill) {
            clearByPaymentOrderAndDelBill(createClearPaymentOrder(paymentTradeOrders).get(0), routing.getLookupId());
        } else {
            clearByPaymentOrder(createClearPaymentOrder(paymentTradeOrders).get(0), routing.getLookupId());
        }
        long end = System.currentTimeMillis();
        log.info("cleanByMajorProductId. one data time:{}", end - start);
    }

    private void clearByPaymentOrderAndDelBill(ClearPaymentOrder paymentOrder, String lookupId) {
        long start = System.currentTimeMillis();
        //总开关
        if (Boolean.TRUE.equals(LoadPropertyUtil.getProperty(TRADE_BILL_CLEAR_ALL_SWITCH, Boolean.class, false))) {
            log.info("清洗总开关关闭. 停止清洗 clearByPaymentOrder");
            return;
        }
        if (paymentOrder == null) {
            return;
        }
        Long paymentOrderId = paymentOrder.getId();
        Long userId = paymentOrder.getUserId();
        long threadId = getThreadId(Thread.currentThread().getName());
        try {
            Routing routing = new Routing().setLookupId(lookupId);
            DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.SLAVE, DatabaseEnum.order, routing);
            List<TradeBillModel> tradeBillModels = tradeBillModelService.queryOrderBillListByProductId(paymentOrder.getMajorProductId(), paymentOrder.getCreateTime(), BizLineMapper.getBizLineEnum(paymentOrder.getBusinessType()));
            if (log.isDebugEnabled()) {
                log.debug("clearByPaymentOrderNotBillId. tradeBillModels:{}", JsonMapper.toJson(tradeBillModels));
            }
            if (CollectionUtils.isNotEmpty(tradeBillModels)) {
                //删除后重新写
                DynamicDataSourceContextHolder.modifyDatabaseType(DatabaseTypeEnum.MASTER);
                transactionService.executeAccount(() -> {
                    for (TradeBillModel delBill : tradeBillModels) {
                        tradeBillModelService.delById(delBill.getId());
                        List<TradeBillOrderMappingModel> delMappings = tradeBillOrderMappingService.queryListByBillId(delBill.getId());
                        if (CollectionUtils.isNotEmpty(delMappings)) {
                            tradeBillOrderMappingService.delById(delBill.getId(), delMappings.stream().map(TradeBillOrderMappingModel::getId).collect(Collectors.toList()));
                        }
                    }
                });
                DynamicDataSourceContextHolder.modifyDatabaseType(DatabaseTypeEnum.SLAVE);
            }
            //排除该行程是重复支付退款
            if (repayOrder(paymentOrderId, userId)) {
                ignoreCount.add(1);
                return;
            }
            //bill_id生成需要根据业务发生时间生成
            int suffix = (int) TableSuffixUtil.getSuffix(userId);
            long billId = getId(paymentOrder.getCreateTime(), lookupId, suffix, threadId);
            Date now = new Date();
            //创建账单信息
            TradeBillModel bill = createTradeBillModel(paymentOrder, billId, now);
            TradeBillOrderMappingModel billMapping = createBillMapping(paymentOrder, billId, now, lookupId, suffix, threadId);
            List<TradeBillOrderMappingModel> billMappingList = new ArrayList<>();
            //乘客各种支付
            List<TradeOrderModel> updateUserIdOrderList = new ArrayList<>();
            TradeOrderModel updatePaymentOrder = new TradeOrderModel();
            updatePaymentOrder.setId(paymentOrderId);
            updatePaymentOrder.setUserId(userId);
            updatePaymentOrder.setUpdateTime(now);
            updatePaymentOrder.setBillId(billId);
            updateUserIdOrderList.add(updatePaymentOrder);
            //车主
            List<TradeOrderModel> updateDriverIdOrderList = new ArrayList<>();
            billMappingList.add(billMapping);
            //补充支付的信息更新
            rideAddPaymentUpdate(bill, paymentOrder, updateUserIdOrderList, now, billMappingList, lookupId, suffix, threadId, updateDriverIdOrderList);
            //查询乘客的退款信息
            queryAndCalculateOrder(bill, billMappingList, paymentOrderId, userId, TradeTypeEnum.refund.name(), false,
                    paymentOrder.getBusinessType(), lookupId, suffix, threadId, now, updateUserIdOrderList, updateDriverIdOrderList);
            if (bill.getPayeeId() != null && bill.getPayeeId() > 0) {
                //查询车主的到账信息 顺风车的补贴
                queryAndCalculateOrder(bill, billMappingList, paymentOrderId, bill.getPayeeId(), TradeTypeEnum.transfer.name(), true,
                        paymentOrder.getBusinessType(), lookupId, suffix, threadId, now, updateUserIdOrderList, updateDriverIdOrderList);
                //查询车主的没收信息
                queryAndCalculateOrder(bill, billMappingList, paymentOrderId, bill.getPayeeId(), TradeTypeEnum.confiscate.name(), true,
                        paymentOrder.getBusinessType(), lookupId, suffix, threadId, now, updateUserIdOrderList, updateDriverIdOrderList);
                //查询顺风车的免单信息
                if (TBusinessEnum.carpool.name().equals(paymentOrder.getBusinessType())) {
                    queryAndCalculateFreeOrder(bill, billMappingList, paymentOrderId, bill.getPayeeId(), lookupId, suffix, threadId, now);
                }
            }
            //总开关
            if (Boolean.TRUE.equals(LoadPropertyUtil.getProperty(TRADE_BILL_CLEAR_ALL_SWITCH, Boolean.class, false))) {
                log.info("清洗总开关关闭. 停止清洗入库");
                return;
            }
            //插入bill 需要走主库加事务
            DynamicDataSourceContextHolder.modifyDatabaseType(DatabaseTypeEnum.MASTER);
            transactionService.executeAccount(() -> {
                tradeOrderModelService.updateListById(updateUserIdOrderList);
                tradeOrderModelService.updateListById(updateDriverIdOrderList);
                tradeBillModelService.saveBillRecord(bill);
                tradeBillOrderMappingService.saveRecordList(billMappingList);
                PayTypeTagData data = new PayTypeTagData(userId, billId);
                Pair<InteractionResult, String> interactionResult = payTypeTagEventCustom.selectPayTypeTag(data);
                if (interactionResult != null && StringUtils.isNotBlank(interactionResult.getValue())) {
                    updateTradeBillPayTypeTage(userId, billId, interactionResult.getValue());
                }
            });
            successCount.add(1);
        } catch (Exception e) {
            log.error("clearByPaymentOrderAndDelBill error. paymentOrderId:{},userId:{},threadId:{},name:{}", paymentOrderId, userId, threadId, Thread.currentThread().getName(), e);
            failCount.add(1);
        }
        long end = System.currentTimeMillis();
        log.debug("clearByPaymentOrderAndDelBill one data time:{}", end - start);

    }

    /**
     * 组装清洗数据参数和查询行程关联的司机id和补充支付id
     *
     * @param paymentTradeOrders -
     * @return -
     */
    private List<ClearPaymentOrder> createClearPaymentOrder(List<TradeOrderModel> paymentTradeOrders) {
        if (Boolean.TRUE.equals(LoadPropertyUtil.getProperty(TRADE_BILL_CLEAR_ALL_SWITCH, Boolean.class, false))) {
            log.info("清洗总开关关闭. 停止清洗 createClearPaymentOrder");
            return new ArrayList<>();
        }
        //行程需要调用行程接口，查询是否存在感谢费，高速费，服务费，有没有补贴，补贴是多少钱，查到有补充支付， id之后再查询订单系统
        List<RideInfoParam> rideInfoParams = paymentTradeOrders.stream().filter(tradeOrderModel -> StringUtils.equalsAny(tradeOrderModel.getBusinessType(),
                TBusinessEnum.taxi.name(),
                TBusinessEnum.t_enterprise.name(),
                TBusinessEnum.t_enterprise_vip.name(),
                TBusinessEnum.t_substitute_pay.name(),
                TBusinessEnum.t_together_pay.name(),
                TBusinessEnum.t_offline.name(),
                TBusinessEnum.carpool.name(),
                TBusinessEnum.c_enterprise_vip.name())
        ).map(tradeOrderModel -> {
            RideInfoParam rideInfoParam = new RideInfoParam();
            rideInfoParam.setRideId(Long.parseLong(tradeOrderModel.getMajorProductId()));
            rideInfoParam.setBusinessType(tradeOrderModel.getBusinessType());
            return rideInfoParam;
        }).collect(Collectors.toList());
        Map<String, com.didapinche.thrift.offlineread.RideInfo> rideInfoMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(rideInfoParams)) {
            TQueryRideInfoByClearDataRequest request = new TQueryRideInfoByClearDataRequest().setRideInfoParams(rideInfoParams);
            TQueryRideInfoByClearDataResponse response;
            try {
                response = offlineReadThriftService.queryRideInfoByClearData(request);
                if (response.getCode() != 0) {
                    response = offlineReadThriftService.queryRideInfoByClearData(request);
                }
                if (response.getCode() == 0) {
                    rideInfoMap = response.getInfos();
                }
            } catch (Exception e) {
                try {
                    response = offlineReadThriftService.queryRideInfoByClearData(request);
                    if (response.getCode() == 0) {
                        rideInfoMap = response.getInfos();
                    }
                } catch (TException ex) {
                }
            }
        }
        //遍历paymentTradeOrders转为List<RideInfoParam>
        Map<String, com.didapinche.thrift.offlineread.RideInfo> finalRideInfoMap = rideInfoMap;
        return paymentTradeOrders.stream().map(paymentOrder -> {
            ClearPaymentOrder clearPaymentOrder = new ClearPaymentOrder();
            clearPaymentOrder.setId(paymentOrder.getId());
            clearPaymentOrder.setMajorProductId(paymentOrder.getMajorProductId());
            clearPaymentOrder.setUserId(paymentOrder.getUserId());
            clearPaymentOrder.setBusinessType(paymentOrder.getBusinessType());
            clearPaymentOrder.setCreateTime(paymentOrder.getCreateTime());
            clearPaymentOrder.setTotalPrice(paymentOrder.getTotalPrice());
            clearPaymentOrder.setTradeType(paymentOrder.getTradeType());
            TradeOrderNote note = TradeOrderNote.create(paymentOrder.getNote());
            clearPaymentOrder.setHold(note.getHold());
            clearPaymentOrder.setSysOrderGroup(note.getSysOrderGroup());
            clearPaymentOrder.setDriverId(note.getDriverId());
            if (!finalRideInfoMap.isEmpty()) {
                //防止顺风车和出租车id一致情况
                String business = StringUtils.equalsAny(paymentOrder.getBusinessType(), TBusinessEnum.carpool.name(), TBusinessEnum.c_enterprise_vip.name()) ? TBusinessEnum.carpool.name() : TBusinessEnum.taxi.name();
                com.didapinche.thrift.offlineread.RideInfo rideInfo = finalRideInfoMap.get(paymentOrder.getMajorProductId() + business);
                if (rideInfo != null) {
                    clearPaymentOrder.setDriverId(rideInfo.getDriverId());
                    clearPaymentOrder.setPlatformCommission(AmountConvertUtil.yuan2BigDecimal(rideInfo.getCommission()));
                    AdditionInfo higWays = rideInfo.getHigways();
                    if (higWays != null && higWays.getPaymentId() > 0 && paymentOrder.getId() != higWays.getPaymentId()) {
                        clearPaymentOrder.setHighwayAmount(AmountConvertUtil.yuan2BigDecimal(higWays.getAmount()));
                        clearPaymentOrder.setHighwayPaymentId(higWays.getPaymentId());
                        clearPaymentOrder.setHighwayStatus(higWays.getStatus());
                    }
                    if (!CollectionUtils.isEmpty(rideInfo.getThanks())) {
                        clearPaymentOrder.setThanksList(rideInfo.getThanks().stream().filter(thanks -> thanks.getPaymentId() != paymentOrder.getId()).collect(Collectors.toList()));
                    }
                }
            }
            return clearPaymentOrder;
        }).collect(Collectors.toList());
    }


    /**
     * 减少查库查到的数据
     *
     * @param paymentOrder -
     */
    private void clearByPaymentOrder(ClearPaymentOrder paymentOrder, String lookupId) {
        long start = System.currentTimeMillis();
        //总开关
        if (Boolean.TRUE.equals(LoadPropertyUtil.getProperty(TRADE_BILL_CLEAR_ALL_SWITCH, Boolean.class, false))) {
            log.info("清洗总开关关闭. 停止清洗 clearByPaymentOrder");
            return;
        }
        if (paymentOrder == null) {
            return;
        }
        Long paymentOrderId = paymentOrder.getId();
        Long userId = paymentOrder.getUserId();
        long threadId = getThreadId(Thread.currentThread().getName());
        try {
            Routing routing = new Routing().setLookupId(lookupId);
            DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.SLAVE, DatabaseEnum.order, routing);
            if (testEnable) {
                DynamicDataSourceContextHolder.modifyDatabaseType(DatabaseTypeEnum.MASTER);
                //判断是否已存在
                if (tradeBillModelService.queryCountOrderBillByProductId(paymentOrder.getMajorProductId(), paymentOrder.getCreateTime(), BizLineMapper.getBizLineEnum(paymentOrder.getBusinessType())) > 0) {
                    log.debug("bill 已存在. productId:{}, buss:{}", paymentOrder.getMajorProductId(), paymentOrder.getBusinessType());
                    return;
                }
                // 查询order走从库
                DynamicDataSourceContextHolder.modifyDatabaseType(DatabaseTypeEnum.SLAVE);
            }
            //排除该行程是重复支付退款
            if (repayOrder(paymentOrderId, userId)) {
                ignoreCount.add(1);
                return;
            }
            //bill_id生成需要根据业务发生时间生成
            int suffix = (int) TableSuffixUtil.getSuffix(userId);
            long billId = getId(paymentOrder.getCreateTime(), lookupId, suffix, threadId);
            Date now = new Date();
            //创建账单信息
            TradeBillModel bill = createTradeBillModel(paymentOrder, billId, now);
            TradeBillOrderMappingModel billMapping = createBillMapping(paymentOrder, billId, now, lookupId, suffix, threadId);
            List<TradeBillOrderMappingModel> billMappingList = new ArrayList<>();
            //乘客各种支付
            List<TradeOrderModel> updateUserIdOrderList = new ArrayList<>();
            TradeOrderModel updatePaymentOrder = new TradeOrderModel();
            updatePaymentOrder.setId(paymentOrderId);
            updatePaymentOrder.setUserId(userId);
            updatePaymentOrder.setUpdateTime(now);
            updatePaymentOrder.setBillId(billId);
            updateUserIdOrderList.add(updatePaymentOrder);
            //车主
            List<TradeOrderModel> updateDriverIdOrderList = new ArrayList<>();
            billMappingList.add(billMapping);
            //补充支付的信息更新
            rideAddPaymentUpdate(bill, paymentOrder, updateUserIdOrderList, now, billMappingList, lookupId, suffix, threadId, updateDriverIdOrderList);
            //查询乘客的退款信息
            queryAndCalculateOrder(bill, billMappingList, paymentOrderId, userId, TradeTypeEnum.refund.name(), false,
                    paymentOrder.getBusinessType(), lookupId, suffix, threadId, now, updateUserIdOrderList, updateDriverIdOrderList);
            if (bill.getPayeeId() != null && bill.getPayeeId() > 0) {
                //查询车主的到账信息 顺风车的补贴
                queryAndCalculateOrder(bill, billMappingList, paymentOrderId, bill.getPayeeId(), TradeTypeEnum.transfer.name(), true,
                        paymentOrder.getBusinessType(), lookupId, suffix, threadId, now, updateUserIdOrderList, updateDriverIdOrderList);
                //查询车主的没收信息
                queryAndCalculateOrder(bill, billMappingList, paymentOrderId, bill.getPayeeId(), TradeTypeEnum.confiscate.name(), true,
                        paymentOrder.getBusinessType(), lookupId, suffix, threadId, now, updateUserIdOrderList, updateDriverIdOrderList);
                //查询顺风车的免单信息
                if (TBusinessEnum.carpool.name().equals(paymentOrder.getBusinessType())) {
                    queryAndCalculateFreeOrder(bill, billMappingList, paymentOrderId, bill.getPayeeId(), lookupId, suffix, threadId, now);
                }
            }
            //总开关
            if (Boolean.TRUE.equals(LoadPropertyUtil.getProperty(TRADE_BILL_CLEAR_ALL_SWITCH, Boolean.class, false))) {
                log.info("清洗总开关关闭. 停止清洗入库");
                return;
            }
            //插入bill 需要走主库加事务
            DynamicDataSourceContextHolder.modifyDatabaseType(DatabaseTypeEnum.MASTER);
            transactionService.executeAccount(() -> {
                tradeOrderModelService.updateListById(updateUserIdOrderList);
                tradeOrderModelService.updateListById(updateDriverIdOrderList);
                tradeBillModelService.saveBillRecord(bill);
                tradeBillOrderMappingService.saveRecordList(billMappingList);
                PayTypeTagData data = new PayTypeTagData(userId, billId);
                Pair<InteractionResult, String> interactionResult = payTypeTagEventCustom.selectPayTypeTag(data);
                if (interactionResult != null && StringUtils.isNotBlank(interactionResult.getValue())) {
                    updateTradeBillPayTypeTage(userId, billId, interactionResult.getValue());
                }
            });
            successCount.add(1);
        } catch (Exception e) {
            log.error("clearByPaymentOrder error. paymentOrderId:{},userId:{},threadId:{},name:{}", paymentOrderId, userId, threadId, Thread.currentThread().getName(), e);
            failCount.add(1);
        }
        long end = System.currentTimeMillis();
        log.debug("one data time:{}", end - start);
    }

    /**
     * 减少查库查到的数据
     *
     * @param paymentOrder -
     */
    private void clearByPaymentOrderNotBillId(TradeOrderModel paymentOrder, String lookupId) {
        long start = System.currentTimeMillis();
        //总开关
        if (Boolean.TRUE.equals(LoadPropertyUtil.getProperty(TRADE_BILL_CLEAR_ALL_SWITCH, Boolean.class, false))) {
            log.info("清洗总开关关闭. 停止清洗 clearByPaymentOrder");
            return;
        }
        if (paymentOrder == null) {
            return;
        }
        Long paymentOrderId = paymentOrder.getId();
        Long userId = paymentOrder.getUserId();
        long threadId = getThreadId(Thread.currentThread().getName());
        try {
            Routing routing = new Routing().setLookupId(lookupId);
            DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.SLAVE, DatabaseEnum.order, routing);
            //排除该行程是重复支付退款
            if (repayOrder(paymentOrderId, userId)) {
                ignoreCount.add(1);
                return;
            }
            if (paymentOrder.getBillId() != null) {
                TradeBillModel billModel = tradeBillModelService.queryOrderBillById(paymentOrder.getBillId());
                if (billModel != null) {
                    return;
                }
            }
            List<TradeBillModel> tradeBillModels = tradeBillModelService.queryOrderBillListByProductId(paymentOrder.getMajorProductId(), paymentOrder.getCreateTime(), BizLineMapper.getBizLineEnum(paymentOrder.getBusinessType()));
            if (log.isDebugEnabled()) {
                log.debug("clearByPaymentOrderNotBillId. tradeBillModels:{}", JsonMapper.toJson(tradeBillModels));
            }
            if (tradeBillModels == null || tradeBillModels.isEmpty()) {
                clearByPaymentOrder(createClearPaymentOrder(Collections.singletonList(paymentOrder)).get(0), routing.getLookupId());
                return;
            } else if (tradeBillModels.size() > 1) {
                DynamicDataSourceContextHolder.modifyDatabaseType(DatabaseTypeEnum.MASTER);
                transactionService.executeAccount(() -> {
                    //删除后重新写
                    for (TradeBillModel delBill : tradeBillModels) {
                        tradeBillModelService.delById(delBill.getId());
                        List<TradeBillOrderMappingModel> delMappings = tradeBillOrderMappingService.queryListByBillId(delBill.getId());
                        if (CollectionUtils.isNotEmpty(delMappings)) {
                            tradeBillOrderMappingService.delById(delBill.getId(), delMappings.stream().map(TradeBillOrderMappingModel::getId).collect(Collectors.toList()));
                        }
                    }
                });
                clearByPaymentOrder(createClearPaymentOrder(Collections.singletonList(paymentOrder)).get(0), routing.getLookupId());
                return;
            }
            TradeBillModel billModel = tradeBillModels.get(0);
            List<TradeBillOrderMappingModel> billMappings = tradeBillOrderMappingService.queryListByBillId(billModel.getId());
            Map<Long, List<TradeOrderModel>> groupOrders = billMappings.stream().map(mapping -> {
                TradeOrderModel orderModel = new TradeOrderModel();
                orderModel.setId(mapping.getTradeOrderId());
                orderModel.setUserId(mapping.getUserId());
                orderModel.setBillId(mapping.getBillId());
                orderModel.setUpdateTime(new Date());
                return orderModel;
            }).collect(Collectors.groupingBy(TradeOrderModel::getUserId));
            List<List<TradeOrderModel>> userGroupedLists = new ArrayList<>(groupOrders.values());
            if (log.isDebugEnabled()) {
                log.debug("clearByPaymentOrderNotBillId groupOrders:{}，userGroupedLists：{}", JsonMapper.toJson(groupOrders), JsonMapper.toJson(userGroupedLists));
            }
            //插入bill 需要走主库加事务
            DynamicDataSourceContextHolder.modifyDatabaseType(DatabaseTypeEnum.MASTER);
            transactionService.executeAccount(() -> {
                for (List<TradeOrderModel> updateOrders : userGroupedLists) {
                    tradeOrderModelService.updateListById(updateOrders);
                }
            });
            successCount.add(1);
        } catch (Exception e) {
            log.error("clearByPaymentOrder error. paymentOrderId:{},userId:{},threadId:{},name:{}", paymentOrderId, userId, threadId, Thread.currentThread().getName(), e);
            failCount.add(1);
        }
        long end = System.currentTimeMillis();
        log.debug("one data time:{}", end - start);
    }

    private boolean updateTradeBillPayTypeTage(Long payerId, Long billId, String payTypeTag) {
        TradeBillModel billModel = new TradeBillModel();
        billModel.setId(billId);
        billModel.setPayTypeTag(payTypeTag);
        LambdaUpdateWrapper<TradeBillModel> updateWrapper = Wrappers.<TradeBillModel>lambdaUpdate()
                .eq(TradeBillModel::getId, billId)
                .eq(TradeBillModel::getPayerId, payerId);
        return tradeBillModelService.updateTradeBillModel(billModel, updateWrapper);
    }

    private boolean repayOrder(Long paymentOrderId, Long userId) {
        //判断是否有重复支付退款的退款记录
        long count = tradeOrderModelService.selectRefundTradeCount(paymentOrderId, RePayBus, null, userId);
        return count > 0;
    }

    private void rideAddPaymentUpdate(TradeBillModel bill, ClearPaymentOrder clearPaymentOrder,
                                      List<TradeOrderModel> updateUserIdOrderList, Date now,
                                      List<TradeBillOrderMappingModel> billMappingList, String lookupId, int suffix, Long threadId,
                                      List<TradeOrderModel> updateDriverIdOrderList) {
        //高速费
        Long userId = clearPaymentOrder.getUserId();
        Long billId = bill.getId();
        Date date = bill.getCreateTime();
        if (clearPaymentOrder.getHighwayPaymentId() != null && clearPaymentOrder.getHighwayPaymentId() > 0) {
            bill.setPaymentAmount(bill.getPaymentAmount().add(clearPaymentOrder.getHighwayAmount()));
            bill.setPrice(bill.getPrice().add(clearPaymentOrder.getHighwayAmount()));
            TradeOrderModel highwayPaymentOrder = new TradeOrderModel();
            highwayPaymentOrder.setId(clearPaymentOrder.getHighwayPaymentId());
            highwayPaymentOrder.setUserId(userId);
            highwayPaymentOrder.setBusinessType(TBusinessEnum.carpool_highway.name());
            highwayPaymentOrder.setTradeType(TradeTypeEnum.payment.name());
            billMappingList.add(createBillMapping(highwayPaymentOrder, billId, date, lookupId, suffix, threadId, now));
            //支付单
            TradeOrderModel updateOrder = new TradeOrderModel();
            updateOrder.setId(clearPaymentOrder.getHighwayPaymentId());
            updateOrder.setUserId(userId);
            updateOrder.setUpdateTime(now);
            updateOrder.setBillId(billId);
            updateUserIdOrderList.add(updateOrder);
            //查询退款id
            List<TradeOrderModel> refundOrders = tradeOrderModelService.selectTradedOrderListByPayTradeNoAndUserIdAndTradeType(clearPaymentOrder.getHighwayPaymentId(), TradeTypeEnum.refund.name(), userId);
            if (CollectionUtils.isNotEmpty(refundOrders)) {
                BigDecimal amount = refundOrders.stream().map(TradeOrderModel::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                //最终支付金额 需根据退款情况入库
                bill.setPaymentAmount(bill.getPaymentAmount().subtract(amount));
                //支付又退款的金额 需根据退款情况入库
                bill.setPayerRefundAmount(bill.getPayerRefundAmount().add(amount));
                List<TradeBillOrderMappingModel> list = refundOrders.stream().map(order -> createBillMapping(order, billId, date, lookupId, suffix, threadId, now)).collect(Collectors.toList());
                //添加mapping
                billMappingList.addAll(list);
                updateUserIdOrderList.addAll(refundOrders.stream().map(refundOrder -> {
                    TradeOrderModel updateRefundOrder = new TradeOrderModel();
                    updateRefundOrder.setId(refundOrder.getId());
                    updateRefundOrder.setUserId(userId);
                    updateRefundOrder.setUpdateTime(now);
                    updateRefundOrder.setBillId(billId);
                    return updateRefundOrder;
                }).collect(Collectors.toList()));
            }
            //查询到账id
            if (bill.getPayeeId() != null && bill.getPayeeId() > 0) {
                List<TradeOrderModel> transferOrders = tradeOrderModelService.selectTradedOrderListByPayTradeNoAndUserIdAndTradeType(clearPaymentOrder.getHighwayPaymentId(), TradeTypeEnum.transfer.name(), bill.getPayeeId());
                if (CollectionUtils.isNotEmpty(transferOrders)) {
                    BigDecimal amount = transferOrders.stream().map(TradeOrderModel::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    bill.setReceiptsAmount(bill.getReceiptsAmount().add(amount));
                    List<TradeBillOrderMappingModel> list = transferOrders.stream().map(order -> createBillMapping(order, billId, date, lookupId, suffix, threadId, now)).collect(Collectors.toList());
                    //添加mapping
                    billMappingList.addAll(list);
                    updateDriverIdOrderList.addAll(transferOrders.stream().map(refundOrder -> {
                        TradeOrderModel updateRefundOrder = new TradeOrderModel();
                        updateRefundOrder.setId(refundOrder.getId());
                        updateRefundOrder.setUserId(bill.getPayeeId());
                        updateRefundOrder.setUpdateTime(now);
                        updateRefundOrder.setBillId(billId);
                        return updateRefundOrder;
                    }).collect(Collectors.toList()));
                }
                List<TradeOrderModel> confiscateOrders = tradeOrderModelService.selectTradedOrderListByPayTradeNoAndUserIdAndTradeType(clearPaymentOrder.getHighwayPaymentId(), TradeTypeEnum.confiscate.name(), bill.getPayeeId());
                if (CollectionUtils.isNotEmpty(confiscateOrders)) {
                    BigDecimal amount = confiscateOrders.stream().map(TradeOrderModel::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    bill.setReceiptsAmount(bill.getReceiptsAmount().subtract(amount));
                    bill.setPayeeReturnAmount(bill.getReceiptsAmount().add(amount));
                    List<TradeBillOrderMappingModel> list = confiscateOrders.stream().map(order -> createBillMapping(order, billId, date, lookupId, suffix, threadId, now)).collect(Collectors.toList());
                    //添加mapping
                    billMappingList.addAll(list);
                    updateDriverIdOrderList.addAll(confiscateOrders.stream().map(refundOrder -> {
                        TradeOrderModel updateRefundOrder = new TradeOrderModel();
                        updateRefundOrder.setId(refundOrder.getId());
                        updateRefundOrder.setUserId(bill.getPayeeId());
                        updateRefundOrder.setUpdateTime(now);
                        updateRefundOrder.setBillId(billId);
                        return updateRefundOrder;
                    }).collect(Collectors.toList()));
                }
            }
        }
        //感谢费
        if (CollectionUtils.isEmpty(clearPaymentOrder.getThanksList())) {
            return;
        }
        for (AdditionInfo thanks : clearPaymentOrder.getThanksList()) {
            if (thanks.getPaymentId() <= 0) {
                continue;
            }
            TradeOrderModel thanksPaymentOrder = new TradeOrderModel();
            thanksPaymentOrder.setId(thanks.getPaymentId());
            thanksPaymentOrder.setUserId(userId);
            thanksPaymentOrder.setBusinessType(TBusinessEnum.carpool_additional.name());
            thanksPaymentOrder.setTradeType(TradeTypeEnum.payment.name());
            billMappingList.add(createBillMapping(thanksPaymentOrder, billId, date, lookupId, suffix, threadId, now));
            TradeOrderModel updateOrder = new TradeOrderModel();
            updateOrder.setId(thanks.getPaymentId());
            updateOrder.setUserId(userId);
            updateOrder.setUpdateTime(now);
            updateOrder.setBillId(billId);
            updateUserIdOrderList.add(updateOrder);
            bill.setPaymentAmount(bill.getPaymentAmount().add(AmountConvertUtil.yuan2BigDecimal(thanks.getAmount())));
            bill.setPrice(bill.getPrice().add(AmountConvertUtil.yuan2BigDecimal(thanks.getAmount())));
            if (thanks.getStatus() != 3) {
                //查询感谢费退款id
                List<TradeOrderModel> orders = tradeOrderModelService.selectTradedOrderListByPayTradeNoAndUserIdAndTradeType(thanks.getPaymentId(), TradeTypeEnum.refund.name(), userId);
                if (CollectionUtils.isNotEmpty(orders)) {
                    BigDecimal amount = orders.stream().map(TradeOrderModel::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    //最终支付金额 需根据退款情况入库
                    bill.setPaymentAmount(bill.getPaymentAmount().subtract(amount));
                    //支付又退款的金额 需根据退款情况入库
                    bill.setPayerRefundAmount(bill.getPayerRefundAmount().add(amount));
                    List<TradeBillOrderMappingModel> list = orders.stream().map(order -> createBillMapping(order, billId, date, lookupId, suffix, threadId, now)).collect(Collectors.toList());
                    //添加mapping
                    billMappingList.addAll(list);
                    updateUserIdOrderList.addAll(orders.stream().map(refundOrder -> {
                        TradeOrderModel updateRefundOrder = new TradeOrderModel();
                        updateRefundOrder.setId(refundOrder.getId());
                        updateRefundOrder.setUserId(userId);
                        updateRefundOrder.setUpdateTime(now);
                        updateRefundOrder.setBillId(billId);
                        return updateRefundOrder;
                    }).collect(Collectors.toList()));
                }
            }
        }
    }

    private void queryAndCalculateFreeOrder(TradeBillModel bill, List<TradeBillOrderMappingModel> billMappingList, Long paymentOrderId,
                                            long driverId, String lookupId, int suffix, Long threadId, Date now) {
        List<RemittanceOrderModelDO> remittanceOrderModelDOS = remittanceOrderRepository.queryByBussinessTypeAndPayTradeNo(paymentOrderId, CARPOOL_ORDER_FREE_BUSINESS_TYPE);
        if (CollectionUtils.isEmpty(remittanceOrderModelDOS)) {
            return;
        }
        //免单只有一次
        RemittanceOrderModelDO remittanceOrderModelDO = remittanceOrderModelDOS.get(0);
        BigDecimal freeAmount = remittanceOrderModelDO.getSumFee();
        bill.setFreeTag(remittanceOrderModelDO.getBusinessType());
        bill.setFreeAmount(freeAmount);
        //到账金额减少
        bill.setReceiptsAmount(AmountConvertUtil.toDecimalPlaces(bill.getReceiptsAmount()).subtract(freeAmount));
        //收款人退回的钱
        bill.setPayeeReturnAmount(BigDecimalUtil.add(defValue(freeAmount), bill.getPayeeReturnAmount()));
        //更新付款人实际付的钱和退款金额
        bill.setPaymentAmount(bill.getPaymentAmount().subtract(defValue(defValue(freeAmount))));
        bill.setPayerRefundAmount(BigDecimalUtil.add(bill.getPayerRefundAmount(), defValue(freeAmount)));
        TradeOrderModel freeOrder = new TradeOrderModel();
        freeOrder.setId(remittanceOrderModelDO.getId());
        freeOrder.setUserId(driverId);
        freeOrder.setBusinessType(remittanceOrderModelDO.getBusinessType());
        freeOrder.setTradeType(TradeTypeEnum.refund.name());
        freeOrder.setCreateTime(remittanceOrderModelDO.getCreateTime());
        billMappingList.add(createBillMapping(freeOrder, bill.getId(), bill.getCreateTime(), lookupId, suffix, threadId, now));
    }

    private static TradeBillOrderMappingModel createBillMapping(ClearPaymentOrder order, Long billId, Date now, String lookupId, int suffix, Long threadId) {
        //插入bill_mapper
        TradeBillOrderMappingModel tradeBillOrderMapping = new TradeBillOrderMappingModel();
        tradeBillOrderMapping.setId(getId(order.getCreateTime(), lookupId, suffix, threadId));
        tradeBillOrderMapping.setBillId(billId);
        tradeBillOrderMapping.setTradeOrderId(order.getId());
        tradeBillOrderMapping.setUserId(order.getUserId());
        tradeBillOrderMapping.setBizType(order.getBusinessType() + "_" + order.getTradeType());
        tradeBillOrderMapping.setCreateTime(order.getCreateTime());
        tradeBillOrderMapping.setUpdateTime(now);
        return tradeBillOrderMapping;
    }

    private static TradeBillOrderMappingModel createBillMapping(TradeOrderModel order, Long billId, Date paymentDate,
                                                                String lookupId, int suffix, Long threadId,
                                                                Date now) {
        //插入bill_mapper
        TradeBillOrderMappingModel tradeBillOrderMapping = new TradeBillOrderMappingModel();
        tradeBillOrderMapping.setId(getId(paymentDate, lookupId, suffix, threadId));
        tradeBillOrderMapping.setBillId(billId);
        tradeBillOrderMapping.setTradeOrderId(order.getId());
        tradeBillOrderMapping.setUserId(order.getUserId());
        tradeBillOrderMapping.setBizType(order.getBusinessType() + "_" + order.getTradeType());
        tradeBillOrderMapping.setCreateTime(paymentDate);
        tradeBillOrderMapping.setUpdateTime(now);
        return tradeBillOrderMapping;
    }

    @NotNull
    private static TradeBillModel createTradeBillModel(ClearPaymentOrder paymentOrder, long billId, Date now) {
        TradeBillModel bill = new TradeBillModel();
        bill.setId(billId);
        bill.setProductId(paymentOrder.getMajorProductId());
        bill.setProductBiz(BizLineMapper.getBizLineEnum(paymentOrder.getBusinessType()));
        bill.setInitialPrice(paymentOrder.getTotalPrice());
        bill.setPrice(paymentOrder.getTotalPrice());
        bill.setPayerId(paymentOrder.getUserId());
        //最终支付金额
        bill.setPaymentAmount(paymentOrder.getTotalPrice());
        //支付又退款的金额
        bill.setPayerRefundAmount(BigDecimal.ZERO);
        bill.setPayTypeTag("");
        //车主id 顺风车订单未到账，不会存在该字段
        bill.setPayeeId(paymentOrder.getDriverId());
        //车主到账金额 需要根据到账情况入库
        bill.setReceiptsAmount(BigDecimal.ZERO);
        //到账又退回的金额 需要根据到账退款和罚没情况入库
        bill.setPayeeReturnAmount(BigDecimal.ZERO);
        //罚没金额 需要根据没收情况入库
        bill.setPayeePenalty(BigDecimal.ZERO);
        //默认0
        bill.setInvolvedCommission(BigDecimal.ZERO);
        //服务费
        bill.setPlatformCommission(paymentOrder.getPlatformCommission());
        bill.setPayState(TradeState.BillState.paid.name());
        bill.setTransferState("");
        //免单字段
        bill.setFreeTag("");
        bill.setFreeAmount(BigDecimal.ZERO);
        bill.setPend(String.valueOf(paymentOrder.getHold()));
        bill.setRiskTag(String.valueOf(paymentOrder.getSysOrderGroup()));
        bill.setVersion(1);
        bill.setCreateTime(TradeDateUtil.mill2Zero(paymentOrder.getCreateTime()));
        bill.setUpdateTime(TradeDateUtil.mill2Zero(now));
        return bill;
    }

    private void queryAndCalculateOrder(TradeBillModel bill, List<TradeBillOrderMappingModel> billMappingList,
                                        Long paymentOrderId, long userId, String tradeType, boolean isDriver,
                                        String businessType, String lookupId, int suffix, Long threadId,
                                        Date now, List<TradeOrderModel> updateUserIdOrderList, List<TradeOrderModel> updateDriverIdOrderList) {
        long billId = bill.getId();
        Date date = bill.getCreateTime();
        List<TradeOrderModel> orders = tradeOrderModelService.selectTradedOrderListByPayTradeNoAndUserIdAndTradeType(paymentOrderId, tradeType, userId);
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        BigDecimal amount = orders.stream().map(TradeOrderModel::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        List<TradeBillOrderMappingModel> list = orders.stream().map(order -> createBillMapping(order, billId, date, lookupId, suffix, threadId, now)).collect(Collectors.toList());
        //添加mapping
        billMappingList.addAll(list);
        List<TradeOrderModel> collect = orders.stream().map(refundOrder -> {
            TradeOrderModel updateRefundOrder = new TradeOrderModel();
            updateRefundOrder.setId(refundOrder.getId());
            updateRefundOrder.setUserId(userId);
            updateRefundOrder.setUpdateTime(now);
            updateRefundOrder.setBillId(billId);
            return updateRefundOrder;
        }).collect(Collectors.toList());
        if (isDriver) {
            updateDriverIdOrderList.addAll(collect);
        } else {
            updateUserIdOrderList.addAll(collect);
        }
        if (TradeTypeEnum.refund.name().equals(tradeType) && !isDriver) {
            //最终支付金额 需根据退款情况入库
            bill.setPaymentAmount(bill.getPaymentAmount().subtract(amount));
            //支付又退款的金额 需根据退款情况入库
            bill.setPayerRefundAmount(bill.getPayerRefundAmount().add(amount));
        } else if (TradeTypeEnum.transfer.name().equals(tradeType) && isDriver) {
            //车主到账金额 需要根据到账情况入库
            bill.setReceiptsAmount(bill.getReceiptsAmount().add(amount));
            bill.setTransferState(TradeState.TransferState.transfer.name());
        } else if (TradeTypeEnum.confiscate.name().equals(tradeType) && isDriver) {
            //车主到账金额 需要根据到账情况入库
            bill.setReceiptsAmount(bill.getReceiptsAmount().subtract(amount));
            bill.setPayeeReturnAmount(bill.getPayeeReturnAmount().add(amount));
            //出租车线下支付服务费出租车未记录，只可根据支付组扣款记录更新
            if (TBusinessEnum.t_offline.name().equals(businessType)) {
                bill.setPlatformCommission(bill.getPlatformCommission().add(amount));
            }
        }
    }

    /**
     * 生成分布式id
     *
     * @param date 时间戳
     * @return 分布式id
     */
    private static long getId(Date date, String lookupId, int suffix, Long threadId) {
        return BillIdFactory.getIdHelper(lookupId, suffix, threadId).genId(date.getTime());
    }

    private static long getThreadId(String threadName) {
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(threadName);
        if (matcher.find()) {
            return Long.parseLong(matcher.group(0));
        } else {
            log.warn("不应该出现该情况. name:{}", threadName);
            return 0;
        }
    }

}
