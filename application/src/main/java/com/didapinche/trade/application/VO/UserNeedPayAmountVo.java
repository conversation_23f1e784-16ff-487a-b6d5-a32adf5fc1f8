package com.didapinche.trade.application.VO;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UserNeedPayAmountVo {

    public int code;
    public String message;


    @JSONField(name = "ride_money")
    public int rideMoney;

    @JSONField(name = "coupon_credit")
    public int couponCredit;

    @JSONField(name = "selected_balance")
    public int selectedBalance;

    @JSONField(name = "selected_bonus")
    public int selectedBonus;

    @JSONField(name = "need_pay")
    public int needPay;

    @JSONField(name = "must_pay_addition_money")
    public Boolean mustPayAdditionMoney;



}
