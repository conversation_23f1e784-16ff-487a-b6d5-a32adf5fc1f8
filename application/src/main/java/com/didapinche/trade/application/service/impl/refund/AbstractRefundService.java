package com.didapinche.trade.application.service.impl.refund;

import cn.hutool.core.util.ObjectUtil;
import com.didapinche.agaue.common.exception.DException;
import com.didapinche.agaue.common.result.Result;
import com.didapinche.agaue.common.result.ResultBuilder;
import com.didapinche.finance.mq.context.enums.TradeTypeEnum;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.server.commons.common.util.UserInfoUtil;
import com.didapinche.trade.application.BO.RefundBO;
import com.didapinche.trade.application.service.impl.AbstractTradeService;
import com.didapinche.trade.application.service.impl.bill.BillMangerSupport;
import com.didapinche.trade.domin.DO.TradeOrderDO;
import com.didapinche.trade.domin.mozi.context.MoziCancelPayContext;
import com.didapinche.trade.domin.mozi.service.MoziService;
import com.didapinche.trade.domin.bill.action.mapping.BillOrderMappingVisitor;
import com.didapinche.trade.domin.bill.action.refund.RefundActionVisitor;
import com.didapinche.trade.domin.bill.action.refund.bo.RefundElementBO;
import com.didapinche.trade.domin.order.service.OrderStatusDomainService;
import com.didapinche.trade.domin.refund.DO.CalculateOrderDO;
import com.didapinche.trade.domin.refund.DO.RefundResultDO;
import com.didapinche.trade.domin.refund.request.CheckRefundDomainRequest;
import com.didapinche.trade.domin.refund.request.RefundCalculateDomainRequest;
import com.didapinche.trade.domin.refund.request.RefundDomainRequest;
import com.didapinche.trade.domin.refund.service.CalculateOrderDomainService;
import com.didapinche.trade.domin.refund.service.RefundDomainService;
import com.didapinche.trade.domin.support.TouchSupportService;
import com.didapinche.trade.domin.support.bean.PushEntity;
import com.didapinche.trade.infrastructure.entities.TradeOrderNote;
import com.didapinche.trade.infrastructure.exception.TradeErrorCode;
import com.didapinche.trade.infrastructure.util.LogProxy;
import com.didapinche.trade.thrift.entities.RefundResult;
import com.didapinche.trade.thrift.enums.TBusinessEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static com.didapinche.trade.infrastructure.exception.TradeErrorCode.PARAM_ERROR;

/**
 * <AUTHOR>
 * @Date 2022/7/14 14:57
 * @Version 1.0
 */
@Slf4j
public abstract class AbstractRefundService<T> extends AbstractTradeService<T> {


    @Autowired
    protected CalculateOrderDomainService calculateOrderDomainService;
    @Autowired
    protected RefundDomainService refundDomainService;
    @Autowired
    private TouchSupportService touchSupportService;

    @Autowired
    OrderStatusDomainService orderStatusDomainService;

    @Autowired
    private MoziService moziService;


    @Autowired
    BillMangerSupport billMangerSupport;


    @Autowired
    RefundActionVisitor refundActionVisitor;

    @Autowired
    BillOrderMappingVisitor billOrderMappingVisitor;


    protected Result<List<RefundResult>> refund(RefundBO refundBO) throws Exception {
        LogProxy.log2(() -> "AbstractRefundService.refund refundBO={}", () -> new Object[]{JsonMapper.toJson(refundBO)});
        // check 校验
        if (CollectionUtils.isEmpty(refundBO.getOrderIdList())){
            log.error("{}退款失败 {}:{} 退款集合为空",this.getClass(),refundBO.getMajorOrderId(),refundBO.getBusinessType());
            throw new DException(PARAM_ERROR);
        }
        List<RefundResult> refundResultList = null;
        CheckRefundDomainRequest checkRefundDomainRequest = new CheckRefundDomainRequest();
        checkRefundDomainRequest.setBusinessType(refundBO.getBusinessType())
                .setRefundAmount(refundBO.getDeductFee())
                .setUserId(refundBO.getUserId())
                .setOrderId(refundBO.getMajorOrderId())//原交易生成的交易ID(trade_order:id)
                .setProductId(refundBO.getProductId());//HPCDO 在此处有使用productId
        LogProxy.log2(() -> "AbstractRefundService.lockAndCheckRefund request={}", () -> new Object[]{JsonMapper.toJson(checkRefundDomainRequest)});
        //查询是否能退，已经是否存在退款记录
        List<RefundResultDO> refundResultDos = refundDomainService.lockAndCheckRefund(checkRefundDomainRequest);
        LogProxy.log2(() -> "AbstractRefundService.lockAndCheckRefund result={}", () -> new Object[]{JsonMapper.toJson(refundResultDos)});
        if (!CollectionUtils.isEmpty(refundResultDos)){
            log.warn("{} {}重复调用",this.getClass(),refundBO.getProductId());
            refundResultList = refundResultDos.stream().map(RefundResultDO::convertToRefundResult).collect(Collectors.toList());
            return ResultBuilder.buildResult(TradeErrorCode.ORDER_ERROR.getCode(),TradeErrorCode.ORDER_ERROR.getMsg(),refundResultList);
        }
        // 计算退款金额，剔除补偿的金额，也即是扣除的费用debuctFee
        RefundCalculateDomainRequest refundCalculateDomainRequest = new RefundCalculateDomainRequest();
        refundCalculateDomainRequest.setMajorOrderId(refundBO.getMajorOrderId())
                .setBusinessType(StringUtils.isEmpty(refundBO.getRefundType())?refundBO.getBusinessType():refundBO.getRefundType())
                .setOrderIdList(refundBO.getOrderIdList())
                .setUserId(refundBO.getUserId())
                .setAmount(refundBO.getDeductFee());//补偿金，不组用户退回的钱
        LogProxy.log2(() -> "AbstractRefundService.refundCalculate request={}", () -> new Object[]{JsonMapper.toJson(refundCalculateDomainRequest)});
        List<CalculateOrderDO> calculateOrderDos = calculateOrderDomainService.refundCalculate(refundCalculateDomainRequest);
        LogProxy.log2(() -> "AbstractRefundService.refundCalculate result={}", () -> new Object[]{JsonMapper.toJson(calculateOrderDos)});

        Date now = ObjectUtils.defaultIfNull(refundBO.getRefunDate(),new Date());
        //根据参数构造退款对象
        RefundElementBO refundElementBO = billMangerSupport.buildRefundBO(refundBO,calculateOrderDos);
        refundElementBO.setTradeDate(now);
        refundElementBO.accept(refundActionVisitor);//更新退款金额
        // 退款
        refundResultList = new ArrayList<>();

        boolean pushFlag = false;
        for (CalculateOrderDO calculateOrderDO : calculateOrderDos){
            Long billId = refundElementBO.getBillId();
            TradeOrderDO tradeOrderDO = new TradeOrderDO();//保存到数据库的退款记录
            tradeOrderDO.setCompanyId(calculateOrderDO.getTradeOrderModel().getCompanyId());
            tradeOrderDO.setUserId(refundBO.getUserId());
            tradeOrderDO.setSource(refundBO.getSource());
            tradeOrderDO.setOrderDesc(refundBO.getOrderDesc());
            tradeOrderDO.setMajorProductId(refundBO.getProductId());
            tradeOrderDO.setBusinessType(refundBO.getBusinessType());
            tradeOrderDO.setPayTradeNo(calculateOrderDO.getOrderId());
            tradeOrderDO.setCreateTime(now);
            tradeOrderDO.setSuccessTime(now);
            tradeOrderDO.setUpdateTime(now);
            tradeOrderDO.setBillId(billId);
            tradeOrderDO.setTradeType(TradeTypeEnum.refund.name());
            TradeOrderNote tradeOrderNote = new TradeOrderNote();
            tradeOrderNote.setRideId(refundBO.getRideId());
            tradeOrderDO.setNote(JsonMapper.toJson(tradeOrderNote));
            RefundDomainRequest refundDomainRequest = new RefundDomainRequest();
            refundDomainRequest.setTradeOrderDO(tradeOrderDO)
                    .setCouponFlag(refundBO.isCouponBack() && calculateOrderDO.getOrderId().equals(refundBO.getMajorOrderId()))
                    .setSysOpId(refundBO.getSysOpId())
                    .setCalculateOrderDO(calculateOrderDO)
                    .setTransferStatus(refundBO.getDriverId() != null)
                    .setPaymentNo(calculateOrderDO.getTradeOrderModel().getPaymentNo())
                    .setMajorOrderId(refundBO.getMajorOrderId())
                    .setDriverId(refundBO.getDriverId())
                    .setDeductFee(refundBO.getDeductFee())
                    .setRefundStatusSuccess(refundBO.getRefundStatusSuccess())
                    .setScene(refundBO.getScene())
                    .setAdvanceOrderDO(refundBO.getAdvanceOrderDO());
            // 只有主订单才退服务费
            if (calculateOrderDO.getOrderId().equals(refundBO.getMajorOrderId())){
                refundDomainRequest.setServiceFee(refundBO.getServiceFee());
            }
            LogProxy.log2(() -> "AbstractRefundService.refundDomainService.refund request={}", () -> new Object[]{JsonMapper.toJson(refundDomainRequest)});
            RefundResultDO refund = refundDomainService.refund(refundDomainRequest);
            LogProxy.log2(() -> "AbstractRefundService.refundDomainService.refund result={}", () -> new Object[]{JsonMapper.toJson(refund)});
            refundElementBO.setRefundOrderId(refund.getRefundOrderId());
            refundElementBO.accept(billOrderMappingVisitor);//插入关联日志

            pushFlag = pushFlag || calculateOrderDO.getPassenger().add(calculateOrderDO.getPassengerNotWithdraw()).compareTo(BigDecimal.ONE)>0 && calculateOrderDO.getThird().compareTo(BigDecimal.ONE)<=0;
            refundResultList.add(refund.convertToRefundResult());
        }
        // 取消订单发送push
        if (pushFlag && TBusinessEnum.carpool_cancel.name().equals(refundBO.getBusinessType())){
            Executors.newSingleThreadExecutor().submit(() -> touchSupportService.sendPushV2(new PushEntity().setUserCid(UserInfoUtil.getUsercidById(refundBO.getUserId().intValue())).setPushTemplateCode("pay-202206011001")));
        }
        detectCancelPay(refundBO);
        return ResultBuilder.buildSuccessResult(refundResultList);
    }

    private void detectCancelPay(RefundBO refundBO) {
        try {
            MoziCancelPayContext context = new MoziCancelPayContext();
            context.setTradeOrderId(refundBO.getMajorOrderId());
            context.setPayOutUserId(refundBO.getUserId());
            context.setMajorProductId(refundBO.getProductId());
            context.setBusinessType(refundBO.getBusinessType());
            context.setCancelTime(new Date().getTime());
            moziService.detectCancelPay(context);
        } catch (Exception ex) {
            //ignore
        }
    }


}
