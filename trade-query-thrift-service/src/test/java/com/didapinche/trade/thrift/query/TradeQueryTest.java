package com.didapinche.trade.thrift.query;

import com.didapinche.trade.infrastructure.util.accountchangefactory.CEnterpriseVipAccountChange;
import com.didapinche.trade.infrastructure.util.accountchangefactory.bean.InnerAccountChangeItem;
import com.didapinche.trade.thrift.TradeQueryThriftService;
import com.didapinche.trade.thrift.entities.GraySwitchRequest;
import com.didapinche.trade.thrift.enums.TOrderAccountTypeEnum;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2022/8/24 16:39
 * @Version 1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Ignore
public class TradeQueryTest {

    @Resource(name = "tradeQueryThriftServiceImpl")
    private TradeQueryThriftService.Iface tradeQueryThriftService;

    @Autowired
    private CEnterpriseVipAccountChange cEnterpriseVipAccountChange;

    @Test
    public void grayScaleTest() throws Exception {
        InnerAccountChangeItem innerAccountChangeItem = new InnerAccountChangeItem();
        innerAccountChangeItem.setBusinessType("carpool_cancel");
        innerAccountChangeItem.setUserId(11L);
        innerAccountChangeItem.setCompanyId(11);
        innerAccountChangeItem.setOrderId(11L);
        innerAccountChangeItem.setAmount(BigDecimal.TEN);
        innerAccountChangeItem.setAccountType(TOrderAccountTypeEnum.c_enterprise_vip.name());
        cEnterpriseVipAccountChange.accountIncome(innerAccountChangeItem);
    }

    @Test
    public void saveGraySwitch() throws Exception {
//        GraySwitch graySwitch = new GraySwitch();
//        graySwitch.setGroup("1");
//        graySwitch.setBusinessType("2");
//        graySwitch.setSource("3");
//        graySwitch.setSwitchType("5");
//        graySwitch.setWhiteList("5");
//        graySwitch.setPercentage(6);
//        graySwitch.setComment("7");
        GraySwitchRequest graySwitchRequest = new GraySwitchRequest();
        graySwitchRequest.setSource("34");
        graySwitchRequest.setBusinessType("2");
        graySwitchRequest.setUserId(5);
        System.err.println(tradeQueryThriftService.graySwitch(graySwitchRequest).toString());;
        TimeUnit.SECONDS.sleep(111111);
    }
}
