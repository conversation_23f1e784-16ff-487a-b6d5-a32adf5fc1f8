package com.didapinche.trade.infrastructure.manager;

import com.didapinche.agaue.datasource.common.Routing;
import com.didapinche.agaue.datasource.enums.DatabaseEnum;
import com.didapinche.agaue.datasource.enums.DatabaseTypeEnum;
import com.didapinche.agaue.datasource.toolkit.DynamicDataSourceContextHolder;
import com.didapinche.id.generator.IdHelper;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.server.commons.common.util.LoadPropertyUtil;
import com.didapinche.trade.infrastructure.service.impl.TradeLogModelService;
import com.didapinche.trade.infrastructure.tbl.TradeAmountDetailsModel;
import com.didapinche.trade.infrastructure.tbl.TradeCouponDetailsModel;
import com.didapinche.trade.infrastructure.tbl.TradeLogModel;
import com.didapinche.trade.infrastructure.tbl.TradeOrderModel;
import com.didapinche.trade.infrastructure.tbl.TradeProductDetailsModel;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TradeLogService {

    @Autowired
    private TradeLogModelService tradeLogModelService;


    @Transactional(rollbackFor = Exception.class)
    public void manageOrder(TradeOrderModel tradeOrderModel, String tableSuffix) {
        try {
            log.debug("manageOrder start. trade_order tableSuffix={}", tableSuffix);
            //优先存储支付单的id
            Long tradeOrderId = ObjectUtils.defaultIfNull(tradeOrderModel.getPayTradeNo(), tradeOrderModel.getId());
            String majorProductId = tradeOrderModel.getMajorProductId();
            Date tableIndexDate = ObjectUtils.defaultIfNull(tradeOrderModel.getCreateTime(), new Date());

            Optional<String> tradeLogIdOptional = queryPossibleTradeLogId(tradeOrderId, majorProductId, tableIndexDate);

            TradeLogModel tradeLogModel = new TradeLogModel();
            tradeLogModel.setId(IdHelper.genId());
            tradeLogModel.setTradeLogId(tradeLogIdOptional.orElse(String.valueOf(IdHelper.genId())));
            tradeLogModel.setTradeOrderId(tradeOrderId);
            tradeLogModel.setProductId(majorProductId);
            tradeLogModel.setUserId(tradeOrderModel.getUserId().intValue());
            tradeLogModel.setStatus(tradeOrderModel.getStatus());
            tradeLogModel.setBusinessType(tradeOrderModel.getBusinessType());
            tradeLogModel.setTradeType(tradeOrderModel.getTradeType());
            tradeLogModel.setAmount(tradeOrderModel.getTotalPrice());
            Map<String, Object> contentMap = Maps.newHashMap();
            contentMap.put("id", tradeOrderModel.getId());
            contentMap.put("table", "trade_order" + tableSuffix);
            tradeLogModel.setContent(JsonMapper.toJson(contentMap));
            tradeLogModel.setRemark(tradeOrderModel.getOrderDesc());
            tradeLogModel.setExt1("");
            tradeLogModel.setExt2("");
            tradeLogModel.setCreateTime(new Date());
            tradeLogModel.setUpdateTime(null);
            tradeLogModel.setTableIndexDate(tableIndexDate);
            tradeLogModelService.save(tradeLogModel);
        } catch (Exception ex) {
            log.error("manageOrder error.", ex);
        }
    }

    private Optional<String> queryPossibleTradeLogId(Long tradeOrderId, String majorProductId, Date tableIndexDate) {
        if (tableIndexDate == null) {
            return Optional.empty();
        }
        if (tradeOrderId != null) {
            TradeLogModel tradeLogModel = tradeLogModelService.queryOneByTradeOrderId(tradeOrderId, tableIndexDate);
            if (tradeLogModel != null) {
                return Optional.ofNullable(tradeLogModel.getTradeLogId());
            }
        }
        if (StringUtils.isNotBlank(majorProductId)) {
            TradeLogModel tradeLogModel = tradeLogModelService.queryOneByProductId(majorProductId, tableIndexDate);
            if (tradeLogModel != null) {
                return Optional.ofNullable(tradeLogModel.getTradeLogId());
            }
        }
        log.debug("queryPossibleTradeLogId不存在. tradeOrderId={} majorProductId={} tableIndexDate={}", tradeOrderId, majorProductId, tableIndexDate);
        return Optional.empty();
    }

    @Transactional(rollbackFor = Exception.class)
    public void manageAmount(TradeOrderModel tradeOrder, Collection<TradeAmountDetailsModel> entityList, String tableSuffix) {
        manageAmount(tradeOrder, entityList, tableSuffix, null);
    }

    private void manageAmount(TradeOrderModel tradeOrderModel, Collection<TradeAmountDetailsModel> entityList, String tableSuffix, Date tableIndexDate) {
        try {
            if (CollectionUtils.isEmpty(entityList)) {
                return;
            }
            List<TradeAmountDetailsModel> models = Lists.newArrayList(entityList);
            TradeAmountDetailsModel amountDetailsModelFirst = models.get(0);

            BigDecimal totalAmount = models.stream().map(TradeAmountDetailsModel::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            List<Pair<String, BigDecimal>> amountItems = models.stream().map(detail -> Pair.of(detail.getType(), detail.getAmount())).collect(Collectors.toList());

            Long tradeOrderId = ObjectUtils.defaultIfNull(tradeOrderModel.getPayTradeNo(), ObjectUtils.defaultIfNull(tradeOrderModel.getId(), amountDetailsModelFirst.getId()));
            String majorProductId = tradeOrderModel.getMajorProductId();
            Date defaultTableIndexDate = ObjectUtils.defaultIfNull(tableIndexDate, new Date());

            Optional<String> tradeLogIdOptional = queryPossibleTradeLogId(tradeOrderId, majorProductId, defaultTableIndexDate);

            TradeLogModel tradeLogModel = new TradeLogModel();
            tradeLogModel.setId(IdHelper.genId());
            tradeLogModel.setTradeLogId(tradeLogIdOptional.orElse(String.valueOf(IdHelper.genId())));
            tradeLogModel.setTradeOrderId(tradeOrderId);
            tradeLogModel.setProductId(majorProductId);
            tradeLogModel.setUserId(tradeOrderModel.getUserId().intValue());
            tradeLogModel.setStatus(tradeOrderModel.getStatus());
            tradeLogModel.setBusinessType(tradeOrderModel.getBusinessType());
            tradeLogModel.setTradeType(tradeOrderModel.getTradeType());
            tradeLogModel.setAmount(totalAmount);
            Map<String, Object> contentMap = Maps.newHashMap();
            contentMap.put("id", amountDetailsModelFirst.getId());
            contentMap.put("amountItems", amountItems);
            contentMap.put("table", "trade_amount_details" + tableSuffix);
            tradeLogModel.setContent(JsonMapper.toJson(contentMap));
            tradeLogModel.setRemark("");
            tradeLogModel.setExt1("");
            tradeLogModel.setExt2("");
            tradeLogModel.setCreateTime(new Date());
            tradeLogModel.setUpdateTime(null);
            tradeLogModel.setTableIndexDate(defaultTableIndexDate);
            tradeLogModelService.save(tradeLogModel);
        } catch (Exception ex) {
            log.error("manageAmount error. entityList={} tableSuffix={} tableIndexDate={}", JsonMapper.toJson(entityList), tableSuffix, tableIndexDate, ex);
        }
    }

}
