package com.didapinche.trade.infrastructure.thrift;

import com.didapinche.agaue.common.exception.DException;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.thrift.taxiwritepay.QueryScanTradeOrderListRequest;
import com.didapinche.thrift.taxiwritepay.QueryScanTradeOrderListResponse;
import com.didapinche.thrift.taxiwritepay.QueryScanTradeOrderListResult;
import com.didapinche.thrift.taxiwritepay.TPayAdvanceParam;
import com.didapinche.thrift.taxiwritepay.TResult;
import com.didapinche.thrift.taxiwritepay.TaxiwritepayThriftService;
import com.didapinche.trade.infrastructure.exception.TradeErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TaxiWritePayClient {

    @Autowired
    private TaxiwritepayThriftService.Iface taxiwritepayService;


    public TResult advance(TPayAdvanceParam param) throws TException {
        return taxiwritepayService.advanced(param);
    }


    public QueryScanTradeOrderListResult queryScanTradeOrderList(String driverCid, String date) {
        QueryScanTradeOrderListRequest request = new QueryScanTradeOrderListRequest().setDriverCid(driverCid).setDate(date);
        try {
            QueryScanTradeOrderListResponse response = taxiwritepayService.queryScanTradeOrderList(request);
            if (response == null) {
                log.error("queryScanTradeOrderList error. request:{}", JsonMapper.toJson(request));
                throw new DException(TradeErrorCode.REQUEST_ERROR);
            }
            return response.getResult();
        } catch (Exception e) {
            log.error("queryScanTradeOrderList error. request:{}", JsonMapper.toJson(request), e);
            throw new DException(TradeErrorCode.REQUEST_ERROR);
        }
    }

}
