package com.didapinche.trade.infrastructure.es;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * 交易账单与订单映射表
 * <AUTHOR>
 */
@Data
public class TradeBillOrderMappingEsModel implements Serializable {
    /**
     * 订单号，雪花算法(带时间)
     */
    private Long id;

    /**
     * trade_bill的ID
     */
    @JSONField(name = "bill_id")
    private Long billId;

    /**
     * trade_order的ID
     */
    @JSONField(name = "trade_order_id")
    private Long tradeOrderId;

    /**
     * 业务类型  业务类型+交易类型=mapping.bizType
     */
    @JSONField(name = "biz_type")
    private String bizType;
    /**
     * 交易类型
     */
    @JSONField(name = "trade_type")
    private String tradeType;

    /**
     * 用户ID
     */
    @JSONField(name = "user_id")
    private Long userId;

    /**
     * 创建时间
     */
    @JSONField(name = "create_time")
    private String createTime;


    private static final long serialVersionUID = 1L;
}