package com.didapinche.trade.infrastructure.thrift;

import com.didapinche.agaue.common.exception.DException;
import com.didapinche.agaue.tools.convert.AmountConvertUtil;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.thrift.accountwrite.AccountwriteThriftService;
import com.didapinche.thrift.accountwrite.TArrearsAccountRechargeCheckRequest;
import com.didapinche.thrift.accountwrite.TArrearsAccountRechargeCheckResponse;
import com.didapinche.thrift.accountwrite.TFreezeBalanceResponse;
import com.didapinche.thrift.accountwrite.TResult;
import com.didapinche.thrift.accountwrite.TThirdpartyCarpoolAccountParams;
import com.didapinche.thrift.accountwrite.TUnfreezeBalanceRequest;
import com.didapinche.trade.infrastructure.constants.LockKeyConstants;
import com.didapinche.trade.infrastructure.entities.account.UserPayAccount;
import com.didapinche.trade.infrastructure.exception.TradeErrorCode;
import com.didapinche.trade.infrastructure.thrift.bean.UserBonus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/7/24
 */
@Slf4j
@Service
public class AccountWriteThriftSupportService {

    @Resource
    private AccountwriteThriftService.Iface accountWriteService;

//    public void incrThirdpartyCarpoolAccount(String changeType, int companyId, BigDecimal amount, Long tradeOrderId) {
//        TThirdpartyCarpoolAccountParams params = new TThirdpartyCarpoolAccountParams();
//        params.setAccountId(companyId);
//        params.setChangeType(changeType);
//        params.setBalanceChange(amount.toString());
//        params.setUniqueKey(tradeOrderId + "");
//        params.setOrderId(tradeOrderId);
//        params.setOrderType("trade_order");
//        TResult tr = null;
//        try {
//            tr = accountWriteService.incrThirdpartyCarpoolAccount(params);
//        } catch (Exception e) {
//            log.error("第三方顺风车加钱错误 {}", tradeOrderId, e);
//            throw new DException(TradeErrorCode.CARPOOL_THIRD_INCR_ERROR);
//        }
//        if (tr.getCode() != 0) {
//            log.error("第三方顺风车加钱失败 {} {}", tradeOrderId, tr.getCode());
//            throw new DException(TradeErrorCode.CARPOOL_THIRD_INCR_ERROR);
//        }
//    }

//    public void decrThirdPartyCarpoolAccount(String changeType, int companyId, BigDecimal amount, Long tradeOrderId) {
//        TThirdpartyCarpoolAccountParams params = new TThirdpartyCarpoolAccountParams();
//        params.setAccountId(companyId);
//        params.setChangeType(changeType);
//        params.setBalanceChange(amount.toString());
//        params.setUniqueKey(tradeOrderId + "");
//        params.setOrderId(tradeOrderId);
//        params.setOrderType("trade_order");
//        TResult tr = null;
//        try {
//            tr = accountWriteService.decrThirdpartyCarpoolAccount(params);
//        } catch (Exception e) {
//            log.error("第三方顺风车减钱错误 {}", tradeOrderId, e);
//            throw new DException(TradeErrorCode.CARPOOL_THIRD_DECR_ERROR);
//        }
//        if (tr.getCode() != 0) {
//            log.error("第三方顺风车减钱失败 {} {}", tradeOrderId, tr.getCode());
//            throw new DException(TradeErrorCode.CARPOOL_THIRD_DECR_ERROR);
//        }
//    }

//    /**
//     * 顺风车三方扣款
//     *
//     * @param companyId    来源
//     * @param price        总价
//     * @param tradeOrderId 交易id
//     */
//    public void decrThirdPartyCarpoolAccount(String majorProductId, Integer companyId, String price, Long tradeOrderId) {
//        TThirdpartyCarpoolAccountParams params = new TThirdpartyCarpoolAccountParams();
//        params.setAccountId(companyId);
//        params.setChangeType("pay_fee");
//        params.setBalanceChange(price);
//        params.setUniqueKey("pay_fee_" + tradeOrderId);
//        params.setOrderId(tradeOrderId);
//        params.setOrderType("trade_order");
//        try {
//            TResult tr = accountWriteService.decrThirdpartyCarpoolAccount(params);
//            if (tr.getCode() != 0) {
//                log.error("第三方顺风车扣款失败.majorProductId:{},orderId:{},source:{}", majorProductId, tradeOrderId, companyId);
//                throw new DException(TradeErrorCode.CARPOOL_THIRD_DECR_ERROR);
//            }
//        } catch (Exception e) {
//            log.error("第三方顺风车扣款失败.majorProductId:{},orderId:{},source:{}", majorProductId, tradeOrderId, companyId, e);
//            throw new DException(TradeErrorCode.CARPOOL_THIRD_DECR_ERROR);
//        }
//    }
//
//    /**
//     * 锁账户
//     *
//     * @param userId         用户id
//     * @param majorProductId 主商品id
//     */
//    public UserPayAccount lockAndGetUserPayAccount(Long userId, String majorProductId) {
//        try {
//            TResult tResult = accountWriteService.lockWithResult("user_payaccount", userId + "",
//                    LockKeyConstants.TRADE_PAYMENT_REENTRANT_KEY + "-" + majorProductId, 2);
//            if (tResult.getCode() != 0 || StringUtils.isBlank(tResult.getRet())) {
//                log.info("lockUserPayAccount error. majorProductId:{},userId:{},result:{}", majorProductId, userId, JsonMapper.toJson(tResult));
//                throw new DException(TradeErrorCode.LOCK_USER_ACCOUNT_ERROR);
//            }
//            UserPayAccount userPayAccount = JsonMapper.json2Bean(tResult.getRet(), UserPayAccount.class);
//            if (userPayAccount == null) {
//                log.info("lockUserPayAccount error. userPayAccount is null. majorProductId:{},userId:{},result:{}",
//                        majorProductId, userId, JsonMapper.toJson(tResult));
//                throw new DException(TradeErrorCode.LOCK_USER_ACCOUNT_ERROR);
//            }
//            log.info("lockUserPayAccount. majorProductId:{},userId:{},result:{}", majorProductId, userId, JsonMapper.toJson(tResult));
//            return userPayAccount;
//        } catch (Exception e) {
//            log.error("lockUserPayAccount error.userId:{}, majorProductId:{}", userId, majorProductId, e);
//            throw new DException(TradeErrorCode.LOCK_USER_ACCOUNT_ERROR);
//        }
//    }

//    /**
//     * 解锁账户
//     *
//     * @param userId         用户id
//     * @param majorProductId 主商品id
//     * @param reentrantKey   重入key
//     */
//    public void unlockUserPayAccount(Long userId, Long tradeOrderId, String majorProductId, String reentrantKey) {
//        try {
//            accountWriteService.unlock("user_payaccount", userId + "", reentrantKey);
//        } catch (Exception e) {
//            log.error("解锁账户失败.userId:{}, majorProductId:{},orderId:{}", userId, majorProductId, tradeOrderId, e);
//        }
//    }
//
//    /**
//     * 锁顺风金
//     *
//     * @param userId         用户id
//     * @param majorProductId 主商品id
//     */
//    public BigDecimal lockAndGetBonus(Long userId, String majorProductId) {
//        try {
//            TResult tResult = accountWriteService.lockWithResultV2("user_bonus", userId + "",
//                    LockKeyConstants.TRADE_PAYMENT_REENTRANT_KEY + "-" + majorProductId);
//            if (tResult.getCode() != 0 || StringUtils.isBlank(tResult.getRet())) {
//                log.info("lockUserPayAccount error. majorProductId:{},userId:{},result:{}", majorProductId, userId, JsonMapper.toJson(tResult));
//                throw new DException(TradeErrorCode.LOCK_BONUS_ACCOUNT_ERROR);
//            }
//            UserBonus userBonus = JsonMapper.json2Bean(tResult.getRet(), UserBonus.class);
//            if (userBonus == null || userBonus.getBonusAccount() == null) {
//                return BigDecimal.ZERO;
//            }
//            return AmountConvertUtil.penny2yuan(userBonus.getBonusAccount());
//        } catch (Exception e) {
//            log.error("锁账户失败.userId:{}, majorProductId:{}", userId, majorProductId, e);
//            throw new DException(TradeErrorCode.LOCK_BONUS_ACCOUNT_ERROR);
//        }
//    }

//    /**
//     * 解锁顺风金
//     *
//     * @param userId         用户id
//     * @param majorProductId 主商品id
//     * @param reentrantKey   重入key
//     */
//    public void unlockBonus(Long userId, Long tradeOrderId, String majorProductId, String reentrantKey) {
//        try {
//            accountWriteService.unlock("user_bonus", userId + "", reentrantKey);
//        } catch (Exception e) {
//            log.error("解锁账户失败.userId:{}, majorProductId:{},orderId:{}", userId, majorProductId, tradeOrderId, e);
//        }
//    }

    public void arrearsAccountRechargeCheck(Long userId, String balance) {
        TArrearsAccountRechargeCheckRequest request = new TArrearsAccountRechargeCheckRequest().setUserId(userId).setUserType("carpool").setRechargeBalance(balance);
        try {
            TArrearsAccountRechargeCheckResponse response = accountWriteService.arrearsAccountRechargeCheck(request);
            if (response.getCode() != 0) {
                log.error("arrearsAccountRechargeCheck error. request:{},response:{}", JsonMapper.toJson(request), JsonMapper.toJson(response));
                throw new DException(response.getCode(), response.getMsg());
            }
        } catch (Exception e) {
            log.error("arrearsAccountRechargeCheck error. request:{}", JsonMapper.toJson(request));
            throw new DException(TradeErrorCode.ARREARS_RECHARGE_ERROR);
        }
    }

    public boolean arrearsAccountRechargeCheckReturnBool(Long userId, String balance) {
        TArrearsAccountRechargeCheckRequest request = new TArrearsAccountRechargeCheckRequest().setUserId(userId).setUserType("carpool").setRechargeBalance(balance);
        try {
            TArrearsAccountRechargeCheckResponse response = accountWriteService.arrearsAccountRechargeCheck(request);
            if (response.getCode() != 0) {
                log.error("arrearsAccountRechargeCheck error. request:{},response:{}", JsonMapper.toJson(request), JsonMapper.toJson(response));
                return false;
            }
        } catch (Exception e) {
            log.error("arrearsAccountRechargeCheck error. request:{}", JsonMapper.toJson(request));
            return false;
        }
        return true;
    }

    /**
     * 解冻
     */
    public void unFreezeBalance(TUnfreezeBalanceRequest request) {
        try {
            TFreezeBalanceResponse t = accountWriteService.unFreezeBalance(request);
            if (t.getCode() != 0 && t.getCode() != 140030) {
                log.error("unFreezeBalance error.unFreezeBalanceRequest:{}, t:{}", JsonMapper.toJson(request), JsonMapper.toJson(t));
                throw new DException(TradeErrorCode.UNFREE_ACCOUNT_ERROR);
            }
            log.info("unFreezeBalance success. freeOrderId:{}, t.id:{}", request.getFreezeBalanceOrderId(), t.getId());
        } catch (Exception e) {
            log.error("unFreezeBalance error.request:{}", JsonMapper.toJson(request), e);
            throw new DException(TradeErrorCode.UNFREE_ACCOUNT_ERROR);
        }
    }
}
