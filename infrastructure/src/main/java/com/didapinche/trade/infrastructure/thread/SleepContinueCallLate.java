package com.didapinche.trade.infrastructure.thread;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 */
@Slf4j
public class SleepContinueCallLate implements RejectedExecutionHandler {
    @Override
    public void rejectedExecution(Runnable runnable, ThreadPoolExecutor executor) {
        if (!executor.isShutdown()) {
            try {
                Thread.sleep(50L);
                executor.execute(runnable);
            } catch (InterruptedException e) {
                log.error("dynamic thread pool runnable Interrupted", e);
                Thread.currentThread().interrupt();
            }
        }
    }
}
