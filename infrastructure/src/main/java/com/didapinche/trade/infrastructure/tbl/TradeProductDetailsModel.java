package com.didapinche.trade.infrastructure.tbl;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 交易产品详情表
 * @TableName trade_product_details
 */
@TableName(value ="trade_product_details")
@Data
public class TradeProductDetailsModel implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联的订单id
     */
    private Long tradeOrderId;

    /**
     * 交易的产品号,如:rideId，taxiRideId,goodOrderId以及各种奖励id等
     */
    private String productId;

    /**
     * 交易的产品类型,ride:行程,coupon_package:券包,tolls:过路费,extra_fee:感谢费(调度费),highway_fee:高速费,以及各种奖励等
     */
    private String type;

    /**
     * 产品价格
     */
    private BigDecimal price;

    /**
     * 新老系统商品标识
     */
    private boolean newGoodsFlag;

    /**
     * 商品code
     */
    private String orderCode;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}