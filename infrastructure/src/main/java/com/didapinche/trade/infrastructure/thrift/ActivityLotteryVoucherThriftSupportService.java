package com.didapinche.trade.infrastructure.thrift;

import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.thrift.activity.right.ActivityLotteryVoucherThriftService;
import com.didapinche.thrift.activity.right.VirtualVoucherInfo;
import com.didapinche.thrift.activity.right.VirtualVoucherResult;
import com.didapinche.thrift.activity.right.VoucherBatchRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ActivityLotteryVoucherThriftSupportService {

    @Resource
    private ActivityLotteryVoucherThriftService.Iface activityLotteryVoucherThriftService;

    /**
     * 查询场景 不抛异常 查询失败或未查到都按没有展示
     */
    public List<VirtualVoucherInfo> queryRightValidByTemplateIds(VoucherBatchRequest voucherBatchRequest) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("queryRightValidByTemplateIds req:{}", JsonMapper.toJson(voucherBatchRequest));
            }
            VirtualVoucherResult tr = activityLotteryVoucherThriftService.queryRightValidByTemplateIds(voucherBatchRequest);
            if (tr.getCode() == 162003) {
                if (log.isDebugEnabled()) {
                    log.debug("queryRightValidByTemplateIds不存在权益 req:{}, result:{}", JsonMapper.toJson(voucherBatchRequest), JsonMapper.toJson(tr));
                }
                return null;
            }
             if (tr.getCode() != 0) {
                log.error("queryRightValidByTemplateIds error req:{}, result:{}", JsonMapper.toJson(voucherBatchRequest), JsonMapper.toJson(tr));
                return null;
            }
            if (log.isDebugEnabled()) {
                log.debug("queryRightValidByTemplateIds result:{}", JsonMapper.toJson(tr));
            }
            return tr.getVirtualVoucherInfoList();
        } catch (Exception e) {
            log.error("queryRightValidByTemplateIds error req:{}, e:{}", JsonMapper.toJson(voucherBatchRequest), e.getMessage());
            return null;
        }
    }


}
