package com.didapinche.trade.infrastructure.mq.bean.callback;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/7/24
 */
@Data
@Accessors(chain = true)
public class CallBackTradeAmountDetails {
    /**
     * 可提现余额账户
     */
    private BigDecimal passengerAmount;
    /**
     * 不可提现余额账户
     */
    private BigDecimal passengerNotWithdrawAmount;
    /**
     * 三方支付账户
     */
    private BigDecimal thirdAmount;
    /**
     * 优惠券账户
     */
    private BigDecimal couponAmount;
    /**
     * 顺风金账户
     */
    private BigDecimal bonusAmount;
    /**
     * 顺风车vip企业账户
     */
    private BigDecimal cEnterpriseVipAmount;
    /**
     * 出租车企业账户
     */
    private BigDecimal tEnterpriseAmount;
    /**
     * 出租车vip企业账户
     */
    private BigDecimal tEnterpriseVipAmount;
    /**
     * 高速费补贴账户
     */
    private BigDecimal highwaySubsidyAmount;

    /**
     * 乘客补贴金额
     */
    private BigDecimal passengerSubsidyAmount;

    /**
     * 三方渠道的支付优惠金额
     * 顺风车车费详情页反显
     *
     */
    private BigDecimal payDiscountAmount;
}
