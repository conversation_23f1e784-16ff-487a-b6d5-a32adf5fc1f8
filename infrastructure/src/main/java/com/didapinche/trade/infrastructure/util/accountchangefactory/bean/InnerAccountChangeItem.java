package com.didapinche.trade.infrastructure.util.accountchangefactory.bean;

import com.didapinche.trade.infrastructure.mq.RocketMqSendUtils;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class InnerAccountChangeItem {

    private String accountType;
    private String businessType;
    private String tradeType;
    private Long userId;
    private Integer companyId;
    private Long orderId;
    private Long payTradeNO;
    private BigDecimal amount = BigDecimal.ZERO;
    private BigDecimal passengerNotWithdraw = BigDecimal.ZERO;
    private Long withdrawId = RocketMqSendUtils.DEFAULT_WITHDRAW;
    private String remarks;
    private String details;
    private String comment;
    private Integer sysopId = RocketMqSendUtils.SYS_OP_ID;

    private String tag;

    private BigDecimal commissionFee = BigDecimal.ZERO;

    // 企业付相关参数
    private BigDecimal awardBalance;
    private Integer epAccountBalance;
    private Long rideId;
    private Integer ruleId;
    private Integer balanceExtra1change;
    private Integer balanceExtra2change;
    private Long plantFreezeTime = 0L;
    private String orderSource;
    private String freezeReason;
    private String productId;
    /**
     * 可能同一个订单号会有多次操作，出租车的挂起和取消挂起，只靠payTradeNO幂等会出错，增加此字段
     */
    private String uniqueKey;
    private String compensationId;

    public void setWithdrawId(Long withdrawId) {
        if (withdrawId == null){
            return;
        }
        this.withdrawId = withdrawId;
    }

    public void setSysopId(Integer sysopId) {
        if (sysopId == null){
            return;
        }
        this.sysopId = sysopId;
    }
}
