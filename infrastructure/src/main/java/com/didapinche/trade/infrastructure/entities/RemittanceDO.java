package com.didapinche.trade.infrastructure.entities;

import com.didapinche.trade.infrastructure.mq.RocketMqSendUtils;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2022/8/25 20:21
 * @Version 1.0
 */
@Data
public class RemittanceDO {

    private Long payUserId;
    private BigDecimal payFee;
    private String payAccountType;
    private Long incomeUserId;
    private BigDecimal incomeFee;
    private String incomeAccountType;
    private Long payTradeNo;
    private String businessType;
    private Long orderId;
    private Integer sysopId = RocketMqSendUtils.SYS_OP_ID;
    private String remarks;
    private String details;
    private String comment;
    private String compensationId;

}
