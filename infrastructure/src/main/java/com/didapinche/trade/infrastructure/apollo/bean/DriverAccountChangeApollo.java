package com.didapinche.trade.infrastructure.apollo.bean;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/7/21 13:18
 * @Version 1.0
 * <p>
 */
@Data
public class DriverAccountChangeApollo {
    /**
     * 备注
     */
    private String comment;
    /**
     * 业务类型，orderId是哪张表的，就选对应的productType taxi/carpool,后期新订单系统上线可以删除
     */
    private String productType;
    /**
     * 账户操作来源
     */
    private String sourceCid;
    /**
     * 账户变动子类型
     */
    private byte subChangeType;
    /**
     *
     */
    private String category;

    private Boolean accountCheck = false;
}
