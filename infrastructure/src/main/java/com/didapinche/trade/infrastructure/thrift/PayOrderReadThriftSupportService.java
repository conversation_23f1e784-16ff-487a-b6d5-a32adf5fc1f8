package com.didapinche.trade.infrastructure.thrift;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.didapinche.agaue.common.exception.DException;
import com.didapinche.agaue.common.exception.ErrorCode;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.thrift.payorderread.PayorderreadThriftService;
import com.didapinche.thrift.payorderread.TQueryPolytypeOrderForUserParams;
import com.didapinche.thrift.payorderread.TQueryQRTaxiOrderParams;
import com.didapinche.thrift.payorderread.TResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/21
 */
@Service
@Slf4j
public class PayOrderReadThriftSupportService {

    @Resource
    private PayorderreadThriftService.Iface payOrderReadThriftService;

    /**
     * 获取支付信息
     */
    public Map<String, String> getPayInfoCarpool(Long rideId, Long orderId) {
        try {
            TResult tResult = payOrderReadThriftService.getPayInfoCarpool(orderId);
            if (tResult.getCode() != 0 || StringUtils.isBlank(tResult.getRet())) {
                log.error("调 getPayInfoCarpool 返回结果失败.rideId:{},orderId:{}, result:{}", rideId, orderId, JsonMapper.toJson(tResult));
                throw new DException(ErrorCode.SERVICE_ERROR);
            }
            Map<String, String> map = JSON.parseObject(tResult.getRet(), new TypeReference<Map<String, String>>() {
            });
            if (map == null || map.isEmpty()) {
                log.error("调 getPayInfoCarpool map is null.rideId:{},orderId:{}, result:{}", rideId, orderId, JsonMapper.toJson(tResult));
                throw new DException(ErrorCode.SERVICE_ERROR);
            }
            return map;
        } catch (Exception e) {
            log.error("调 getPayInfoCarpool 发生异常.rideId:{},orderId:{}", rideId, orderId, e);
            throw new DException(ErrorCode.SERVICE_ERROR);
        }
    }

    public String getPolytypeOrderMoneyByParam(long userId, String month)  {
        TQueryPolytypeOrderForUserParams params = new TQueryPolytypeOrderForUserParams();
        params.setUserId(userId);
        params.setStartTime(month);
        params.setEndTime(month);
        try {
            TResult result = payOrderReadThriftService.getPolytypeOrderMoneyByParam(params);
            return result.getCode() != 0 ? null : result.getRet();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取支付信息
     */
    public Map<String, String> queryQRTaxiOrderDetailList(TQueryQRTaxiOrderParams params) {
        try {
            TResult tResult = payOrderReadThriftService.queryQRTaxiOrderDetailList(params);
            if (tResult == null) {
                log.error("调 queryQRTaxiOrderDetailList 返回tResult==null.params:{}", JsonMapper.toJson(params));
                throw new DException(ErrorCode.SERVICE_ERROR);
            }
            if (tResult.getCode() != 0 || StringUtils.isBlank(tResult.getRet())) {
                log.error("调 queryQRTaxiOrderDetailList 返回结果失败.params:{}, result:{}", JsonMapper.toJson(params), JsonMapper.toJson(tResult));
                throw new DException(ErrorCode.SERVICE_ERROR);
            }
            Map<String, String> map = JSON.parseObject(tResult.getRet(), new TypeReference<Map<String, String>>() {
            });
            if (map == null || map.isEmpty()) {
                log.error("调 queryQRTaxiOrderDetailList map is null.params:{},result:{}", JsonMapper.toJson(params), JsonMapper.toJson(tResult));
                throw new DException(ErrorCode.SERVICE_ERROR);
            }
            return map;
        } catch (Exception e) {
            log.error("调 queryQRTaxiOrderDetailList 发生异常.params:{}", JsonMapper.toJson(params),  e);
            throw new DException(ErrorCode.SERVICE_ERROR);
        }
    }

}
