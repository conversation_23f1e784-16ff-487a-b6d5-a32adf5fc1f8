package com.didapinche.trade.infrastructure.thrift;

import com.didapinche.agaue.common.exception.DException;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.thrift.taxidriver.TResult;
import com.didapinche.thrift.taxidriver.TaxidriverThriftService;
import com.didapinche.trade.infrastructure.exception.TradeErrorCode;
import com.didapinche.trade.infrastructure.thrift.bean.DriverWiMiniResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Service
@Slf4j
public class TaxidriverThriftSupportService {
    
    @Resource
    private TaxidriverThriftService.Iface taxidriverThriftService;

    public String getDefaultCarinfoByUserId(int driverId) {
        try {
        TResult defaultCarinfo = taxidriverThriftService.getDefaultCarinfoByUserId(driverId);
            if (defaultCarinfo.getCode() != 0 || StringUtils.isBlank(defaultCarinfo.getData())) {
                log.error("getDriverWxMiniOpenId 查询西安司机清分标识接口报错.driverId:{}, defaultCarinfo:{}", driverId, defaultCarinfo.toString());
                throw new DException(TradeErrorCode.PAYMENT_ERROR);
            }
            Map<String, Object> defaultCarinfoMap = JsonMapper.json2Map(defaultCarinfo.getData());
            if (defaultCarinfoMap == null || defaultCarinfoMap.get("partnerId") == null) {
                //清分标识不存在，报错
                log.error("getDriverWxMiniOpenId 查询西安司机清分标识不存在报错.driverId:{}, defaultCarinfo:{}", driverId, defaultCarinfo.toString());
                throw new DException(TradeErrorCode.PAYMENT_ERROR);
            }
            return defaultCarinfoMap.get("partnerId").toString();
        } catch (Exception e) {
            log.error("getDriverWxMiniOpenId 查询西安司机清分标识接口异常.driverId:{}", driverId);
            throw new DException(TradeErrorCode.PAYMENT_ERROR);
        }
    }
}
