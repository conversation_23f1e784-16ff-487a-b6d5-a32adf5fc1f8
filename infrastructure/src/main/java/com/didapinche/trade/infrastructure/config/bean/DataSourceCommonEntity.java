package com.didapinche.trade.infrastructure.config.bean;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
public class DataSourceCommonEntity {

    private String driverClassName;

    private int initialSize;

    private int minIdle;

    private int maxActive;

    private int maxWait;

    private Boolean defaultAutoCommit;

    protected boolean removeAbandoned;

    /**
     * 单位是秒
     */
    protected int removeAbandonedTimeout;

    private int timeBetweenEvictionRunsMillis;

    private int minEvictableIdleTimeMillis;

    private String validationQuery;

    private boolean testWhileIdle;

    private boolean testOnBorrow;

    private boolean testOnReturn;

    private boolean poolPreparedStatements;

    private int maxPoolPreparedStatementPerConnectionSize;

    private String filters;

    private String connectionProperties;

    private List<String> connectionInitSqls = new ArrayList<>();

}
