package com.didapinche.trade.infrastructure.tbl;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 交易订单表
 * <AUTHOR>
 * @TableName trade_order
 */
@TableName(value ="trade_order")
@Data
public class TradeOrderModel implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 发起交易的业务订单号，rideId，taxiRideId,goodOrderId等
     */
    private String majorProductId;

    /**
     * 原支付交易订单号,有则传
     */
    private Long payTradeNo;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 交易类型，payment：支付，refund：退款
     */
    private String tradeType;

    /**
     * 总金额
     */
    private BigDecimal totalPrice;

    /**
     * 业务类型，顺风车：carpool，出租车：taxi，顺风车高速费：carpool_highway...
     */
    private String businessType;

    /**
     * 业务来源，carpool_highway:顺风车高速费，carpool_app:顺风车app
     */
    private String source;

    /**
     * 状态,create:创建,processing:处理中,success:成功, fail:失败
     */
    private String status;

    /**
     * 三方渠道
     * @see com.didapinche.trade.thrift.enums.TChannelEnum
     */
    private String thirdChannel;

    /**
     * 三方支付订单号,由三方生成
     */
    private String thirdTradeNo;

    /**
     * 第三方支付系统唯一标识
     */
    private String paymentNo;

    /**
     * 订单描述
     */
    private String orderDesc;

    /**
     * 企业付公司id
     */
    private Integer companyId;

    /**
     * 其他信息
     * @see com.didapinche.trade.infrastructure.entities.TradeOrderNote
     */
    private String note;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 订单交易成功
     */
    private Date successTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * trade_bill中的ID
     */
    private Long billId;

    /**
     * 交易单ID
     */
    private Long orderId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}