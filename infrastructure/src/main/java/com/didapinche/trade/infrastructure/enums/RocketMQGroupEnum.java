package com.didapinche.trade.infrastructure.enums;

public enum RocketMQGroupEnum {


    /**
     * 顺风车欠款充值
     */
    C_BDC_PAYMENT_TRADE_CARPOOL_ARREARS_RECHARGE,

    /**
     * 出租车欠款充值
     */
    C_BDC_PAYMENT_TRADE_TAXI_ARREARS_RECHARGE,

    /**
     * 出租车扫码支付
     */
    C_BDC_PAYMENT_TRADE_TAXI_SCAN_PAY,

    /**
     * 出租车乘客支付总额修改
     */
    C_BDC_PAYMENT_TRADE_TAXI_PASSENGER_PAID_SUM_MONEY_MODIFY,

    /**
     * 创建先享后付单
     */
    C_BDC_PAYMENT_PAU_TRADE_ORDER_CREATE,

    /**
     * 芝麻扣款
     */
    C_BDC_PAYMENT_ZHIMA_PAU_TRADE_ORDER_CUT,

    /**
     * 抖音支付扣款
     */
    C_BDC_PAYMENT_DOUYINPAY_PAU_TRADE_ORDER_CUT,

    /**
     * 微信分扣款
     */
    C_BDC_PAYMENT_ZHIFUFEN_PAU_TRADE_ORDER_CUT,

    /**
     * 登录触发先乘后付设备校验
     */
    C_BDC_PAYMENT_PAU_SIGN_MODEL_LOGIN_CHECK,

    C_BDC_PAYMENT_PAID_UPDATE_RIDE_ORDER,

    /**
     * 核销优惠券
     */
    C_SERVER_ALLPAYMENT_COUPON,
    C_BDC_PAYMENT_TAXI_HARLEYC,
    C_BDC_PAYMENT_ORDER_CALLBACK_RECEIVED,
    C_BDC_PAYMENT_ORDER_CREATED,
    C_SERVER_TAXI_PAID_MONEY_MONITOR_TAXI,
    C_SERVER_ENTERPRISEPAY_SUCCESS,
    C_SERVER_TAXI_SCANPAY_SENDMSG,
    C_SERVER_TAXI_PAID_MONEY_MONITOR_CARPOOL,
    C_SERVER_CARPOOL_ONRIDE_STAT_MONEY,
    C_SERVER_ENTERPRISE_ACCOUNT_DECR,
    C_SERVER_ENTERPRISE_ACCOUNT_INCR,
    C_BDC_PAYMENT_DRIVER_CERTIFY_COUPON_SEND,

    C_BDC_PAYMENT_TAXIQR_DRIVER_TRANSFER,
    /**
     * 智慧码订单到账确认
     */
    C_BDC_PAYMENT_TAXIQR_TRANSFER_STATUS_RECONFIRM,

    C_BDC_PAYMENT_SAVED_MONEY,

    /**
     * 三方退款成功通知
     */
    C_BDC_PAYMENT_THIRD_REFUND_SUCCESS_NOTIFY,
    ;
}
