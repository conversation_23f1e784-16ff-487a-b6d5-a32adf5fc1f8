package com.didapinche.trade.infrastructure.constants;

/**
 * <AUTHOR>
 * @Date 2023/11/14 14:01
 * @Version 1.0
 */
public class MQConstants {

    public static  final String THEA_BIZ_TYPE = "THEA";

    public static  final String LIQUIDATION_BIZ_TYPE = "liquidation";

    public static  final String PAU_CUT_SEND_BIZ_TYPE = "pauCutSendMq";

    public static  final String INSERT_INFO_SYS_OP_HIS_BIZ_TYPE = "insertInfoSysOpHis";

    public static  final String PAU_CUT_STATUS_FOR_ACTIVE_BIZ_TYPE = "pauCutStatusForActive";

    public static  final String PAU_CUT_STATUS_BIZ_TYPE = "pauCutStatus";

    public static  final String SEND_TAXI_ENTERPRISE_ACCOUNT_DECR_BIZ_TYPE = "sendTaxiEnterpriseAccountDecr";

    public static  final String DOUBLE_WRITE_BIZ_TYPE = "doubleWrite";

    public static  final String SEND_ARREARS_ACCOUNT_RESTORE_WRITE_BIZ_TYPE = "sendArrearsAccountRestore";

    public static  final String TAXI_ENTERPRISE_ACCOUNT_DECR_BIZ_TYPE = "taxiEnterpriseAccountDecr";

    public static  final String TAXI_ENTERPRISE_ACCOUNT_INCR_BIZ_TYPE = "taxiEnterpriseAccountIncr";

    public static  final String PAYMENT_GOODS_CALL_BACK_NOTIFY_BIZ_TYPE = "paymentGoodsCallBackNotify";

    public static  final String PAYMENT_REFUND_BIZ_TYPE = "paymentRefund";

    public static  final String USER_ACCOUNT_CHANGE_BIZ_TYPE = "userAccountChange";

    public static  final String DRIVER_ACCOUNT_CHANGE_BIZ_TYPE = "driverAccountChange";

    public static  final String PAYMENT_CALL_BACK_NOTIFY_BIZ_TYPE = "paymentCallBackNotify";
    public static  final String PAYMENT_CALL_BACK_NOTIFY_PLATFORM_BIZ_TYPE = "paymentCallBackNotifyPlatform";

    public static  final String REFUND_COUPON_TYPE = "refundCoupon";
    public static  final String USE_COUPON_TYPE = "useCoupon";

    public static  final String BONUS_CHANGE_TYPE = "bonusChange";

    public static  final String TAXI_ACCOUNT_CHANGE_TYPE = "taxiAccountChange";




}
