package com.didapinche.trade.infrastructure.util.accountchangefactory;

import com.didapinche.agaue.tools.convert.AmountConvertUtil;
import com.didapinche.trade.infrastructure.enums.EpAccUseHisOrderTypeEnum;
import com.didapinche.trade.infrastructure.enums.EpAccUseHisTypeEnum;
import com.didapinche.trade.infrastructure.mq.RocketMqSendUtils;
import com.didapinche.trade.infrastructure.mq.bean.EnterpriseAccMQEntity;
import com.didapinche.trade.infrastructure.util.accountchangefactory.bean.InnerAccountChangeItem;
import com.didapinche.trade.thrift.enums.TOrderAccountTypeEnum;
import org.springframework.stereotype.Component;

@Component("t_enterprise")
public class TEnterpriseAccountChange extends AbsAccountChange {

    @Override
    public void accountIncome(InnerAccountChangeItem innerAccountChangeItem) {
        tEnterpriseAccountChange(innerAccountChangeItem,EpAccUseHisTypeEnum.REFUND.getStatus());
    }

    @Override
    public void accountPayout(InnerAccountChangeItem innerAccountChangeItem) {
        tEnterpriseAccountChange(innerAccountChangeItem, EpAccUseHisTypeEnum.PAYOUT.getStatus());
    }

    private void tEnterpriseAccountChange(InnerAccountChangeItem innerAccountChangeItem, String changeType){
        EnterpriseAccMQEntity enterpriseAccMQEntity = new EnterpriseAccMQEntity();
        enterpriseAccMQEntity.setCompanyId(innerAccountChangeItem.getCompanyId());
        enterpriseAccMQEntity.setUserId(innerAccountChangeItem.getUserId().intValue());
        enterpriseAccMQEntity.setOriginBalance(innerAccountChangeItem.getEpAccountBalance());
        enterpriseAccMQEntity.setBalanceChange(AmountConvertUtil.yuan2penny(innerAccountChangeItem.getAmount()));
        enterpriseAccMQEntity.setAwardBalance(AmountConvertUtil.yuan2penny(innerAccountChangeItem.getAwardBalance()));
        enterpriseAccMQEntity.setType(changeType);
        enterpriseAccMQEntity.setOrderType(EpAccUseHisOrderTypeEnum.TAXI.getStatus());
        enterpriseAccMQEntity.setOrderId(innerAccountChangeItem.getOrderId());
        enterpriseAccMQEntity.setRideId(innerAccountChangeItem.getRideId());
        enterpriseAccMQEntity.setRuleId(innerAccountChangeItem.getRuleId());
        enterpriseAccMQEntity.setComment(innerAccountChangeItem.getComment());
        enterpriseAccMQEntity.setBusinessId(innerAccountChangeItem.getOrderId()+"");
        enterpriseAccMQEntity.setSysOpId(innerAccountChangeItem.getSysopId());
        enterpriseAccMQEntity.setReentrantKey(innerAccountChangeItem.getOrderId()+"");
        enterpriseAccMQEntity.setBalanceExtra1change(innerAccountChangeItem.getBalanceExtra1change());
        enterpriseAccMQEntity.setBalanceExtra2change(innerAccountChangeItem.getBalanceExtra2change());
        enterpriseAccMQEntity.setPaymentOrderId(innerAccountChangeItem.getPayTradeNO());
        enterpriseAccMQEntity.setAmountType(TOrderAccountTypeEnum.t_enterprise.name());
        if(EpAccUseHisTypeEnum.PAYOUT.getStatus().equals(changeType)){
            RocketMqSendUtils.taxiEnterpriseAccountDecr(enterpriseAccMQEntity);
        }else if (EpAccUseHisTypeEnum.REFUND.getStatus().equals(changeType)){
            RocketMqSendUtils.taxiEnterpriseAccountIncr(enterpriseAccMQEntity);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {

    }
}
