package com.didapinche.trade.infrastructure.zeus;

import com.didapinche.trade.infrastructure.util.LogProxy;
import com.didapinche.zeus.common.constant.SideEnum;
import com.didapinche.zeus.interceptor.Interceptor;
import com.didapinche.zeus.invoker.Invoker;
import com.didapinche.zeus.invoker.invocation.Invocation;
import com.didapinche.zeus.spring.boot.annotation.ZeusComponent;
import lombok.extern.slf4j.Slf4j;

/**
 * 获取调用上游的名称
 */
@Slf4j
@ZeusComponent(name = "get_upstream_name")
public class UpstreamNameInterceptor implements Interceptor {

    @Override
    public Object invoke(Invoker invoker, Invocation invocation) throws Throwable {
        try {
            if (invocation.getSideEnum() == SideEnum.SERVER) {
                LogProxy.log2(() -> "remote_address={} remote_name={}", () -> new Object[]{invocation.getExtra(Invocation.ATTRIBUTE_REMOTE_ADDRESS), invocation.getExtra(Invocation.ATTRIBUTE_ORIGIN_APPLICATION_NAME)});
            }
        } catch (Throwable throwable) {
            //ignore throwable
        }
        Object result = invoker.invoke(invocation);
        return result;
    }
}
