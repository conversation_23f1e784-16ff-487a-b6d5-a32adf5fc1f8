package com.didapinche.trade.infrastructure.thrift;

import com.didapinche.account.thrift.AccountThriftService;
import com.didapinche.account.thrift.entities.*;
import com.didapinche.account.thrift.enums.TProductTypeEnum;
import com.didapinche.account.thrift.enums.TUserTypeEnum;
import com.didapinche.agaue.common.exception.DException;
import com.didapinche.server.commons.common.eventtrack.EventTrack;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.server.commons.common.util.DateUtil;
import com.didapinche.trade.infrastructure.exception.TradeErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class AccountThriftSupportService {

    @Resource
    private AccountThriftService.Iface accountThriftService;

    public TFreezeAccountOrderQueryResult doFreezeAccount (int userId, String orderCid, BigDecimal freezeBalance, String freezeReason, String freezeComment, String uniqueKey) {
        try {
            TFreezeAccountParam tFreezeAccountParam = new TFreezeAccountParam();
            tFreezeAccountParam.setUserId(userId);
            tFreezeAccountParam.setUserType(TUserTypeEnum.carpool_driver);
            tFreezeAccountParam.setBalanceChange(freezeBalance.multiply(BigDecimal.valueOf(100)).setScale(0, 4).longValue());
            TAccountOrderGroup tAccountOrderGroup = new TAccountOrderGroup();
            tAccountOrderGroup.setOrderCid(orderCid);
            tAccountOrderGroup.setProductType(TProductTypeEnum.trade_order);
            tFreezeAccountParam.setOrderGroup(tAccountOrderGroup);
            tFreezeAccountParam.setFreezeReason(freezeReason);
            tFreezeAccountParam.setFreezeComment(freezeComment);
            Calendar instance = Calendar.getInstance();
            instance.add(Calendar.YEAR, 100);
            tFreezeAccountParam.setPlanUnfreezeTime(DateUtil.date2String2(instance.getTime()));
            tFreezeAccountParam.setUniqueKey(uniqueKey);
            TAccountResult tAccountResult = accountThriftService.doFreezeAccount(tFreezeAccountParam);
            if (tAccountResult.getCode() == 0 && tAccountResult.getFreezeAccountOrderList() != null) {
                return tAccountResult.getFreezeAccountOrderList().get(0);
            }
            log.error("doFreezeAccount失败.userId:{},orderCid:{},result:{}", userId, orderCid, tAccountResult.toString());
            return null;
        } catch (Exception e) {
            log.error("doFreezeAccount失败.userId:{},orderCid:{}", userId, orderCid, e);
            return null;
        }
    }

    /**
     *
     * @param userId int
     * @param userRole int 1:乘客，2:车主，3:司机
     * @return
     */
    public TAccountQueryResult queryAccount(int userId, int userRole) {
        try {
            List<TAccountQueryParam> accountQueryList = new ArrayList<>();
            TAccountQueryParam tAccountQueryParam = new TAccountQueryParam();
            tAccountQueryParam.setUserId(userId);
            tAccountQueryParam.setUserType(userRole == 1 ? TUserTypeEnum.passenger : (userRole == 2 ? TUserTypeEnum.carpool_driver : TUserTypeEnum.taxi_driver));
            accountQueryList.add(tAccountQueryParam);
            TAccountResult tAccountResult = accountThriftService.queryAccount(accountQueryList);
            if (tAccountResult.getCode() == 0 && tAccountResult.getAccountListSize() > 0) {
                return tAccountResult.getAccountList().get(0);
            }
            if (tAccountResult.getCode() == -999){
                EventTrack.recordEventPoint("ACCOUNT_QUERYACCOUNT_CIRCUITBREAKER", userId+"", 0, userId+":"+userRole);
            }
            return null;
        } catch (Exception e) {
            log.error("queryAccount异常.userId:{},role:{}", userId, userRole, e);
            return null;
        }
    }

    public TBonusAccountQueryResult queryBonusAccount(int userId) {
        try {
            TAccountResult tAccountResult = accountThriftService.queryBonusAccount(Collections.singletonList(userId));
            if (tAccountResult.getCode() == 0 && tAccountResult.getBonusAccountListSize() > 0) {
                return tAccountResult.getBonusAccountList().get(0);
            }
            if (tAccountResult.getCode() == -999){
                EventTrack.recordEventPoint("BONUS_QUERYBONUSACCOUNT_CIRCUITBREAKER", userId+"", 0, userId+"");
            }
            return null;
        } catch (Exception e) {
            log.error("queryBonusAccount异常.userId:{}", userId, e);
            return null;
        }
    }

    public List <TFreezeAccountOrderQueryResult> queryFreezeAccountOrder(int userId) {
        try {
            TFreezeAccountOrderQueryParam tFreezeAccountOrderQueryParam = new TFreezeAccountOrderQueryParam();
            tFreezeAccountOrderQueryParam.setUserId(userId);
            tFreezeAccountOrderQueryParam.setUserType(TUserTypeEnum.carpool_driver);
            TAccountResult tAccountResult = accountThriftService.queryFreezeAccountOrder(tFreezeAccountOrderQueryParam);
            if (tAccountResult.getCode() == 0 && tAccountResult.getFreezeAccountOrderListSize() > 0) {
                return tAccountResult.getFreezeAccountOrderList();
            }
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("queryFreezeAccountOrder异常.userId:{}", userId, e);
            return null;
        }
    }

    public void doUnfreezeAccount(int userId, long freezeBalanceOrderId, long balanceChange, String changeReason, String changeComment) {
        try {
            TUnfreezeAccountParam tUnfreezeAccountParam = new TUnfreezeAccountParam();
            tUnfreezeAccountParam.setUserId(userId);
            tUnfreezeAccountParam.setUserType(TUserTypeEnum.carpool_driver);
            tUnfreezeAccountParam.setFreezeBalanceOrderId(freezeBalanceOrderId);
            tUnfreezeAccountParam.setBalanceChange(balanceChange);
            tUnfreezeAccountParam.setChangeReason(changeReason);
            tUnfreezeAccountParam.setChangeComment(changeComment);
            accountThriftService.doUnfreezeAccount(tUnfreezeAccountParam);
        } catch (Exception e) {
            log.error("unFreezeBalance error.userId:{},freezeBalanceOrderId:{}", userId, freezeBalanceOrderId, e);
            throw new DException(TradeErrorCode.UNFREE_ACCOUNT_ERROR);
        }
    }

    public void arrearsAccountRechargeCheck(int userId, long rechargeBalance) {
        TArrearsAccountRechargeCheckParam tArrearsAccountRechargeCheckParam = new TArrearsAccountRechargeCheckParam();
        tArrearsAccountRechargeCheckParam.setUserId(userId);
        tArrearsAccountRechargeCheckParam.setRechargeBalance(rechargeBalance);
        try {
            TAccountResult tAccountResult = accountThriftService.arrearsAccountRechargeCheck(tArrearsAccountRechargeCheckParam);
            if (tAccountResult.getCode() != 0) {
                log.info("arrearsAccountRechargeCheck. userId:{},result:{}", userId, tAccountResult.toString());
                throw new DException(tAccountResult.getCode(), tAccountResult.getMsg());
            }
        } catch (Exception e) {
            log.error("arrearsAccountRechargeCheck error.userId:{},rechargeBalance:{}", userId, rechargeBalance, e);
            throw new DException(TradeErrorCode.ARREARS_RECHARGE_ERROR);
        }
    }

    public TAccountResult queryAccountHis(TAccountHisQueryParam param) {
        try {
            TAccountResult tAccountResult = accountThriftService.queryAccountHis(param);
            if (tAccountResult.getCode() != 0) {
                log.info("arrearsAccountRechargeCheck. userId:{},result:{}", param.getUserId(), JsonMapper.toJson(tAccountResult));
                throw new DException(tAccountResult.getCode(), tAccountResult.getMsg());
            }
            return tAccountResult;
        } catch (Exception e) {
            log.error("arrearsAccountRechargeCheck error.userId:{}", param.getUserId(), e);
            throw new DException(TradeErrorCode.ARREARS_RECHARGE_ERROR);
        }
    }

    public PayBankCardItem getPayBankCard(int userId) {
        try {
            QueryPayBankCardParam param = new QueryPayBankCardParam();
            param.setUserId(userId);
            QueryPayBankCardResult queryPayBankCardResult = accountThriftService.queryPayBankCard(param);
            if (queryPayBankCardResult.getCode() == 0 && CollectionUtils.isNotEmpty(queryPayBankCardResult.getBindBanks())) {
                return queryPayBankCardResult.getBindBanks().get(0);
            }
            return null;
        } catch (Exception exception) {
            return null;
        }
    }

    /**
     * 获取银行卡支付的营销文案描述
     * @param userId
     * @return
     */
    public PayBankInfoItem queryMarketPayBank(int userId) {
        try {
            QueryPayBankInfosParam request = new QueryPayBankInfosParam();
            request.setUserId(userId);
            request.setOnlyMarket(true);
            QueryPayBankInfosResult bankInfosResult = accountThriftService.queryPayBankInfos(request);
            if (CollectionUtils.isNotEmpty(bankInfosResult.getBankInfos())) {
                return bankInfosResult.getBankInfos().get(0);
            }
            return null;
        } catch (Exception exception) {
            return null;
        }
    }
}
