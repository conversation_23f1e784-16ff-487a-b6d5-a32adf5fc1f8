<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.didapinche.trade.infrastructure.mapper.order.TradeOrderModelMapper">
    <resultMap id="BaseResultMap" type="com.didapinche.trade.infrastructure.tbl.TradeOrderModel">
        <!--@Table trade_order-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="major_product_id" jdbcType="VARCHAR" property="majorProductId"/>
        <result column="pay_trade_no" jdbcType="BIGINT" property="payTradeNo"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="trade_type" jdbcType="VARCHAR" property="tradeType"/>
        <result column="total_price" jdbcType="DECIMAL" property="totalPrice"/>
        <result column="business_type" jdbcType="VARCHAR" property="businessType"/>
        <result column="source" jdbcType="VARCHAR" property="source"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="third_channel" jdbcType="VARCHAR" property="thirdChannel"/>
        <result column="third_trade_no" jdbcType="VARCHAR" property="thirdTradeNo"/>
        <result column="payment_no" jdbcType="VARCHAR" property="paymentNo"/>
        <result column="order_desc" jdbcType="VARCHAR" property="orderDesc"/>
        <result column="company_id" jdbcType="INTEGER" property="companyId"/>
        <result column="note" jdbcType="VARCHAR" property="note"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="success_time" jdbcType="TIMESTAMP" property="successTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="bill_id" jdbcType="BIGINT" property="billId"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
    </resultMap>
    <resultMap id="BaseScanResultMap" type="com.didapinche.trade.infrastructure.entities.order.ScanTradeOrder">
        <result column="total_price" jdbcType="DECIMAL" property="totalPrice"/>
        <result column="third_channel" jdbcType="VARCHAR" property="thirdChannel"/>
        <result column="success_time" jdbcType="TIMESTAMP" property="successTime"/>
        <result column="user_id" jdbcType="BIGINT" property="userId" />
        <result column="third_trade_no" jdbcType="VARCHAR" property="thirdTradeNo" />
        <result column="note" jdbcType="VARCHAR" property="note" />
    </resultMap>
    <sql id="Base_Column_List">
        id,
        major_product_id,
        pay_trade_no,
        user_id,
        trade_type,
        total_price,
        business_type,
        `source`,
        `status`,
        third_channel,
        third_trade_no,
        payment_no,
        order_desc,
        company_id,
        note,
        create_time,
        success_time,
        update_time,
        bill_id,
        order_id
    </sql>
    <sql id="Base_Scan_Column_List">
        total_price,
        third_channel,
        success_time,
        user_id,
        third_trade_no,
        note
    </sql>


    <select id="isPaid" resultType="boolean">
        SELECT count(1)
        from
        <choose>
            <when test="suffix == ''">
                trade_order a,
                trade_product_details b
            </when>
            <when test="suffix != ''">
                trade_order ${suffix} a
                , trade_product_details ${suffix} b
            </when>
        </choose>
        where a.id=b.trade_order_id
        and a.user_id=#{userId}
        and a.business_type=#{businessType}
        and a.status='success'
        and a.trade_type='payment'
        limit 1;
    </select>

    <select id="selectOrderListByCriteria" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM trade_order
        WHERE create_time BETWEEN #{startTime} AND #{endTime}
        <if test="ids != null and ids.size() > 0">
            AND id IN
            <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="userIds != null and userIds.size() > 0">
            AND user_id IN
            <foreach item="item" index="index" collection="userIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="businessTypes != null and businessTypes.size() > 0">
            AND business_type IN
            <foreach item="item" index="index" collection="businessTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="tradeTypes != null and tradeTypes.size() > 0">
            AND trade_type IN
            <foreach item="item" index="index" collection="tradeTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="statusList != null and statusList.size() > 0">
            AND status IN
            <foreach item="item" index="index" collection="statusList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectScanOrderListByUserId" resultMap="BaseScanResultMap">
        SELECT
        <include refid="Base_Scan_Column_List"/>
        FROM trade_order
        WHERE user_id = #{userId}
        AND business_type = 't_scan'
        AND trade_type = 'payment'
        AND status = 'success'
        AND create_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY create_time asc
    </select>

    <select id="selectScanOrderListById"
            resultMap="BaseScanResultMap">
        SELECT
        <include refid="Base_Scan_Column_List"/>
        FROM trade_order
        WHERE business_type = 't_scan'
        AND trade_type = 'payment'
        <if test="ids != null and ids.size() > 0">
            AND id IN
            <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        limit #{num}
    </select>

    <select id="countScanOrderListByDriverId" resultType="java.lang.Integer">
        SELECT count(1)
        FROM trade_order
        WHERE user_id = #{userId}
          AND business_type = 't_scan'
          AND trade_type = 'payment'
          AND status = 'success'
          AND create_time BETWEEN #{startTime} AND #{endTime}
    </select>

    <select id="selectScanOrderListByDriverId"
            resultMap="BaseScanResultMap">
        SELECT
        <include refid="Base_Scan_Column_List"/>
        FROM trade_order
        WHERE user_id = #{userId}
        AND business_type = 't_scan'
        AND trade_type = 'payment'
        AND status = 'success'
        AND create_time BETWEEN #{startTime} AND #{endTime}
        limit #{pageNo}, #{pageSize}
    </select>
</mapper>