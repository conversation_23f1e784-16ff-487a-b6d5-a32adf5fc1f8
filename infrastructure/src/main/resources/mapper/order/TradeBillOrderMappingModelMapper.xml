<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.didapinche.trade.infrastructure.mapper.order.TradeBillOrderMappingModelMapper">
  <resultMap id="BaseResultMap" type="com.didapinche.trade.infrastructure.tbl.TradeBillOrderMappingModel">
    <!--@mbg.generated-->
    <!--@Table trade_bill_order_mapping_202403-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bill_id" jdbcType="BIGINT" property="billId" />
    <result column="trade_order_id" jdbcType="BIGINT" property="tradeOrderId" />
    <result column="biz_type" jdbcType="VARCHAR" property="bizType" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, bill_id, trade_order_id, biz_type, user_id, create_time, update_time
  </sql>

  <select id="selectListByCriteria" resultMap="BaseResultMap">
      SELECT
      <include refid="Base_Column_List"/>
      FROM trade_bill_order_mapping
      WHERE create_time BETWEEN #{startTime} AND #{endTime}
      <if test="tradeOrderIds != null and tradeOrderIds.size() > 0">
          AND trade_order_id IN
          <foreach item="item" index="index" collection="tradeOrderIds" open="(" separator="," close=")">
              #{item}
          </foreach>
      </if>
      <if test="userIds != null and userIds.size() > 0">
          AND user_id IN
          <foreach item="item" index="index" collection="userIds" open="(" separator="," close=")">
              #{item}
          </foreach>
      </if>
  </select>
</mapper>