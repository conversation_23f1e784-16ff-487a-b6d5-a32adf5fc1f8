@startuml

title 老系统-先享后付完单扣款

autonumber

participant "顺风车" as carpool
participant "carpoolwritepay-thrift" as carpoolwritethrift
participant "allpayment-hook" as allpaymenthook
participant "agreementpay-hook" as agreementpayhook
participant "支付中心" as paythrift

carpool->carpoolwritethrift: 确认到达进行扣款
group 走合拼返款逻辑

end

group 走退差价逻辑

end
carpoolwritethrift->carpoolwritethrift: 计算完单金额
note left of carpoolwritethrift: 完单金额=三方支付+券包金额-退款金额
alt 完单金额=0
carpoolwritethrift->paythrift: 取消先享后付单
carpoolwritethrift-->carpool: MQ通知顺风车
note left of carpoolwritethrift:t_bdc_payment_pau_cut_status_notify\n status=2

else 完单金额>0
carpoolwritethrift-->agreementpayhook:完单扣款MQ
	activate agreementpayhook
	note left of allpaymenthook: t_bdc_payment_pau_order_cut
	agreementpayhook->carpoolwritethrift:判断是否允许扣款
	alt 微信
		agreementpayhook->carpoolwritethrift:扣款【微信】先享后付订单
	else 支付宝
		agreementpayhook->paythrift:扣款【支付宝】先享后付订单
	end
	agreementpayhook-->agreementpayhook:如果扣款失败再发一个消息
	note right of agreementpayhook:t_bdc_payment_pau_order_cut
	deactivate
end
@enduml