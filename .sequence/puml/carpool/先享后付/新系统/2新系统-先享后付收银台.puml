@startuml
'https://plantuml.com/sequence-diagram
title 先享后付收银台
autonumber


actor "用户" as User
participant "APP/小程序" as Applet
participant "顺风车CarpoolPayRs" as CarpoolRs
participant "顺风车CarpoolPayThrift" as CarpoolTh
participant "营销" as Activate
participant "支付" as Trade


User -> Applet: 收银台


Applet -> CarpoolRs: 查询收银台\napp:/ride/query \n小程序:/ride/query/mini
CarpoolRs -> CarpoolTh: 查询收银台

CarpoolTh -> Activate: 查询是否有可用券包

Activate --> CarpoolTh: 返回可用券包

CarpoolTh -> Trade: 查询收银台queryCashier()

Trade -> Trade: 查余额
Trade -> Trade: 查优惠券
Trade -> Trade: 查顺风金
note right Trade:通过来源判断
alt 微信小程序(minipg_carpool_wx)
Trade -> Trade: 查是否可用微信先享后付 灰度AB实验组
else 支付宝小程序(minipg_carpool_ali/minipg_cx_ali)
Trade -> Trade: 查是否可用支付宝先享后付 灰度AB实验组
else 抖音小程序(minipg_carpool_dy)
Trade -> Trade: 查是否可用抖音先享后付 灰度AB实验组
end


Trade --> CarpoolTh: 返回收银台


CarpoolTh --> CarpoolRs: 返回收银台
CarpoolRs --> Applet: 返回收银台

Applet --> User: 返回信息


@enduml