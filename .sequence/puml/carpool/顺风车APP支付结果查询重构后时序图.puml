@startuml
'https://plantuml.com/sequence-diagram
title 顺风车APP支付重构后时序图
autonumber
actor 乘客
participant 客户端 as app
participant 顺风车组Rs服务 as carpoolRs
participant 顺风车组thrift服务 as carpoolThrift
participant 顺风车组读服务 as carpoolreadThrift
participant 顺风车组写服务 as carpoolWrite
participant 顺风车组重构后读服务 as carpoolQuery
participant 用户组服务 as userThrift
participant 活动组服务 as jifenThrift
participant 交易中台 as tradeThrift
participant 用户组标签服务 as userpermissionThrift
participant 支付账户写服务 as accountwriteThrift
participant 支付账户读服务 as accountRead
participant 优惠券服务 as couponThrift
participant 支付中台 as payThrift
'== 发起支付(包含三方) ==

乘客 -> app : 查询支付结果

loop **次
app -> carpoolRs : 查询支付结果
carpoolRs -> carpoolRs : 查询ride支付状态
app <-- carpoolRs : 返回支付结果
end

乘客 <-- app : 返回支付结果





























@enduml