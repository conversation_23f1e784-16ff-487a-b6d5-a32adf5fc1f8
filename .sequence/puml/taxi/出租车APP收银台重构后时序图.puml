@startuml
'https://plantuml.com/sequence-diagram
title 出租车APP收银台重构后时序图
autonumber
actor 乘客 as people
participant 出租车组Rs服务 as taxiRs
participant 出租车组thrift服务 as taxiThrift
participant 用户组服务 as userThrift
participant 出租车组读服务 as taxireadThrift
participant 用户关联关系判定服务 as aiUser
participant 出租车司机服务 as taxiDriver
participant 活动组服务 as active
participant 交易中台trade as tradeThrift
participant 用户最新信息服务 as usercurren
queue MQ as mq
participant 优惠券服务 as coupon
participant 支付组账户服务 as account


people -> taxiRs : 发起收银台请求

activate taxiRs

taxiRs -> taxiThrift : 收银台

activate taxiThrift

taxiThrift -> userThrift : 获取用户id

activate userThrift

taxiThrift <-- userThrift : 返回用户id

deactivate userThrift

taxiThrift -> taxireadThrift : 获取行程

activate taxireadThrift

taxiThrift <-- taxireadThrift : 返回行程信息

deactivate taxireadThrift

taxiThrift -> taxiThrift : 校验行程状态是否可支付

taxiThrift -> aiUser : 获取用户关联关系判定

activate aiUser

taxiThrift <-- aiUser : 返回用户关联关系判定

deactivate aiUser


deactivate taxiDriver

taxiThrift -> active : 获取券包

activate active

taxiThrift <-- active : 返回券包信息

deactivate active

deactivate aiUser
taxiThrift -> tradeThrift : 收银台接口：queryCashier()

activate tradeThrift

tradeThrift -> tradeThrift : 校验下单用户与支付用户是否是同一个人 \n代付无需校验

tradeThrift -> coupon : 获取可用优惠券

activate coupon

tradeThrift <-- coupon : 返回可用优惠券

deactivate coupon

tradeThrift -> account : 获取可用余额

activate account

tradeThrift <-- account : 返回可用余额

deactivate account

tradeThrift -> tradeThrift : 京东白条默认值

tradeThrift -> tradeThrift : 获取本次支付可支持的支付方式

tradeThrift -> tradeThrift : 缓存过路费

tradeThrift -> tradeThrift : 缓存券包id

tradeThrift -> tradeThrift : 缓存车费


taxiThrift <-- tradeThrift : 返回查询收银台结果

deactivate tradeThrift

taxiRs <-- taxiThrift : 返回收银台结果

deactivate taxiThrift

deactivate taxiRs

people <-- taxiRs : 返回收银台结果




@enduml