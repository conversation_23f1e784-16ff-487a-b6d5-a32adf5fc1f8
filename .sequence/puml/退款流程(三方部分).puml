@startuml
'https://plantuml.com/sequence-diagram
autonumber
actor 乘客或系统
queue MQ
participant 交易系统
participant 支付系统
participant 订单系统

== 发起订单退款(三方部分) ==

乘客或系统 -> 交易系统 : 取消订单 \n 重复支付等

交易系统 -> 订单系统 : 创建退款订单

订单系统 --> 交易系统 : 创建退款订单 ok

交易系统 -> MQ : 发送退款消息

MQ --> 交易系统 : 退款消息 ok

交易系统 --> 乘客或系统 :取消订单 ok

== 消费订单退款消息请求三方退款 ==

MQ --> 交易系统 : 消费退款消息

交易系统 -> 支付系统: 发起退款

支付系统 --> 交易系统: 成功或处理中

交易系统 -> 订单系统: 修改订单为退款成功或处理中

订单系统 --> 交易系统 : 同步订单退款状态 ok

交易系统 --> MQ: 消费退款消息成功

== 消费退款成功消息 ==

MQ --> 交易系统: 消费退款成功消息

交易系统 -> 订单系统: 修改订单为退款成功

订单系统 --> 交易系统 : 同步订单退款状态 ok

交易系统 --> MQ: 消费退款成功消息成功
@enduml