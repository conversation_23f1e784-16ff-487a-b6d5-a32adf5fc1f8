USE
didapinche_order;

DROP TABLE IF EXISTS `trade_product_details`;
CREATE TABLE `trade_product_details`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `trade_order_id` bigint(20) NOT NULL COMMENT '关联的订单id',
    `product_id`     varchar(64)    NOT NULL COMMENT '交易的产品号,如:rideId，taxiRideId,goodOrderId以及各种奖励id等',
    `type`           varchar(40)    NOT NULL COMMENT '交易的产品类型,ride:行程,coupon_package:券包,tolls:过路费,extra_fee:感谢费(调度费),highway_fee:高速费,以及各种奖励等',
    `price`          decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '产品价格',
    PRIMARY KEY (`id`),
    KEY              `idx_trade_order_id` (`trade_order_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='交易产品详情表';

DROP TABLE IF EXISTS `trade_product_details_1`;
CREATE TABLE `trade_product_details_1`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `trade_order_id` bigint(20) NOT NULL COMMENT '关联的订单id',
    `product_id`     varchar(64)    NOT NULL COMMENT '交易的产品号,如:rideId，taxiRideId,goodOrderId以及各种奖励id等',
    `type`           varchar(40)    NOT NULL COMMENT '交易的产品类型,ride:行程,coupon_package:券包,tolls:过路费,extra_fee:感谢费(调度费),highway_fee:高速费,以及各种奖励等',
    `price`          decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '产品价格',
    PRIMARY KEY (`id`),
    KEY              `idx_trade_order_id` (`trade_order_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='交易产品详情表';

DROP TABLE IF EXISTS `trade_product_details_2`;
CREATE TABLE `trade_product_details_2`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `trade_order_id` bigint(20) NOT NULL COMMENT '关联的订单id',
    `product_id`     varchar(64)    NOT NULL COMMENT '交易的产品号,如:rideId，taxiRideId,goodOrderId以及各种奖励id等',
    `type`           varchar(40)    NOT NULL COMMENT '交易的产品类型,ride:行程,coupon_package:券包,tolls:过路费,extra_fee:感谢费(调度费),highway_fee:高速费,以及各种奖励等',
    `price`          decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '产品价格',
    PRIMARY KEY (`id`),
    KEY              `idx_trade_order_id` (`trade_order_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='交易产品详情表';

DROP TABLE IF EXISTS `trade_product_details_3`;
CREATE TABLE `trade_product_details_3`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `trade_order_id` bigint(20) NOT NULL COMMENT '关联的订单id',
    `product_id`     varchar(64)    NOT NULL COMMENT '交易的产品号,如:rideId，taxiRideId,goodOrderId以及各种奖励id等',
    `type`           varchar(40)    NOT NULL COMMENT '交易的产品类型,ride:行程,coupon_package:券包,tolls:过路费,extra_fee:感谢费(调度费),highway_fee:高速费,以及各种奖励等',
    `price`          decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '产品价格',
    PRIMARY KEY (`id`),
    KEY              `idx_trade_order_id` (`trade_order_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='交易产品详情表';

DROP TABLE IF EXISTS `trade_product_details_4`;
CREATE TABLE `trade_product_details_4`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `trade_order_id` bigint(20) NOT NULL COMMENT '关联的订单id',
    `product_id`     varchar(64)    NOT NULL COMMENT '交易的产品号,如:rideId，taxiRideId,goodOrderId以及各种奖励id等',
    `type`           varchar(40)    NOT NULL COMMENT '交易的产品类型,ride:行程,coupon_package:券包,tolls:过路费,extra_fee:感谢费(调度费),highway_fee:高速费,以及各种奖励等',
    `price`          decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '产品价格',
    PRIMARY KEY (`id`),
    KEY              `idx_trade_order_id` (`trade_order_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='交易产品详情表';

DROP TABLE IF EXISTS `trade_product_details_5`;
CREATE TABLE `trade_product_details_5`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `trade_order_id` bigint(20) NOT NULL COMMENT '关联的订单id',
    `product_id`     varchar(64)    NOT NULL COMMENT '交易的产品号,如:rideId，taxiRideId,goodOrderId以及各种奖励id等',
    `type`           varchar(40)    NOT NULL COMMENT '交易的产品类型,ride:行程,coupon_package:券包,tolls:过路费,extra_fee:感谢费(调度费),highway_fee:高速费,以及各种奖励等',
    `price`          decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '产品价格',
    PRIMARY KEY (`id`),
    KEY              `idx_trade_order_id` (`trade_order_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='交易产品详情表';

DROP TABLE IF EXISTS `trade_product_details_6`;
CREATE TABLE `trade_product_details_6`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `trade_order_id` bigint(20) NOT NULL COMMENT '关联的订单id',
    `product_id`     varchar(64)    NOT NULL COMMENT '交易的产品号,如:rideId，taxiRideId,goodOrderId以及各种奖励id等',
    `type`           varchar(40)    NOT NULL COMMENT '交易的产品类型,ride:行程,coupon_package:券包,tolls:过路费,extra_fee:感谢费(调度费),highway_fee:高速费,以及各种奖励等',
    `price`          decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '产品价格',
    PRIMARY KEY (`id`),
    KEY              `idx_trade_order_id` (`trade_order_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='交易产品详情表';

DROP TABLE IF EXISTS `trade_product_details_7`;
CREATE TABLE `trade_product_details_7`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `trade_order_id` bigint(20) NOT NULL COMMENT '关联的订单id',
    `product_id`     varchar(64)    NOT NULL COMMENT '交易的产品号,如:rideId，taxiRideId,goodOrderId以及各种奖励id等',
    `type`           varchar(40)    NOT NULL COMMENT '交易的产品类型,ride:行程,coupon_package:券包,tolls:过路费,extra_fee:感谢费(调度费),highway_fee:高速费,以及各种奖励等',
    `price`          decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '产品价格',
    PRIMARY KEY (`id`),
    KEY              `idx_trade_order_id` (`trade_order_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='交易产品详情表';

DROP TABLE IF EXISTS `trade_product_details_8`;
CREATE TABLE `trade_product_details_8`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `trade_order_id` bigint(20) NOT NULL COMMENT '关联的订单id',
    `product_id`     varchar(64)    NOT NULL COMMENT '交易的产品号,如:rideId，taxiRideId,goodOrderId以及各种奖励id等',
    `type`           varchar(40)    NOT NULL COMMENT '交易的产品类型,ride:行程,coupon_package:券包,tolls:过路费,extra_fee:感谢费(调度费),highway_fee:高速费,以及各种奖励等',
    `price`          decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '产品价格',
    PRIMARY KEY (`id`),
    KEY              `idx_trade_order_id` (`trade_order_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='交易产品详情表';

DROP TABLE IF EXISTS `trade_product_details_9`;
CREATE TABLE `trade_product_details_9`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `trade_order_id` bigint(20) NOT NULL COMMENT '关联的订单id',
    `product_id`     varchar(64)    NOT NULL COMMENT '交易的产品号,如:rideId，taxiRideId,goodOrderId以及各种奖励id等',
    `type`           varchar(40)    NOT NULL COMMENT '交易的产品类型,ride:行程,coupon_package:券包,tolls:过路费,extra_fee:感谢费(调度费),highway_fee:高速费,以及各种奖励等',
    `price`          decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '产品价格',
    PRIMARY KEY (`id`),
    KEY              `idx_trade_order_id` (`trade_order_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='交易产品详情表';

DROP TABLE IF EXISTS `trade_product_details_10`;
CREATE TABLE `trade_product_details_10`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `trade_order_id` bigint(20) NOT NULL COMMENT '关联的订单id',
    `product_id`     varchar(64)    NOT NULL COMMENT '交易的产品号,如:rideId，taxiRideId,goodOrderId以及各种奖励id等',
    `type`           varchar(40)    NOT NULL COMMENT '交易的产品类型,ride:行程,coupon_package:券包,tolls:过路费,extra_fee:感谢费(调度费),highway_fee:高速费,以及各种奖励等',
    `price`          decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '产品价格',
    PRIMARY KEY (`id`),
    KEY              `idx_trade_order_id` (`trade_order_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='交易产品详情表';

DROP TABLE IF EXISTS `trade_product_details_11`;
CREATE TABLE `trade_product_details_11`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `trade_order_id` bigint(20) NOT NULL COMMENT '关联的订单id',
    `product_id`     varchar(64)    NOT NULL COMMENT '交易的产品号,如:rideId，taxiRideId,goodOrderId以及各种奖励id等',
    `type`           varchar(40)    NOT NULL COMMENT '交易的产品类型,ride:行程,coupon_package:券包,tolls:过路费,extra_fee:感谢费(调度费),highway_fee:高速费,以及各种奖励等',
    `price`          decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '产品价格',
    PRIMARY KEY (`id`),
    KEY              `idx_trade_order_id` (`trade_order_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='交易产品详情表';

DROP TABLE IF EXISTS `trade_product_details_12`;
CREATE TABLE `trade_product_details_12`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `trade_order_id` bigint(20) NOT NULL COMMENT '关联的订单id',
    `product_id`     varchar(64)    NOT NULL COMMENT '交易的产品号,如:rideId，taxiRideId,goodOrderId以及各种奖励id等',
    `type`           varchar(40)    NOT NULL COMMENT '交易的产品类型,ride:行程,coupon_package:券包,tolls:过路费,extra_fee:感谢费(调度费),highway_fee:高速费,以及各种奖励等',
    `price`          decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '产品价格',
    PRIMARY KEY (`id`),
    KEY              `idx_trade_order_id` (`trade_order_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='交易产品详情表';

DROP TABLE IF EXISTS `trade_product_details_13`;
CREATE TABLE `trade_product_details_13`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `trade_order_id` bigint(20) NOT NULL COMMENT '关联的订单id',
    `product_id`     varchar(64)    NOT NULL COMMENT '交易的产品号,如:rideId，taxiRideId,goodOrderId以及各种奖励id等',
    `type`           varchar(40)    NOT NULL COMMENT '交易的产品类型,ride:行程,coupon_package:券包,tolls:过路费,extra_fee:感谢费(调度费),highway_fee:高速费,以及各种奖励等',
    `price`          decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '产品价格',
    PRIMARY KEY (`id`),
    KEY              `idx_trade_order_id` (`trade_order_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='交易产品详情表';

DROP TABLE IF EXISTS `trade_product_details_14`;
CREATE TABLE `trade_product_details_14`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `trade_order_id` bigint(20) NOT NULL COMMENT '关联的订单id',
    `product_id`     varchar(64)    NOT NULL COMMENT '交易的产品号,如:rideId，taxiRideId,goodOrderId以及各种奖励id等',
    `type`           varchar(40)    NOT NULL COMMENT '交易的产品类型,ride:行程,coupon_package:券包,tolls:过路费,extra_fee:感谢费(调度费),highway_fee:高速费,以及各种奖励等',
    `price`          decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '产品价格',
    PRIMARY KEY (`id`),
    KEY              `idx_trade_order_id` (`trade_order_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='交易产品详情表';

DROP TABLE IF EXISTS `trade_product_details_15`;
CREATE TABLE `trade_product_details_15`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `trade_order_id` bigint(20) NOT NULL COMMENT '关联的订单id',
    `product_id`     varchar(64)    NOT NULL COMMENT '交易的产品号,如:rideId，taxiRideId,goodOrderId以及各种奖励id等',
    `type`           varchar(40)    NOT NULL COMMENT '交易的产品类型,ride:行程,coupon_package:券包,tolls:过路费,extra_fee:感谢费(调度费),highway_fee:高速费,以及各种奖励等',
    `price`          decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '产品价格',
    PRIMARY KEY (`id`),
    KEY              `idx_trade_order_id` (`trade_order_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='交易产品详情表';