USE didapinche_payment;

DROP TABLE IF EXISTS `p_conf_cashier_merchant_rel`;
CREATE TABLE `p_conf_cashier_merchant_rel`
(
    `id`                int(11)     NOT NULL AUTO_INCREMENT COMMENT '主键索引',
    `business`          varchar(64) NOT NULL COMMENT '业务类型，出租车:taxi,出租车智慧码:taxiqr,司机欠款充值:taxi_recharge等',
    `source`            varchar(64) NOT NULL COMMENT '来源，如嘀嗒出行app:didachuxing_app,智慧码小程序:minipg_taxiqr_wx等',
    `channel`           varchar(30) NOT NULL COMMENT '支付渠道，微信:weixin,支付宝:alipay',
    `mch_unique_no`     varchar(32) NOT NULL COMMENT '商户唯一编号,关联商户号配置表p_merchant_info',
    `taxiqr_company_id` int(11)     NOT NULL DEFAULT 0 COMMENT '出租车三化公司id,非出租车三化业务默认0',
    `pay_type`          varchar(64) NOT NULL COMMENT '支付方式,JSAPI:小程序,MWEB:网页支付,SP_JSAPI:小程序服务商模式',
    `app_id`            varchar(32)          DEFAULT NULL COMMENT '应用id,如果存在同一套配置信息多个app_id,需要关联表中定义出app_id',
    `sub_app_id`        varchar(32)          DEFAULT NULL COMMENT '服务商模式下子商户appid',
    `sub_mch_no`        varchar(32)          DEFAULT NULL COMMENT '服务商模式下子子商户号',
    `sub_mch_name`      varchar(64)          DEFAULT NULL COMMENT '服务商模式子商户号授权主体',
    `open`              tinyint(4)           DEFAULT 0 COMMENT '是否可用,0-关闭，1-开启',
    `liquidation`       tinyint(4)           DEFAULT 0 COMMENT '是否对接二清,0-关闭，1-开启',
    `settlement`        tinyint(4)           DEFAULT 0 COMMENT '是否对接结算,0-关闭，1-开启',
    `create_time`       datetime    NOT NULL COMMENT '创建时间',
    `update_time`       datetime    NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `business_source_channel_uidx` (`business`, `source`, `taxiqr_company_id`, `channel`, `open`) USING BTREE COMMENT '联合唯一索引'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='支付配置收银台商户信息关联表（新）';