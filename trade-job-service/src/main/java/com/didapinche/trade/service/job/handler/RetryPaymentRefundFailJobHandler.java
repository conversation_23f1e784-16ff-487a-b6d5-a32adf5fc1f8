package com.didapinche.trade.service.job.handler;

import com.didapinche.trade.infrastructure.thrift.PayThriftClient;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;



@Component
@Slf4j
public class RetryPaymentRefundFailJobHandler {

    @Autowired
    private PayThriftClient payThriftClient;

    @XxlJob("retryPaymentRefundFailJobHandler")
    public void execute() throws Exception {
        payThriftClient.asyncRetryFailedRefund();
        XxlJobHelper.handleSuccess();
    }

}
