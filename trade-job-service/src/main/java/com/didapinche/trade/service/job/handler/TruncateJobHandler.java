package com.didapinche.trade.service.job.handler;

import com.didapinche.agaue.datasource.autoconfigure.DynamicDataSourceProperties;
import com.didapinche.agaue.datasource.common.Routing;
import com.didapinche.agaue.datasource.enums.DatabaseEnum;
import com.didapinche.agaue.datasource.enums.DatabaseTypeEnum;
import com.didapinche.agaue.datasource.toolkit.DynamicDataSourceContextHolder;
import com.didapinche.payment.async.localmsg.core.db.biz.LocalMsgBizService;
import com.didapinche.payment.async.localmsg.core.db.biz.LocalMsgService;
import com.didapinche.payment.async.shard.ShardUtil;
import com.didapinche.trade.infrastructure.util.ChatbotSendUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Component
@Slf4j
public class TruncateJobHandler  {


    private LocalMsgBizService localMsgBizService;

    private LocalMsgService localMsgService;

    private List<String> dbRoutingList;

    public TruncateJobHandler(@Qualifier("dynamicDataSourceProperties") DynamicDataSourceProperties properties, LocalMsgBizService localMsgBizService, LocalMsgService localMsgService) {
        Map<String, List<String>> datasourceShardingSuffix = properties.getDatasourceShardingSuffix();
        this.localMsgBizService=localMsgBizService;
        this.localMsgService = localMsgService;
        dbRoutingList=datasourceShardingSuffix.entrySet().stream().filter(ds -> "order".equals(ds.getKey())).map(ds -> ds.getValue()).flatMap(List::stream).collect(Collectors.toList());
        XxlJobHelper.log("dbRoutingList:{}",dbRoutingList);
    }

    @XxlJob("truncateJobHandler")
    public void execute() {

        for (String dbRouting : dbRoutingList) {
            Routing routing = new Routing().setLookupId(dbRouting);
            DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
            Integer suffix = ShardUtil.truncateTableSuffix();
            if (localMsgService.existFailRecord(suffix) != null){
                ChatbotSendUtil.sendText("https://oapi.dingtalk.com/robot/send?access_token=5277f0f0fcd8bd393d9b7e69c4c061e79553f16586de120d6f8c52286235d83f","消息表清空失败 suffix:"+suffix);
                log.error("有未处理成功的消息 suffix{}",suffix);
                XxlJobHelper.handleSuccess();
                return;
            }
            if (suffix != null){
                localMsgBizService.truncateTable(suffix);
                log.info("清空表成功 {}",suffix);
            }
        }
        XxlJobHelper.handleSuccess();
    }

}
