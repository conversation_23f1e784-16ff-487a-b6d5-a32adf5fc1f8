spring:
  profiles:
    active: dev
  cloud:
    inetutils:
      ignored-interfaces: ^((?!em2)).*
      preferred-networks: 192.168.*
  datasource:
    async:
      sqlSessionFactory: sqlSessionFactory
      sqlSessionTemplate: sqlSessionTemplate
      transactionManager: transactionManager
      defaultMQProducer: defaultEnvMQProducer

jedis:
  enable_clusters: rc9


---
spring:
  profiles: dev
  datasource:
    multi:
      dynamicTable:
        enable: true
        tableName: trade_order,trade_product_details,trade_coupon_details,trade_amount_details,trade_bill,trade_bill_order_mapping
      enable:
        autofillDate: true
        createTimeFieldName: create_time
        dataSourceName: payment,order,didapinche
        autoOrm: mybatis_plus
        updateTimeFieldName: update_time
    async:
      defaultMQProducer: defaultEnvMQProducer

eureka:
  client:
    service-url:
      defaultZone: http://erk-test.didapinche.com:8761/eureka/
    register-with-eureka: false
apollo:
  meta: http://apollo-meta.didapinche.com
  bootstrap:
    enabled: true
    namespaces: application,server.configure.dev,java.rocketmq.default,redis.rc9,bdc.xxl-job.default

dida:
  monitor:
    thrift: true
  method:
    log:
      enable: true

#logging:
#  config: classpath:logback-spring-dev.xml

---
spring:
  profiles: beta
  datasource:
    multi:
      dynamicTable:
        enable: true
        tableName: trade_order,trade_product_details,trade_coupon_details,trade_amount_details,trade_bill,trade_bill_order_mapping
      enable:
        autofillDate: true
        createTimeFieldName: create_time
        dataSourceName: payment,order,didapinche
        autoOrm: mybatis_plus
        updateTimeFieldName: update_time

eureka:
  client:
    serviceUrl:
      defaultZone: http://dongping:8761/eureka/,http://lujunyi:8761/eureka/

dida:
  monitor:
    thrift: true
  method:
    log:
      enable: true

---
spring:
  profiles: production
  datasource:
    multi:
      dynamicTable:
        enable: true
        tableName: trade_order,trade_product_details,trade_coupon_details,trade_amount_details,trade_bill,trade_bill_order_mapping
      enable:
        autofillDate: true
        createTimeFieldName: create_time
        dataSourceName: payment,order,didapinche
        autoOrm: mybatis_plus
        updateTimeFieldName: update_time

eureka:
  client:
    serviceUrl:
      defaultZone: http://192.168.2.100:8761/eureka/

dida:
  monitor:
    thrift: true
  method:
    log:
      enable: true

