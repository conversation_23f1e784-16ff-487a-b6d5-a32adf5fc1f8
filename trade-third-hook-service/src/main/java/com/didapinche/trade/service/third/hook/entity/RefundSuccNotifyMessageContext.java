package com.didapinche.trade.service.third.hook.entity;

import com.didapinche.finance.mq.context.message.messagecontext.BaseContext;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class RefundSuccNotifyMessageContext extends BaseContext {
    /**
     * 支付订单号
     */
    private String payTradeNo;
    /**
     * 商户退款订单号
     */
    private String paymentRefundNo;
    /**
     * 三方退款订单号,不一定有
     */
    private String thirdRefundNo;
    /**
     * 退款金额
     */
    private BigDecimal refundAmount;
    /**
     * 实际退款金额
     */
    private BigDecimal actualRefundAmount;
    /**
     * 退款时间,同步三方时间
     */
    private Date refundTime;

    /**
     * 扩展信息
     */
    private String  refundExtra;

    private Long userId;
    /**
     * 先乘后付取消操作标识
     */
    private Boolean pauCancelFlag = false;
}
