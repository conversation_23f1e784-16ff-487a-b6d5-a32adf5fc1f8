package com.didapinche.trade.service.third.hook.subscriber.pau;

import com.didapinche.commons.rocketmq.AbstractRocketMQSubscriber;
import com.didapinche.pay.service.thrift.*;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.server.commons.common.rocketmq.RocketMQUtil;
import com.didapinche.server.commons.common.util.DateUtil;
import com.didapinche.server.commons.common.util.LoadPropertyUtil;
import com.didapinche.trade.infrastructure.enums.PaymentPauCutFlageEnum;
import com.didapinche.trade.infrastructure.enums.RocketMQGroupEnum;
import com.didapinche.trade.infrastructure.enums.RocketMQTopicEnum;
import com.didapinche.trade.infrastructure.mq.RocketMqSendUtils;
import com.didapinche.trade.infrastructure.mq.bean.PauDoCutEntity;
import com.didapinche.trade.infrastructure.thrift.TradeThriftSupportService;
import com.didapinche.trade.thrift.TradeQueryThriftService;
import com.didapinche.trade.thrift.entities.QueryTradeRequest;
import com.didapinche.trade.thrift.entities.QueryTradeResponse;
import com.didapinche.trade.thrift.enums.TBusinessEnum;
import com.didapinche.trade.thrift.enums.TPaymentWayEnum;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;

@Component
@Slf4j
public class PauCutDouyinpayOrderSubscriber extends AbstractRocketMQSubscriber<PauDoCutEntity> {

    @Resource
    private TradeThriftSupportService tradeThriftSupportService;
    @Resource
    private PayThriftService.Iface payThriftService;
    @Resource
    private TradeQueryThriftService.Iface tradeQueryThriftService;

    @Override
    public void init() {
        setGroup(RocketMQGroupEnum.C_BDC_PAYMENT_DOUYINPAY_PAU_TRADE_ORDER_CUT.name());
        setTopic(RocketMQTopicEnum.t_bdc_payment_pau_trade_order_cut.name());
        setTags(TPaymentWayEnum.douyinpay_pau.name());
        setRetryTimesAware(true);
    }

    @Override
    public void execute(PauDoCutEntity params, int reConsumeTimes) throws Exception {
        log.info("抖音支付先享后付扣款 start. params:{}, reConsumeTimes:{}", JsonMapper.toJson(params), reConsumeTimes);
        if (params.getUserId() <= 0L || params.getTradeOrderId() <= 0L || params.getCutAmount().compareTo(BigDecimal.ZERO) < 0) {
            log.error("抖音支付先享后付扣款 参数错误. params:{}", JsonMapper.toJson(params));
            return;
        }
        if (!tradeThriftSupportService.pauPaymentCutJudgeContinue(params)) {
            log.info("抖音支付无需继续扣款. orderId:{}", params.getTradeOrderId());
            return;
        }
        FinishZhifufenOrderResponse tCutResult=null;
        // 执行扣款操作
        FinishZhifufenOrderRequest request = new FinishZhifufenOrderRequest();
        request.setPayTradeNo(params.getTradeOrderId().toString());
        request.setPaymentAmount(params.getCutAmount().toString());
        request.setUserId(params.getUserId().intValue());
        DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyyMMddHHmmss");
        request.setServerEndTime(formatter.print(System.currentTimeMillis()));
        log.info("cutPauOrder. param={}", JsonMapper.toJson(request));
        String cutRetryLevel = LoadPropertyUtil.getProperty("douyinpay_cut_retry_level", "28");
        try {
            tCutResult = payThriftService.finishDouyinPauOrder(request);
        } catch (Exception e) {
            log.error("抖音支付先享后付扣款，重试.req:{},rsp:{}", JsonMapper.toJson(request), JsonMapper.toJson(tCutResult));
            repeatCut(cutRetryLevel, params, reConsumeTimes);
            return;
        }
//        if (tCutResult == null || tCutResult.getCode() != 0 || (tCutResult.getOrderNo() != null && !tCutResult.getData().isRel())) {
//            String businessType = queryTradeOrderBusinessType(params.getUserId(), params.getTradeOrderId());
//            log.error("抖音支付先享后付扣款，重试.req:{},rsp:{},businessType:{}", JsonMapper.toJson(request), JsonMapper.toJson(tCutResult), businessType);
//            if (TBusinessEnum.mall.name().equals(businessType) && reConsumeTimes == 0) {
//                RocketMqSendUtils.pauCutStatusForActive(params.getUserId(), params.getMajorProductId(), PaymentPauCutFlageEnum.fail.getStatus());
//            }
//            repeatCut(cutRetryLevel, params, reConsumeTimes);
//        }
        log.info("抖音支付先享后付扣款 end. orderId:{}", params.getTradeOrderId());
    }

    private void repeatCut(String cutRetryLevel, PauDoCutEntity params, int reConsumeTimes) throws Exception {
        params.setReConsumeTimes(reConsumeTimes);
        String rowKey = DateUtil.date2String(new Date());
        String mqRowKey = params.getTradeOrderId() + rowKey;
        log.info("抖音支付先享后付扣款 sendDelayMQ mqRowKey:{},params:{},cutRetryLevel:{}", mqRowKey, JsonMapper.toJson(params), cutRetryLevel);
        RocketMQUtil.sendDelayMQ(mqRowKey, params, RocketMQTopicEnum.t_bdc_payment_pau_trade_order_cut.name(), TPaymentWayEnum.douyinpay_pau.name(), Integer.parseInt(cutRetryLevel));
    }

    private String queryTradeOrderBusinessType(long userId, long orderId) {
        QueryTradeRequest queryTradeRequest = new QueryTradeRequest();
        queryTradeRequest.setUserId(userId);
        queryTradeRequest.setOrderIdList(Collections.singletonList(orderId));
        try {
            QueryTradeResponse queryTradeResponse = tradeQueryThriftService.queryTrade(queryTradeRequest);
            if (queryTradeResponse.getData() != null && !queryTradeResponse.getData().isEmpty() && queryTradeResponse.getData().get(0) != null) {
                return queryTradeResponse.getData().get(0).getBusinessType();
            }
        } catch (Exception e) {}
        return null;
    }
}
