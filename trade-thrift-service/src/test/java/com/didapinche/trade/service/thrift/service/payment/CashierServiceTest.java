package com.didapinche.trade.service.thrift.service.payment;

import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.trade.application.service.impl.payment.cashier.CashierServiceImpl;
import com.didapinche.trade.thrift.entities.QueryCashierRequest;
import com.didapinche.trade.thrift.entities.QueryCashierResult;
import com.didapinche.trade.thrift.entities.TRideInfo;
import com.didapinche.trade.thrift.entities.TUserPaymentParam;
import com.didapinche.trade.thrift.enums.TBusinessEnum;
import com.didapinche.trade.thrift.enums.TOrderSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/7/19
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
@Ignore
public class CashierServiceTest {


    @Resource
    private CashierServiceImpl cashierService;

    QueryCashierRequest request = new QueryCashierRequest();
    TUserPaymentParam userPaymentParam = new TUserPaymentParam();
    TRideInfo rideInfo = new TRideInfo();

    @Before
    public void init() {
        userPaymentParam.setUserId(*********);
        userPaymentParam.setMajorProductId("1407858287979238446");
        userPaymentParam.setBusinessType(TBusinessEnum.taxi.name());
        userPaymentParam.setSource(TOrderSourceEnum.didachuxing_app.name());
        userPaymentParam.setVersion("8.29.0");

        rideInfo.setUserId(*********);
        rideInfo.setRideMoney("10");
        rideInfo.setSinglePrice("10");
        rideInfo.setPlanStartTime("2022-08-11 10:26:14");
        request.setUserCid("309a971d-33c3-48e7-bde7-ebc41172ee64");
        request.setUserPaymentParam(userPaymentParam);
        request.setRideInfo(rideInfo);
    }

    @Test
    public void queryCashier() {
        try {
            QueryCashierResult result = cashierService.doTrade(request);
            log.info("res:{}", JsonMapper.toJson(result));
        } catch (Exception e) {
            log.error("e", e);
        }
    }

    @Test
    public void queryCashierNoRide() {
        try {
            userPaymentParam.setBusinessType(TBusinessEnum.carpool_highway.name());
            request.unsetRideInfo();
            QueryCashierResult result = cashierService.doTrade(request);
            log.info("res:{}", JsonMapper.toJson(result));
        } catch (Exception e) {
            log.error("e", e);
        }
    }

}
