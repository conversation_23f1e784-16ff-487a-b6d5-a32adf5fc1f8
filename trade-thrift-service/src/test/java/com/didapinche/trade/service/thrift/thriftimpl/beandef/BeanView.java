package com.didapinche.trade.service.thrift.thriftimpl.beandef;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.stereotype.Component;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

//@Component
public class BeanView implements BeanDefinitionRegistryPostProcessor {

    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {
//        String[] beanDefinitionNames = registry.getBeanDefinitionNames();
//        File file = new File("beanDefinitionNames.txt");
//        try {
//            FileWriter fileWriter = new FileWriter(file);
//            BufferedWriter bufferedWriter = new BufferedWriter(fileWriter);
//            for (String bean : beanDefinitionNames) {
//                bufferedWriter.write(bean);
//                bufferedWriter.newLine();
//            }
//            bufferedWriter.flush();
//        } catch (IOException e) {
//            //ignore
//        }
        registry.removeBeanDefinition("thriftMonitor");
        registry.removeBeanDefinition("tracingFilter");
        registry.removeBeanDefinition("tracingHandlerInterceptor");
//        registry.removeBeanDefinition("auroraAutoConfiguration");
    }

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {

    }
}
