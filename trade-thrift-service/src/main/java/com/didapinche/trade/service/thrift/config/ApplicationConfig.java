package com.didapinche.trade.service.thrift.config;

import com.didapinche.payment.async.shard.IShardService;
import com.didapinche.payment.async.shard.WeekShardServiceImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Created by yangyongxin on 2017/10/15.
 */
@Configuration
public class ApplicationConfig {
    @Bean
    IShardService shardService(){
        return new WeekShardServiceImpl();
    }

}
