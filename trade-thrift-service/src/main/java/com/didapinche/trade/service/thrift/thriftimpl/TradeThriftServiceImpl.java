package com.didapinche.trade.service.thrift.thriftimpl;

import com.didapinche.agaue.common.exception.DException;
import com.didapinche.agaue.common.exception.DidaCode;
import com.didapinche.agaue.common.exception.SuccessCode;
import com.didapinche.agaue.common.result.Result;
import com.didapinche.agaue.common.utils.JSONUtil;
import com.didapinche.agaue.core.annotation.LogOpen;
import com.didapinche.agaue.datasource.common.Routing;
import com.didapinche.agaue.datasource.enums.DatabaseEnum;
import com.didapinche.agaue.datasource.enums.DatabaseTypeEnum;
import com.didapinche.agaue.datasource.toolkit.DynamicDataSourceContextHolder;
import com.didapinche.agaue.spring.template.DefaultThriftService;
import com.didapinche.agaue.spring.template.ThriftServiceWithCodeAction;
import com.didapinche.agaue.spring.template.ThriftServiceWithLogkeyAction;
import com.didapinche.finance.mq.context.enums.TradeTypeEnum;
import com.didapinche.payment.backupcheck.BackupCheck;
import com.didapinche.payment.dislock.DisLock;
import com.didapinche.payment.frigate.Frigate;
import com.didapinche.risk.sdk.RiskBarrier;
import com.didapinche.risk.sdk.helper.IServerRiskCallback;
import com.didapinche.risk.sdk.intercept.CheckRiskClientResponse;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.trade.application.service.impl.TableManagerService;
import com.didapinche.trade.application.service.impl.cheat.SetSysOrderGroupServiceImpl;
import com.didapinche.trade.application.service.impl.cheat.UpdateHoldOrderStatusServiceImpl;
import com.didapinche.trade.application.service.impl.compensate.CompensateServiceImpl;
import com.didapinche.trade.application.service.impl.confiscate.CommonConfiscateServiceImpl;
import com.didapinche.trade.application.service.impl.confiscate.ConfiscateBacktrackServiceImpl;
import com.didapinche.trade.application.service.impl.confiscate.OrderSetTagAndConfiscateServiceImpl;
import com.didapinche.trade.application.service.impl.order.UpdateTradeAmountDetailReceiptStatusServiceImpl;
import com.didapinche.trade.application.service.impl.payment.CallBackTradeService;
import com.didapinche.trade.application.service.impl.payment.CheckPaymentServiceImpl;
import com.didapinche.trade.application.service.impl.payment.PaymentOfflineServiceImpl;
import com.didapinche.trade.application.service.impl.payment.PaymentServiceImpl;
import com.didapinche.trade.application.service.impl.payment.RechargePayServiceImpl;
import com.didapinche.trade.application.service.impl.payment.ScanPayServiceImpl;
import com.didapinche.trade.application.service.impl.payment.cashier.CashierServiceImpl;
import com.didapinche.trade.application.service.impl.payment.pau.PauAddPaymentServiceImpl;
import com.didapinche.trade.application.service.impl.payment.pau.PauPaymentApplicationService;
import com.didapinche.trade.application.service.impl.payment.pau.PauPaymentCutFinishServiceImpl;
import com.didapinche.trade.application.service.impl.payment.pau.PauPaymentCutJudgeContinueServiceImpl;
import com.didapinche.trade.application.service.impl.payment.pau.PauPaymentCutServiceImpl;
import com.didapinche.trade.application.service.impl.payment.pau.PauPaymentServiceImpl;
import com.didapinche.trade.application.service.impl.query.TradeQueryImpl;
import com.didapinche.trade.application.service.impl.refund.AllRefundServiceImpl;
import com.didapinche.trade.application.service.impl.refund.CancelOrderRefundServiceImpl;
import com.didapinche.trade.application.service.impl.refund.CarpoolHighwayRefundServiceImpl;
import com.didapinche.trade.application.service.impl.refund.CarpoolMultiRefundServiceImpl;
import com.didapinche.trade.application.service.impl.refund.CmsRefundServiceImpl;
import com.didapinche.trade.application.service.impl.refund.PriceDifferencesRefundServiceImpl;
import com.didapinche.trade.application.service.impl.refund.ThirdCashRefundServiceImpl;
import com.didapinche.trade.application.service.impl.refund.ThirdRefundServiceImpl;
import com.didapinche.trade.application.service.impl.remittance.CarpoolFreeService;
import com.didapinche.trade.application.service.impl.remittance.RemitService;
import com.didapinche.trade.application.service.impl.settle.PayAdvancePreSettleServiceImpl;
import com.didapinche.trade.application.service.impl.settle.SendAliceMqToLiquidationServiceImpl;
import com.didapinche.trade.application.service.impl.tencent.TencentBillNotifyService;
import com.didapinche.trade.application.service.impl.transfer.CommonTransferServiceImpl;
import com.didapinche.trade.application.service.impl.transfer.TaxiQrTransferConfirmServiceImpl;
import com.didapinche.trade.application.service.impl.transfer.TaxiQrTransferServiceImpl;
import com.didapinche.trade.application.service.impl.transfer.TransferWithCheckPayServiceImpl;
import com.didapinche.trade.application.service.support.RedisActionService;
import com.didapinche.trade.application.service.support.cleardata.CleanOrderDataService;
import com.didapinche.trade.application.service.support.platform.TradePlatformSupportService;
import com.didapinche.trade.infrastructure.constants.TradeConstants;
import com.didapinche.trade.infrastructure.database.DBRoutingService;
import com.didapinche.trade.infrastructure.exception.TradeErrorCode;
import com.didapinche.trade.infrastructure.metric.TradeMetric;
import com.didapinche.trade.infrastructure.thrift.RoutingThriftSupportService;
import com.didapinche.trade.infrastructure.util.LockUtil;
import com.didapinche.trade.infrastructure.util.TableSuffixUtils;
import com.didapinche.trade.infrastructure.util.TradePlatformGrayUtil;
import com.didapinche.trade.thrift.TradeThriftService;
import com.didapinche.trade.thrift.entities.*;
import com.didapinche.trade.thrift.enums.TBusinessEnum;
import com.didapinche.trade.thrift.enums.TOrderSourceEnum;
import com.didapinche.trade.thrift.enums.TPaymentWayEnum;
import com.didapinche.trade.thrift.enums.TradeOrderTypeEnum;
import com.didapinche.zeus.ZeusServer;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.aopalliance.intercept.MethodInvocation;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;

import static com.didapinche.trade.infrastructure.exception.TradeErrorCode.SYSTEM_ERROR;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @date 18:42 2022/6/1
 **/
@ZeusServer(thrift = TradeThriftService.class)
@Slf4j
public class TradeThriftServiceImpl extends DefaultThriftService implements TradeThriftService.Iface , IServerRiskCallback {

    private static final String LOG_KEY_FORMAT = "%s:%s";
    private static final String LOCK_KEY_FORMAT = "%s_%s_%s";

    @Resource
    private PaymentServiceImpl paymentService;
    @Resource
    private PaymentOfflineServiceImpl paymentOfflineService;
    @Resource
    private CommonTransferServiceImpl transferService;
    @Resource
    private CommonConfiscateServiceImpl commonConfiscateService;
    @Resource
    private CashierServiceImpl cashierService;
    @Resource
    private CancelOrderRefundServiceImpl thirdCashDeductRefundService;
    @Resource
    private CmsRefundServiceImpl cmsRefundService;
    @Resource
    private ThirdCashRefundServiceImpl thirdCashRefundService;
    @Resource
    private ThirdRefundServiceImpl thirdRefundService;
    @Resource
    private PriceDifferencesRefundServiceImpl priceDifferencesRefundService;
    @Resource
    private AllRefundServiceImpl allRefund;
    @Resource
    private RoutingThriftSupportService routingThriftService;
    @Resource
    private CheckPaymentServiceImpl checkPaymentService;
    @Resource
    private TransferWithCheckPayServiceImpl transferWithCheckPayService;
    @Resource
    private CarpoolFreeService carpoolFreeService;
    @Resource
    private RemitService remitService;
    @Resource
    private CarpoolMultiRefundServiceImpl carpoolMultiRefundService;
    @Resource
    private CarpoolHighwayRefundServiceImpl carpoolHighwayRefundService;

    @Resource
    private PayAdvancePreSettleServiceImpl payAdvancePreSettleService;

    @Resource
    private ScanPayServiceImpl scanPayService;
    @Resource
    private RechargePayServiceImpl rechargePayService;
    @Resource
    private CallBackTradeService callBackTradeService;
    @Resource
    private PauPaymentServiceImpl pauPaymentService;
    @Resource
    private PauAddPaymentServiceImpl pauAddPaymentService;
    @Resource
    private PauPaymentCutJudgeContinueServiceImpl pauPaymentCutJudgeContinueService;
    @Resource
    private PauPaymentCutServiceImpl pauPaymentCutService;
    @Resource
    private PauPaymentCutFinishServiceImpl pauPaymentCutFinishService;
    @Resource
    private ConfiscateBacktrackServiceImpl confiscateBacktrackService;
    @Resource
    private PauPaymentApplicationService pauPaymentApplicationService;
    @Resource
    private RedisActionService redisActionService;
    @Resource
    private TradeQueryImpl tradeQueryService;
    @Resource
    private SetSysOrderGroupServiceImpl setSysOrderGroupService;
    @Resource
    private UpdateHoldOrderStatusServiceImpl updateHoldOrderStatusService;
    @Resource
    private OrderSetTagAndConfiscateServiceImpl orderSetTagAndConfiscateService;
    @Resource
    private SendAliceMqToLiquidationServiceImpl sendAliceMqToLiquidationService;
    @Resource
    private TaxiQrTransferServiceImpl taxiQrTransferService;
    @Resource
    private TaxiQrTransferConfirmServiceImpl taxiQrTransferConfirmService;
    @Resource
    private UpdateTradeAmountDetailReceiptStatusServiceImpl updateTradeAmountDetailReceiptStatusService;

    @Autowired
    private DBRoutingService dbRoutingService;

    @Autowired
    private TableManagerService tableManagerService;
    @Resource
    private CleanOrderDataService cleanOrderDataService;

    @Autowired
    private CompensateServiceImpl compensateService;
    @Autowired
    private TencentBillNotifyService tencentBillNotifyService;
    @Resource
    private TradePlatformSupportService tradePlatformSupportService;

    @Override
    @LogOpen(value = "支付", pluto = true)
    @Frigate(name ="支付")
    @DisLock("'trade:'+#request.businessType+':'+#request.majorProductId+':payment'")
    @RiskBarrier
    public PaymentResponse doPayment(PaymentRequest request) {
        ThriftServiceWithLogkeyAction<PaymentResult> action = new ThriftServiceWithLogkeyAction<PaymentResult>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, request.getBusinessType(), request.getMajorProductId());
            }

            @Override
            public DidaCode checkParam() throws DException {
                return paymentService.verifyParameter(request);
            }

            @Override
            public PaymentResult doAction() {
                LockUtil<PaymentResult> lockUtil = new LockUtil<>(String.format(LOCK_KEY_FORMAT, request.getBusinessType(),
                        request.getMajorProductId(), TradeTypeEnum.payment.name()), () -> paymentService.doTrade(request));
                return lockUtil.trade();
            }
        };
        Result<PaymentResult> result = this.execute(action);
        if (result.getCode() == 0) {
            TradeMetric.payment(request.getBusinessType(), request.getSource());
        }else {
            TradeMetric.paymentError(request.getBusinessType(), request.getSource());
        }
        if (StringUtils.equalsAny(request.getPaymentWay(), TPaymentWayEnum.zhima_pau.name(),TPaymentWayEnum.zhima_pau_sign.name(),TPaymentWayEnum.weixin_pau.name(), TPaymentWayEnum.douyinpay_pau.name(),TPaymentWayEnum.douyinpay_pau_sign.name())) {
            TradeMetric.common("pau", TradeTypeEnum.payment.name(), result.getCode());
        }
        return new PaymentResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    @LogOpen(value = "线下支付", pluto = true)
    @Frigate(name ="线下支付")
    @DisLock("'trade:'+#request.businessType+':'+#request.majorProductId+':payment'")
    @RiskBarrier
    public PaymentResponse doPaymentOffline(PaymentOfflineRequest request) {
        ThriftServiceWithLogkeyAction<PaymentResult> action = new ThriftServiceWithLogkeyAction<PaymentResult>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, request.getBusinessType(), request.getMajorProductId());
            }

            @Override
            public DidaCode checkParam() throws DException {
                return paymentOfflineService.verifyParameter(request);
            }

            @Override
            public PaymentResult doAction() {
                Routing routing = new Routing()
                        .setLookupId(routingThriftService.getLookupKeyById(request.majorProductId))
                        .setDynamicTableSuffix(TableSuffixUtils.getSuffix(request.getUserId()));
                DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                LockUtil<PaymentResult> lockUtil = new LockUtil<>(String.format(LOCK_KEY_FORMAT, request.getBusinessType(),
                        request.getMajorProductId(), TradeTypeEnum.payment.name()), () -> paymentOfflineService.doTrade(request));
                return lockUtil.trade();
            }
        };
        Result<PaymentResult> result = this.execute(action);
        try {
            if (result.getCode() == 0) {
                TradeMetric.payment(request.getBusinessType(), request.getSource());
            }else {
                TradeMetric.paymentError(request.getBusinessType(), request.getSource());
            }
        }catch (Exception ex){
            //ignore ex
        }
        return new PaymentResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    /**
     * 取消订单，且扣除补偿金，且补偿金<=三方+余额
     * @param thirdCashDeductRefundRequest
     * @return
     */
    @Override
    @LogOpen(value = "取消订单退款", pluto = true)
    @Frigate(name ="取消订单退款补偿金额")
    @DisLock("'trade:'+#thirdCashDeductRefundRequest.majorOrderId")
    @RiskBarrier
    public RefundResponse thirdCashDeductRefund(ThirdCashDeductRefundRequest thirdCashDeductRefundRequest) {
        ThriftServiceWithCodeAction<List<RefundResult>> action = new ThriftServiceWithCodeAction<List<RefundResult>>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, thirdCashDeductRefundRequest.getBusinessType(), thirdCashDeductRefundRequest.getProductId());
            }

            @Override
            public DidaCode checkParam() throws DException {
                return thirdCashDeductRefundService.verifyParameter(thirdCashDeductRefundRequest);
            }

            @Override
            public Result<List<RefundResult>> doAction() {
                LockUtil<Result<List<RefundResult>>> lockUtil = new LockUtil<Result<List<RefundResult>>>(thirdCashDeductRefundRequest.getMajorOrderId(), () -> {
                    Routing routing = new Routing()
                            .setLookupId(routingThriftService.getLookupKeyById(String.valueOf(thirdCashDeductRefundRequest.getMajorOrderId())))
                            .setDynamicTableSuffix(TableSuffixUtils.getSuffix(thirdCashDeductRefundRequest.getUserId()));
                    DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                    pauPaymentApplicationService.sendPaymentTheaAndLiq(thirdCashDeductRefundRequest.getMajorOrderId(), thirdCashDeductRefundRequest.getUserId());
                    return thirdCashDeductRefundService.doTrade(thirdCashDeductRefundRequest);
                });
                return lockUtil.trade();
            }
        };
        Result<List<RefundResult>> result = this.execute(action);
        try {
            TradeMetric.common(thirdCashDeductRefundRequest.getBusinessType(),TradeTypeEnum.refund.name(), result.getCode());
        }catch (Exception ex){
            log.error("监控err",ex);
        }
        return new RefundResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    @LogOpen(value = "线下操作订单退款", pluto = true)
    @Frigate(name ="线下操作订单退款")
    @DisLock("'trade:'+#cmsRefundRequest.majorOrderId")
    @BackupCheck(value = "cmsRefund")
    @RiskBarrier
    public RefundResponse cmsRefund(CmsRefundRequest cmsRefundRequest) {
        ThriftServiceWithCodeAction<List<RefundResult>> action = new ThriftServiceWithCodeAction<List<RefundResult>>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, cmsRefundRequest.getBusinessType(), cmsRefundRequest.getProductId());
            }

            @Override
            public DidaCode checkParam() throws DException {
                return cmsRefundService.verifyParameter(cmsRefundRequest);
            }

            @Override
            public Result<List<RefundResult>> doAction() {
                LockUtil<Result<List<RefundResult>>> lockUtil = new LockUtil<Result<List<RefundResult>>>(cmsRefundRequest.getMajorOrderId(), () -> {
                    Routing routing = new Routing()
                            .setLookupId(routingThriftService.getLookupKeyById(String.valueOf(cmsRefundRequest.getMajorOrderId())));
                    DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                    return cmsRefundService.doTrade(cmsRefundRequest);
                });
                return lockUtil.trade();
            }
        };
        Result<List<RefundResult>> result = this.execute(action);
        try {
            TradeMetric.common(cmsRefundRequest.getBusinessType(),TradeTypeEnum.refund.name(), result.getCode());
        }catch (Exception ex){
            log.error("监控err",ex);
        }
        return new RefundResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    /**
     * 退三方和余额（顺风车调用）
     * @param thirdCashRefundRequest
     * @return
     */
    @Override
    @LogOpen(value = "取消金退款", pluto = true)
    @Frigate(name ="取消金退款")
    @DisLock("'trade:'+#thirdCashRefundRequest.majorOrderId")
    @BackupCheck(value = "thirdCashRefund")
    @RiskBarrier
    public RefundResponse thirdCashRefund(ThirdCashRefundRequest thirdCashRefundRequest) {
        ThriftServiceWithCodeAction<List<RefundResult>> action = new ThriftServiceWithCodeAction<List<RefundResult>>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, thirdCashRefundRequest.getBusinessType(), thirdCashRefundRequest.getProductId());
            }

            @Override
            public DidaCode checkParam() throws DException {
                return thirdCashRefundService.verifyParameter(thirdCashRefundRequest);
            }

            @Override
            public Result<List<RefundResult>> doAction() {
                LockUtil<Result<List<RefundResult>>> lockUtil = new LockUtil<Result<List<RefundResult>>>(thirdCashRefundRequest.getMajorOrderId(), () -> {
                    Routing routing = new Routing()
                            .setLookupId(routingThriftService.getLookupKeyById(String.valueOf(thirdCashRefundRequest.getMajorOrderId())));
                    DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                    return thirdCashRefundService.doTrade(thirdCashRefundRequest);
                });
                return lockUtil.trade();
            }
        };
        Result<List<RefundResult>> result = this.execute(action);
        try {
            TradeMetric.common(thirdCashRefundRequest.getBusinessType(),TradeTypeEnum.refund.name(), result.getCode());
        }catch (Exception ex){
            log.error("监控err",ex);
        }
        return new RefundResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    @LogOpen(value = "顺风车合拼返款", pluto = true)
    @Frigate(name ="顺风车合拼返款")
    @DisLock("'trade:'+#carpoolMultiRefundRequest.majorOrderId")
    @BackupCheck(value = "carpoolMultiRefund")
    @RiskBarrier
    public RefundResponse carpoolMultiRefund(CarpoolMultiRefundRequest carpoolMultiRefundRequest) {
        ThriftServiceWithCodeAction<List<RefundResult>> action = new ThriftServiceWithCodeAction<List<RefundResult>>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, carpoolMultiRefundRequest.getBusinessType(), carpoolMultiRefundRequest.getProductId());
            }

            @Override
            public DidaCode checkParam() throws DException {
                return carpoolMultiRefundService.verifyParameter(carpoolMultiRefundRequest);
            }


            @Override
            public Result<List<RefundResult>> doAction() {
                LockUtil<Result<List<RefundResult>>> lockUtil = new LockUtil<Result<List<RefundResult>>>(carpoolMultiRefundRequest.getMajorOrderId(), () -> {
                    Routing routing = new Routing()
                            .setLookupId(routingThriftService.getLookupKeyById(String.valueOf(carpoolMultiRefundRequest.getMajorOrderId())))
                            .setDynamicTableSuffix(TableSuffixUtils.getSuffix(carpoolMultiRefundRequest.getUserId()));
                    DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                    return carpoolMultiRefundService.doTrade(carpoolMultiRefundRequest);
                });
                return lockUtil.trade();
            }
        };
        Result<List<RefundResult>> result = this.execute(action);
        try {
            TradeMetric.common(carpoolMultiRefundRequest.getBusinessType(),TradeTypeEnum.refund.name(), result.getCode());
        }catch (Exception ex){
            log.error("监控err",ex);
        }
        return new RefundResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    public RefundResponse carpoolInviteReductionRefund(CarpoolInviteReductionRequest carpoolInviteReductionRequest) throws TException {
        return null;
    }

    @Override
    @LogOpen(value = "退差价", pluto = true)
    @Frigate(name ="退差价")
    @DisLock("'trade:'+#priceDifferencesRefundRequest.majorOrderId")
    @BackupCheck(value = "priceDifferencesRefund")
    @RiskBarrier
    public RefundResponse priceDifferencesRefund(PriceDifferencesRefundRequest priceDifferencesRefundRequest) {
        ThriftServiceWithCodeAction<List<RefundResult>> action = new ThriftServiceWithCodeAction<List<RefundResult>>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, priceDifferencesRefundRequest.getBusinessType(), priceDifferencesRefundRequest.getProductId());
            }

            @Override
            public DidaCode checkParam() throws DException {
                return priceDifferencesRefundService.verifyParameter(priceDifferencesRefundRequest);
            }

            @Override
            public Result<List<RefundResult>> doAction() {
                LockUtil<Result<List<RefundResult>>> lockUtil = new LockUtil<Result<List<RefundResult>>>(priceDifferencesRefundRequest.getMajorOrderId(), () -> {
                    Routing routing = new Routing()
                            .setLookupId(routingThriftService.getLookupKeyById(String.valueOf(priceDifferencesRefundRequest.getMajorOrderId())));
                    DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                    return priceDifferencesRefundService.doTrade(priceDifferencesRefundRequest);
                });
                return lockUtil.trade();
            }
        };
        Result<List<RefundResult>> result = this.execute(action);
        try {
            TradeMetric.common(priceDifferencesRefundRequest.getBusinessType(),TradeTypeEnum.refund.name(), result.getCode());
        }catch (Exception ex){
            log.error("监控err",ex);
        }
        return new RefundResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    /**
     * 只退三方金额
     * @param thirdRefundRequest
     * @return
     */
    @Override
    @LogOpen(value = "第三方金额退款", pluto = true)
    @DisLock("'trade:'+#thirdRefundRequest.orderId")
    @BackupCheck(value = "thirdRefund")
    @RiskBarrier
    public RefundResponse thirdRefund(ThirdRefundRequest thirdRefundRequest) {
        ThriftServiceWithCodeAction<List<RefundResult>> action = new ThriftServiceWithCodeAction<List<RefundResult>>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, thirdRefundRequest.getBusinessType(), thirdRefundRequest.getProductId());
            }

            @Override
            public DidaCode checkParam() throws DException {
                return thirdRefundService.verifyParameter(thirdRefundRequest);
            }

            @Override
            public Result<List<RefundResult>> doAction() {
                LockUtil<Result<List<RefundResult>>> lockUtil = new LockUtil<Result<List<RefundResult>>>(thirdRefundRequest.getOrderId(), () -> {
                    Routing routing = new Routing()
                            .setLookupId(routingThriftService.getLookupKeyById(String.valueOf(thirdRefundRequest.getOrderId())));
                    DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                    return thirdRefundService.doTrade(thirdRefundRequest);
                });
                return lockUtil.trade();
            }
        };
        Result<List<RefundResult>> result = this.execute(action);
        try {
            TradeMetric.common(thirdRefundRequest.getBusinessType(),TradeTypeEnum.refund.name(), result.getCode());
        }catch (Exception ex){
            log.error("监控err",ex);
        }
        return new RefundResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    @LogOpen(value = "高速费退款", pluto = true)
    @Frigate(name ="高速费退款")
    @DisLock("'trade:'+#request.orderId")
    @BackupCheck(value = "carpoolHighwayRefund")
    @RiskBarrier
    public CarpoolHighwayResponse carpoolHighwayRefund(CarpoolHighwayRequest request) {
        ThriftServiceWithCodeAction<CarpoolHighwayResponse> action = new ThriftServiceWithCodeAction<CarpoolHighwayResponse>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, TBusinessEnum.carpool_highway.name(), request.getProductId());
            }

            @Override
            public DidaCode checkParam() throws DException {
                return carpoolHighwayRefundService.verifyParameter(request);
            }

            @Override
            public Result<CarpoolHighwayResponse> doAction() {
                LockUtil<Result<CarpoolHighwayResponse>> lockUtil = new LockUtil<Result<CarpoolHighwayResponse>>(request.getOrderId(), () -> {
                    Routing routing = new Routing()
                            .setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getOrderId())));
                    DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                    return carpoolHighwayRefundService.doTrade(request);
                });
                return lockUtil.trade();
            }
        };
        Result<CarpoolHighwayResponse> result = this.execute(action);
        if (result.getData() != null) {
            return result.getData().setCode(result.getCode()).setMessage(result.getMessage());
        }
        try {
            TradeMetric.common(TBusinessEnum.carpool_highway_fee.name(),TradeTypeEnum.refund.name(), result.getCode());
        }catch (Exception ex){
            log.error("监控err",ex);
        }
        return new CarpoolHighwayResponse().setCode(result.getCode()).setMessage(result.getMessage());
    }

    @Override
    @LogOpen(value = "重复支付退款", pluto = true)
    @Frigate(name ="重复支付退款")
    @DisLock("'trade:'+#repeatRefundRequest.orderId")
    @BackupCheck(value = "allRefund")
    @RiskBarrier
    public RefundResponse allRefund(AllRefundRequest repeatRefundRequest) {
        ThriftServiceWithCodeAction<List<RefundResult>> action = new ThriftServiceWithCodeAction<List<RefundResult>>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, repeatRefundRequest.getBusinessType(), repeatRefundRequest.getProductId());
            }

            @Override
            public DidaCode checkParam() throws DException {
                return allRefund.verifyParameter(repeatRefundRequest);
            }

            @Override
            public Result<List<RefundResult>> doAction() {
                LockUtil<Result<List<RefundResult>>> lockUtil = new LockUtil<Result<List<RefundResult>>>(repeatRefundRequest.getOrderId(), () -> {
                    Routing routing = new Routing()
                            .setLookupId(routingThriftService.getLookupKeyById(String.valueOf(repeatRefundRequest.getOrderId())));
                    DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                    pauPaymentApplicationService.sendPaymentTheaAndLiq(repeatRefundRequest.getOrderId(), repeatRefundRequest.getUserId());
                    return allRefund.doTrade(repeatRefundRequest);
                });
                return lockUtil.trade();
            }
        };
        Result<List<RefundResult>> result = this.execute(action);
        try {
            TradeMetric.common(repeatRefundRequest.getBusinessType(),TradeTypeEnum.refund.name(), result.getCode());
        }catch (Exception ex){
            log.error("监控err",ex);
        }
        return new RefundResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    @LogOpen(value = "转账", pluto = true)
    @Frigate(name ="转账")
    @DisLock("#request.payTradeId > 0 ? 'trade:'+#request.payTradeId : 'trade:'+#request.businessType+':'+#request.productId+':transfer'")
    @BackupCheck(value = "commonTransfer")
    @RiskBarrier
    public TransferResponse doCommonTransfer(CommonTransferRequest request) {
        ThriftServiceWithCodeAction<TransferResult> action = new ThriftServiceWithCodeAction<TransferResult>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, request.getBusinessType(), request.getProductId());
            }

            @Override
            public DidaCode checkParam() throws DException {
                return transferService.verifyParameter(request);
            }

            @Override
            public Result<TransferResult> doAction() {
                LockUtil<Result<TransferResult>> lockUtil = new LockUtil<Result<TransferResult>>(String.format(LOCK_KEY_FORMAT, request.getBusinessType(), request.getProductId(), TradeTypeEnum.transfer.name()), () -> {
                    Routing routing = new Routing()
                            .setLookupId(routingThriftService.getLookupKeyById(request.getPayTradeId() > 0 ? String.valueOf(request.getPayTradeId()) : request.getProductId()));
                    DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                    return transferService.doTrade(request);
                });
                return lockUtil.trade();
            }
        };
        Result<TransferResult> result = this.execute(action);
        try {
            TradeMetric.common(request.getBusinessType(),TradeTypeEnum.transfer.name(), result.getCode());
        }catch (Exception ex){
            log.error("监控err",ex);
        }
        return new TransferResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    @LogOpen(value = "有支付订单的到账", pluto = true)
    @Frigate(name ="有支付订单的到账")
    @DisLock("'trade:'+#request.payTradeId")
    @BackupCheck(value = "transferWithCheckPay")
    @RiskBarrier
    public TransferResponse doTransferWithCheckPay(TransferWithCheckPayRequest request) {
        ThriftServiceWithCodeAction<TransferResult> action = new ThriftServiceWithCodeAction<TransferResult>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, request.getBusinessType(), request.getProductId());
            }

            @Override
            public DidaCode checkParam() throws DException {
                return transferWithCheckPayService.verifyParameter(request);
            }

            @Override
            public Result<TransferResult> doAction() {
                LockUtil<Result<TransferResult>> lockUtil = new LockUtil<Result<TransferResult>>(request.getPayTradeId(), () -> {
                    Routing routing = new Routing()
                            .setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getPayTradeId())));
                    DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                    pauPaymentApplicationService.sendPaymentTheaAndLiq(request.getPayTradeId(), request.getPayUserId());
                    return transferWithCheckPayService.doTrade(request);
                });
                return lockUtil.trade();


            }
        };
        Result<TransferResult> result = this.execute(action);
        try {
            TradeMetric.common(request.getBusinessType(),TradeTypeEnum.transfer.name(), result.getCode());
        }catch (Exception ex){
            log.error("监控err",ex);
        }
        return new TransferResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    @LogOpen(value = "没收", pluto = true)
    @Frigate(name ="没收")
    //HPCDO 在分布式锁中有使用
    @DisLock("#request.payTradeId > 0 ? 'trade:'+#request.payTradeId : 'trade:'+#request.businessType+':'+#request.productId+':confiscate'")
    @BackupCheck(value = "commonConfiscate")
    @RiskBarrier
    public ConfiscateResponse doCommonConfiscate(CommonConfiscateRequest request) {
        ThriftServiceWithCodeAction<ConfiscateResult> action = new ThriftServiceWithCodeAction<ConfiscateResult>() {
            @Override
            public String addLogKey() {
                //HPCDO 日志中使用，
                return String.format(LOG_KEY_FORMAT, request.getBusinessType(), request.getProductId());
            }

            @Override
            public DidaCode checkParam() throws DException {
                return commonConfiscateService.verifyParameter(request);
            }

            @Override
            public Result<ConfiscateResult> doAction() {
                LockUtil<Result<ConfiscateResult>> lockUtil = new LockUtil<Result<ConfiscateResult>>(String.format(LOCK_KEY_FORMAT, request.getBusinessType(), request.getProductId(), TradeTypeEnum.confiscate.name()), () -> {
                    //HPCDO 在获取路由信息中有使用
                    Routing routing = new Routing()
                            .setLookupId(routingThriftService.getLookupKeyById(request.getPayTradeId() > 0 ? String.valueOf(request.getPayTradeId()) : request.getProductId()));
                    DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                    return commonConfiscateService.doTrade(request);
                });
                return lockUtil.trade();
            }
        };
        Result<ConfiscateResult> result = this.execute(action);
        try {
            TradeMetric.common(request.getBusinessType(),TradeTypeEnum.confiscate.name(), result.getCode());
        }catch (Exception ex){
            log.error("监控err",ex);
        }
        return new ConfiscateResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    @LogOpen(value = "trade赔付doCompensate", pluto = true)
    @DisLock("'trade:'+#request.compensateProductId")
    @RiskBarrier
    public CompensateResponse doCompensate(CompensateRequest request) {
        ThriftServiceWithCodeAction<CompensateResponse> action = new ThriftServiceWithCodeAction<CompensateResponse>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, request.getBusinessType(), request.getCompensateProductId());
            }

            @Override
            public DidaCode checkParam() throws DException {
                return compensateService.verifyParameter(request);
            }

            @Override
            public Result<CompensateResponse> doAction() {
                Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(request.getCompensateProductId()));
                DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                try {
                    return compensateService.doTrade(request);
                } catch (Exception e) {
                    log.error("doCompensate.error. request={}", JsonMapper.toJson(request), e);
                    throw new DException(SYSTEM_ERROR);
                }
            }
        };
        Result<CompensateResponse> result = this.execute(action);
        return result.getData();
    }

    @Override
    @LogOpen(value = "赔付", pluto = true)
    @Frigate(name ="赔付")
    @DisLock("#request.payTradeId > 0 ? 'trade:'+#request.payTradeId : 'trade:'+#request.businessType+':'+#request.productId+':transfer'")
    @RiskBarrier
    public RemittanceResponse remittance(RemittanceRequest request) {
        ThriftServiceWithCodeAction<RemittanceResult> action = new ThriftServiceWithCodeAction<RemittanceResult>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, request.getBusinessType(), request.getProductId());
            }

            @Override
            public DidaCode checkParam() throws DException {
                return remitService.verifyParameter(request);
            }

            @Override
            public Result<RemittanceResult> doAction() {
                LockUtil<Result<RemittanceResult>> lockUtil = new LockUtil<Result<RemittanceResult>>(String.format(LOCK_KEY_FORMAT, request.getBusinessType(), request.getProductId(), TradeTypeEnum.transfer.name()), () -> {
                    Routing routing = new Routing()
                            .setLookupId(routingThriftService.getLookupKeyById(request.getPayTradeId() > 0 ? String.valueOf(request.getPayTradeId()) : request.getProductId()));
                    DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                    return remitService.doTrade(request);
                });
                return lockUtil.trade();
            }
        };
        Result<RemittanceResult> result = this.execute(action);
        try {
            TradeMetric.common(request.getBusinessType(), "remittance", result.getCode());
        } catch (Exception ex) {
            log.error("监控err", ex);
        }

        return new RemittanceResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    @LogOpen(value = "顺风车免单", pluto = true)
    @Frigate(name ="顺风车免单")
    @DisLock("'trade:'+#request.payTradeId")
    @BackupCheck(value = "freeOrder")
    @RiskBarrier
    public CarpoolFreeResponse carpoolFree(CarpoolFreeRequest request) {
        ThriftServiceWithCodeAction<CarpoolFreeResult> action = new ThriftServiceWithCodeAction<CarpoolFreeResult>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, request.getBusinessType(), request.getProductId());
            }

            @Override
            public DidaCode checkParam() throws DException {
                return carpoolFreeService.verifyParameter(request);
            }


            @Override
            public Result<CarpoolFreeResult> doAction() {
                LockUtil<Result<CarpoolFreeResult>> lockUtil = new LockUtil<Result<CarpoolFreeResult>>(request.getPayTradeId(), () -> {
                    Routing routing = new Routing()
                            .setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getPayTradeId())));
                    DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                    pauPaymentApplicationService.sendPaymentTheaAndLiq(request.getPayTradeId(), request.getPassengerId());
                    return carpoolFreeService.doTrade(request);
                });
                return lockUtil.trade();
            }
        };
        Result<CarpoolFreeResult> result = this.execute(action);
        try {
            TradeMetric.common(request.getBusinessType(), "carpoolFree", result.getCode());
        }catch (Exception ex){
            log.error("监控err",ex);
        }
        return new CarpoolFreeResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    @LogOpen(value = "垫付", pluto = true)
    @Frigate(name ="垫付")
    @DisLock("'trade:'+#request.taxiRideId")
    @RiskBarrier
    public PayAdvanceReponse advanced(PayAdvanceRequest request) throws TException {
        ThriftServiceWithCodeAction<PayAdvanceReponse> action = new ThriftServiceWithCodeAction<PayAdvanceReponse>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, request.getTaxiRideId(), request.getPassengerId() + "_" + request.getDriverId());
            }

            @Override
            public DidaCode checkParam() throws DException {
                return payAdvancePreSettleService.verifyParameter(request);
            }

            @Override
            public Result<PayAdvanceReponse> doAction() {
                LockUtil<Result<PayAdvanceReponse>> lockUtil = new LockUtil<Result<PayAdvanceReponse>>(request.getTaxiRideId(), () -> {
                    Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getTaxiRideId())));
                    DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                    return payAdvancePreSettleService.doTrade(request);
                });
                return lockUtil.trade();

            }
        };
        Result<PayAdvanceReponse> result = this.execute(action);
        return new PayAdvanceReponse().setCode(result.getCode()).setMessage(result.getMessage());
    }

    @Override
//    @LogOpen("查询收银台")
    public QueryCashierResponse queryCashier(QueryCashierRequest request) {
        ThriftServiceWithLogkeyAction<QueryCashierResult> action = new ThriftServiceWithLogkeyAction<QueryCashierResult>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, request.getUserPaymentParam().getBusinessType(), request.getUserPaymentParam().getMajorProductId());
            }

            @Override
            public DidaCode checkParam() throws DException {
                return cashierService.verifyParameter(request);
            }

            @Override
            public QueryCashierResult doAction() {
                return cashierService.doTrade(request);
            }
        };
        Result<QueryCashierResult> result = this.execute(action);
        try {
            if (result.getCode() == 0) {
                TradeMetric.cashier(request.getUserPaymentParam().getBusinessType(), request.getUserPaymentParam().getSource());
            }
        }catch (Exception ex){
            //ignore ex
        }
        return new QueryCashierResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    public QueryCashierResponse queryRealCashier(RealCashierRequest request) throws TException {
        QueryCashierResponse response;
        try {
            response = cashierService.queryRealCashier(request);
        }catch (Exception ex){
            response = new QueryCashierResponse();
            response.setCode(SYSTEM_ERROR.getCode());
            response.setMessage(SYSTEM_ERROR.getMsg());
        }
        try {
            TradeMetric.common("queryRealCashier", "queryRealCashier", response.getCode());
        } catch (Exception ex) {
            log.error("监控err", ex);
        }
        return response;

    }

    @Override
    @LogOpen(value = "支付检查", pluto = true)
    @RiskBarrier
    public CheckPaymentResponse checkPayment(CheckPaymentRequest request) {
        ThriftServiceWithLogkeyAction<CheckPaymentResult> action = new ThriftServiceWithLogkeyAction<CheckPaymentResult>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, request.getUserPaymentParam().getBusinessType(), request.getUserPaymentParam().getMajorProductId());
            }

            @Override
            public DidaCode checkParam() throws DException {
                return checkPaymentService.verifyParameter(request);
            }

            @Override
            public CheckPaymentResult doAction() {
                return checkPaymentService.doTrade(request);
            }
        };
        Result<CheckPaymentResult> result = this.execute(action);
        return new CheckPaymentResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }


    @Override
    @SneakyThrows
    @LogOpen(value = "出租车扫码支付", pluto = true)
    @Frigate(name ="出租车扫码支付")
    @DisLock("'trade:t_scan:'+#request.openId")
    @RiskBarrier
    public ScanPayReponse scanPay(ScanPayRequest request) {
        ThriftServiceWithLogkeyAction<ScanPayReponse> action = new ThriftServiceWithLogkeyAction<ScanPayReponse>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, request.getDriverCid(), request.getOpenId());
            }

            @Override
            public DidaCode checkParam() throws DException {
                return scanPayService.verifyParameter(request);
            }

            @SneakyThrows
            @Override
            public ScanPayReponse doAction() {
                LockUtil<ScanPayReponse> lockUtil = new LockUtil<ScanPayReponse>(TBusinessEnum.t_scan.name() + request.getOpenId(), () -> {
                    Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyByUserCid(request.getDriverCid()));
                    DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                    return scanPayService.doTrade(request);
                });
                return lockUtil.trade();
            }
        };
        Result<ScanPayReponse> result = this.execute(action);
        if (result.getCode() != SuccessCode.SUCCESS.getCode()) {
            TradeMetric.paymentError(TBusinessEnum.t_scan.name(), TOrderSourceEnum.taxi.name());
            return new ScanPayReponse().setCode(result.getCode()).setMessage(result.getMessage());
        }
        TradeMetric.payment(TBusinessEnum.t_scan.name(), TOrderSourceEnum.taxi.name());
        return new ScanPayReponse().setCode(result.getCode()).setMessage(result.getMessage()).setUnifiedOrderResult(result.getData().getUnifiedOrderResult());
    }

    @Override
    @LogOpen(value = "司机充值", pluto = true)
    @Frigate(name ="司机充值")
    @DisLock("'trade:taxi_arrears_recharge:'+#request.driverCid")
    @RiskBarrier
    public RechargeReponse recharge(RechargeRequest request) {
        ThriftServiceWithLogkeyAction<RechargeReponse> action = new ThriftServiceWithLogkeyAction<RechargeReponse>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, request.getDriverCid(), request.getPaymentChannelId());
            }

            @Override
            public DidaCode checkParam() throws DException {
                return rechargePayService.verifyParameter(request);
            }

            @SneakyThrows
            @Override
            public RechargeReponse doAction() {
                LockUtil<RechargeReponse> lockUtil = new LockUtil<RechargeReponse>(TBusinessEnum.taxi_arrears_recharge.name() + request.getDriverCid(), () -> {
                    Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyByUserCid(request.getDriverCid()));
                    DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                    return rechargePayService.doTrade(request);
                });
                return lockUtil.trade();
            }
        };
        Result<RechargeReponse> result = this.execute(action);
        if (result.getCode() != SuccessCode.SUCCESS.getCode()) {
            return new RechargeReponse().setCode(result.getCode()).setMessage(result.getMessage());
        }
        return new RechargeReponse().setCode(result.getCode()).setMessage(result.getMessage()).setUnifiedOrderResult(result.getData().getUnifiedOrderResult());
    }

    @Override
    @LogOpen(value = "支付回调", pluto = true)
    @DisLock("'trade:'+#request.payTradeNo")
    public PaymentSuccessCallbackResponse paymentSuccessCallback(PaymentSuccessCallbackRequest request) {
        ThriftServiceWithLogkeyAction<PaymentSuccessCallbackResult> action = new ThriftServiceWithLogkeyAction<PaymentSuccessCallbackResult>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, TradeConstants.CALL_BACK, request.getPayTradeNo());
            }
            @Override
            public DidaCode checkParam() throws DException {
                return callBackTradeService.verifyParameter(request);
            }
            @Override
            public PaymentSuccessCallbackResult doAction() {
                LockUtil<PaymentSuccessCallbackResult> lockUtil = new LockUtil<>(request.getPayTradeNo(), () -> callBackTradeService.doTrade(request));
                return lockUtil.trade();
            }
        };
        Result<PaymentSuccessCallbackResult> result = this.execute(action);
        TradeMetric.common("payment", "callback", result.getCode());
        return new PaymentSuccessCallbackResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    @LogOpen(value = "先乘后付支付", pluto = true)
    @DisLock("'trade:'+#request.businessType+':'+#request.majorProductId+':payment'")
    @Frigate(name ="先乘后付支付")
    @RiskBarrier
    public TPauPaymentResponse doPauPayment(TPauPaymentRequest request) {
        ThriftServiceWithLogkeyAction<TPauPaymentResult> action = new ThriftServiceWithLogkeyAction<TPauPaymentResult>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, request.getBusinessType(), request.getMajorProductId());
            }

            @Override
            public DidaCode checkParam() throws DException {
                return pauPaymentService.verifyParameter(request);
            }

            @Override
            public TPauPaymentResult doAction() {
                LockUtil<TPauPaymentResult> lockUtil = new LockUtil<>(String.format(LOCK_KEY_FORMAT, request.getBusinessType(),
                        request.getMajorProductId(), TradeTypeEnum.payment.name()), () -> pauPaymentService.doTrade(request));
                return lockUtil.trade();
            }
        };
        Result<TPauPaymentResult> result = this.execute(action);
        TradeMetric.common("pau_pay_" + StringUtils.defaultIfBlank(request.getBusinessType(), "null"), StringUtils.defaultIfBlank(request.getSource(), "null"), result.getCode());
        if (result.getCode() != SuccessCode.SUCCESS.getCode()) {
            TradeMetric.paymentError(request.getBusinessType(), request.getSource());
        } else {
            TradeMetric.payment(request.getBusinessType(), request.getSource());
        }
        TradeMetric.common("pau", TradeTypeEnum.payment.name(), result.getCode());
        return new TPauPaymentResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    @LogOpen(value = "先乘后付补充支付", pluto = true)
    @RiskBarrier
    public TPauAddPaymentResponse doPauAddPayment(TPauAddPaymentRequest request) {
        if (TradePlatformGrayUtil.gray("carpool.pau.add", request.getUserId(), request.getMajorProductId(), false)) {
            return tradePlatformSupportService.doPauAddPaymentNew(request);
        } else {
            //发送交易前置补充支付预算
//            tradePlatformSupportService.sendPaymentPauAdd(request);
            return tradePlatformSupportService.doPauAddPaymentOld(request);
        }
//        ThriftServiceWithLogkeyAction<TPauAddPauPaymentResult> action = new ThriftServiceWithLogkeyAction<TPauAddPauPaymentResult>() {
//            @Override
//            public String addLogKey() {
//                return String.format(LOG_KEY_FORMAT, TradeConstants.PAU_ADD, request.getTradeOrderId());
//            }
//            @Override
//            public DidaCode checkParam() throws DException {
//                return pauAddPaymentService.verifyParameter(request);
//            }
//            @Override
//            public TPauAddPauPaymentResult doAction() {
//                Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getTradeOrderId())));
//                DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
//                LockUtil<TPauAddPauPaymentResult> lockUtil = new LockUtil<>(request.getTradeOrderId(), () -> pauAddPaymentService.doTrade(request));
//                return lockUtil.trade();
//            }
//        };
//        Result<TPauAddPauPaymentResult> result = this.execute(action);
//        TradeMetric.common("pau_pay_add", "pau_pay_add", result.getCode());
//        TradeMetric.common("pau", "add", result.getCode());
//        return new TPauAddPaymentResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    @LogOpen(value = "前置先乘后付补充支付", pluto = true)
    @DisLock("'trade:'+#request.tradeOrderId")
    public TPauAddPaymentResponse doPlatformPauAddPayment(TPlatformPauAddPaymentRequest request) {
        try {
            Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getTradeOrderId())));
            DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
            MDC.put("tradeId", String.format(LOG_KEY_FORMAT, TradeConstants.PAU_ADD, request.getTradeOrderId()));
            return pauAddPaymentService.platformPauAdd(request);
        } catch (DException e) {
            return new TPauAddPaymentResponse().setCode(e.getErrorCode().getCode()).setMessage(e.getErrorCode().getMsg());
        } catch (Exception e) {
            log.error("platformPauAdd error.", e);
            return new TPauAddPaymentResponse().setCode(TradeErrorCode.SYSTEM_104_ERROR.getCode()).setMessage(TradeErrorCode.SYSTEM_104_ERROR.getMsg());
        } finally {
            MDC.remove("tradeId");
        }
    }

    @Override
    @LogOpen(value = "先乘后付是否继续扣款判定", pluto = true)
    @DisLock("'trade:'+#request.tradeOrderId")
    public TPauPaymentCutJudgeContinueResponse pauPaymentCutJudgeContinue(TPauPaymentCutJudgeContinueRequest request) {
        ThriftServiceWithLogkeyAction<TPauPaymentCutJudgeContinueResult> action = new ThriftServiceWithLogkeyAction<TPauPaymentCutJudgeContinueResult>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, TradeConstants.PAU_JUDGE_CONTINUE, request.getTradeOrderId());
            }
            @Override
            public DidaCode checkParam() throws DException {
                return pauPaymentCutJudgeContinueService.verifyParameter(request);
            }
            @Override
            public TPauPaymentCutJudgeContinueResult doAction() {
                Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getTradeOrderId())));
                DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                LockUtil<TPauPaymentCutJudgeContinueResult> lockUtil = new LockUtil<>(request.getTradeOrderId(), () -> pauPaymentCutJudgeContinueService.doTrade(request));
                return lockUtil.trade();
            }
        };
        Result<TPauPaymentCutJudgeContinueResult> result = this.execute(action);
        TradeMetric.common("pau", "cut_judge", result.getCode());
        return new TPauPaymentCutJudgeContinueResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    @LogOpen(value = "先乘后付扣款", pluto = true)
    @DisLock("'trade:'+#request.tradeOrderId")
    @RiskBarrier
    public TPauPaymentCutResponse pauPaymentCut(TPauPaymentCutRequest request) {
        ThriftServiceWithLogkeyAction<TPauPaymentCutResult> action = new ThriftServiceWithLogkeyAction<TPauPaymentCutResult>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, TradeConstants.PAU_CUT, request.getTradeOrderId());
            }
            @Override
            public DidaCode checkParam() throws DException {
                return pauPaymentCutService.verifyParameter(request);
            }
            @Override
            public TPauPaymentCutResult doAction() {
                Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getTradeOrderId())));
                DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                LockUtil<TPauPaymentCutResult> lockUtil = new LockUtil<>(request.getTradeOrderId(), () -> pauPaymentCutService.doTrade(request));
                return lockUtil.trade();
            }
        };
        Result<TPauPaymentCutResult> result = this.execute(action);
        pauCutMetric(request, result.getCode());
        return new TPauPaymentCutResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    private void pauCutMetric(TPauPaymentCutRequest request, int code) {
        TradeMetric.common(getPauBusinessType(request), "pau_payment_cut", code);
        if (request.getCarpoolMultiRefundRequest() != null) {
            TradeMetric.common(request.getCarpoolMultiRefundRequest().getBusinessType(), TradeTypeEnum.refund.name(), code);
        }
        if (request.getRequest() != null) {
            TradeMetric.common(request.getRequest().getBusinessType(), TradeTypeEnum.refund.name(), code);
        }
        TradeMetric.common("pau", "cut", code);
    }

    /**
     * 无业务作用，仅用于埋点区分
     * @param request
     * @return
     */
    private String getPauBusinessType(TPauPaymentCutRequest request) {
        if (request.getCarpoolMultiRefundRequest() != null) {
            return "pau_carpool_multi";
        }
        if (request.getRequest() != null) {
            return "pau_carpool_price_diff";
        }
        return "pau_normal";
    }

    @Override
    @LogOpen(value = "先乘后付终止扣款", pluto = true)
    @DisLock("'trade:'+#request.tradeOrderId")
    @RiskBarrier
    public TFinishPauCutResponse finishPauCut(TFinishPauCutRequest request) {
        ThriftServiceWithLogkeyAction<TFinishPauCutResult> action = new ThriftServiceWithLogkeyAction<TFinishPauCutResult>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, TradeConstants.PAU_FINISH, request.getTradeOrderId());
            }
            @Override
            public DidaCode checkParam() throws DException {
                return pauPaymentCutFinishService.verifyParameter(request);
            }
            @Override
            public TFinishPauCutResult doAction() {
                Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getTradeOrderId())));
                DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                pauPaymentApplicationService.sendPaymentTheaAndLiq(request.getTradeOrderId(), request.getUserId());
                LockUtil<TFinishPauCutResult> lockUtil = new LockUtil<>(request.getTradeOrderId(), () -> pauPaymentCutFinishService.doTrade(request));
                return lockUtil.trade();
            }
        };
        Result<TFinishPauCutResult> result = this.execute(action);
        TradeMetric.common("pau", "finish", result.getCode());
        return new TFinishPauCutResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    @LogOpen(value = "confiscateBacktrack", pluto = true)
    @DisLock("'trade:'+#request.tradeOrderId")
    @RiskBarrier
    public TConfiscateBacktrackResponse confiscateBacktrack(TConfiscateBacktrackRequest request) {
        ThriftServiceWithLogkeyAction<TConfiscateBacktrackResult> action = new ThriftServiceWithLogkeyAction<TConfiscateBacktrackResult>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, request.getBusinessType(), request.getTradeOrderId());
            }
            @Override
            public DidaCode checkParam() throws DException {
                return confiscateBacktrackService.verifyParameter(request);
            }
            @Override
            public TConfiscateBacktrackResult doAction() {
                Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getTradeOrderId())));
                DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                return confiscateBacktrackService.doTrade(request);
            }
        };
        Result<TConfiscateBacktrackResult> result = this.execute(action);
        TradeMetric.common(request.getBusinessType(),TradeTypeEnum.system.name(), result.getCode());
        return new TConfiscateBacktrackResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    public TCancelFreepayResponse cancelFreepay(TCancelFreepayReq request) {
        return null;
    }

    @Override
    @LogOpen("updateBillingIncome")
    public UpdateBillingIncomeResponse updateBillingIncome(UpdateBillingIncomeRequest request) {
        return redisActionService.updateBillingIncome(request);
    }

    @Override
    @LogOpen("setTaxiTransferRisk")
    public UpdateTaxiTransferRiskResponse setTaxiTransferRisk(UpdateTaxiTransferRiskRequest request) {
        return redisActionService.setTaxiTransferRisk(request);
    }

    @Override
    @LogOpen("setTaxiUpdateCoord")
    public UpdateTaxiUpdateCoordResponse setTaxiUpdateCoord(UpdateTaxiUpdateCoordRequest request) {
        return redisActionService.setTaxiUpdateCoord(request);
    }

    @Override
    @LogOpen("queryActualOrderCut")
    public QueryActualOrderCutResponse queryActualOrderCut(QueryActualOrderCutRequest request) {
        Routing routing = new Routing()
                .setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getOrderId())));
        DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.SLAVE, DatabaseEnum.order, routing);
        return tradeQueryService.queryActualOrderCut(request);
    }

    @Override
    @LogOpen("saveTradeInfoToRedis")
    public SaveTradeInfoToRedisResponse saveTradeInfoToRedis(SaveTradeInfoToRedisRequest request) {
        return redisActionService.saveTradeInfoToRedis(request);
    }

    @Override
    @LogOpen(value = "setSysOrderGroup", pluto = true)
    @DisLock("'trade:'+#request.tradeOrderId")
    public TSetSysOrderGroupResponse setSysOrderGroup(TSetSysOrderGroupRequest request){
        ThriftServiceWithLogkeyAction<TSetSysOrderGroupResult> action = new ThriftServiceWithLogkeyAction<TSetSysOrderGroupResult>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, request.getBusinessType(), request.getTradeOrderId());
            }
            @Override
            public DidaCode checkParam() throws DException {
                return setSysOrderGroupService.verifyParameter(request);
            }
            @Override
            public TSetSysOrderGroupResult doAction() {
                Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getTradeOrderId())));
                DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                return setSysOrderGroupService.doTrade(request);
            }
        };
        Result<TSetSysOrderGroupResult> result = this.execute(action);
        TradeMetric.common(request.getBusinessType(),TradeTypeEnum.system.name(), result.getCode());
        return new TSetSysOrderGroupResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    @LogOpen(value = "updateHoldOrderStatus", pluto = true)
    @DisLock("'trade:'+#request.tradeOrderId")
    public TUpdateHoldOrderStatusResponse updateHoldOrderStatus(TUpdateHoldOrderStatusRequest request){
        ThriftServiceWithLogkeyAction<TUpdateHoldOrderStatusResult> action = new ThriftServiceWithLogkeyAction<TUpdateHoldOrderStatusResult>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, request.getBusinessType(), request.getTradeOrderId());
            }
            @Override
            public DidaCode checkParam() throws DException {
                return updateHoldOrderStatusService.verifyParameter(request);
            }
            @Override
            public TUpdateHoldOrderStatusResult doAction() {
                Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getTradeOrderId())));
                DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                return updateHoldOrderStatusService.doTrade(request);
            }
        };
        Result<TUpdateHoldOrderStatusResult> result = this.execute(action);
        TradeMetric.common(request.getBusinessType(),TradeTypeEnum.system.name(), result.getCode());
        return new TUpdateHoldOrderStatusResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    @LogOpen(value = "orderSetTagAndConfiscate", pluto = true)
    @DisLock("'trade:'+#request.tradeOrderId")
    public TOrderSetTagAndConfiscateResponse orderSetTagAndConfiscate(TOrderSetTagAndConfiscateRequest request){
        ThriftServiceWithLogkeyAction<TOrderSetTagAndConfiscateResult> action = new ThriftServiceWithLogkeyAction<TOrderSetTagAndConfiscateResult>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, request.getBusinessType(), request.getTradeOrderId());
            }
            @Override
            public DidaCode checkParam() throws DException {
                return orderSetTagAndConfiscateService.verifyParameter(request);
            }
            @Override
            public TOrderSetTagAndConfiscateResult doAction() {
                Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getTradeOrderId())));
                DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                return orderSetTagAndConfiscateService.doTrade(request);
            }
        };
        Result<TOrderSetTagAndConfiscateResult> result = this.execute(action);
        TradeMetric.common(request.getBusinessType(),TradeTypeEnum.confiscate.name(), result.getCode());
        return new TOrderSetTagAndConfiscateResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    @LogOpen(value = "sendAliceMqToLiquidation", pluto = true)
    public TSendAliceMqToLiquidationResponse sendAliceMqToLiquidation(TSendAliceMqToLiquidationRequest request){
        ThriftServiceWithLogkeyAction<TSendAliceMqToLiquidationResult> action = new ThriftServiceWithLogkeyAction<TSendAliceMqToLiquidationResult>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, TradeConstants.SEND_ALICE_MQ_TO_LIQUIDATION, request.getTradeOrderId());
            }
            @Override
            public DidaCode checkParam() throws DException {
                return sendAliceMqToLiquidationService.verifyParameter(request);
            }
            @Override
            public TSendAliceMqToLiquidationResult doAction() {
                Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getTradeOrderId())));
                DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                return sendAliceMqToLiquidationService.doTrade(request);
            }
        };
        Result<TSendAliceMqToLiquidationResult> result = this.execute(action);
        TradeMetric.common("alice_send_liq",TradeTypeEnum.system.name(), result.getCode());
        return new TSendAliceMqToLiquidationResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    public TransferToUserResponse kefuTransferToUser(TransferToUserRequest request) throws TException {
        return null;
    }

    @Override
    @LogOpen(value = "智慧码到账", pluto = true)
    @Frigate(name ="智慧码到账")
    @DisLock("'trade:'+#request.tradeOrderId")
    public TTaxiQrTransferResponse doTaxiQrTransfer(TTaxiQrTransferRequest request) {
        ThriftServiceWithLogkeyAction<TTaxiQrTransferResult> action = new ThriftServiceWithLogkeyAction<TTaxiQrTransferResult>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, request.getBusinessType() + TradeOrderTypeEnum.transfer.name(), request.getTradeOrderId());
            }
            @Override
            public DidaCode checkParam() throws DException {
                return taxiQrTransferService.verifyParameter(request);
            }

            @Override
            public TTaxiQrTransferResult doAction() {
                Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getTradeOrderId())));
                DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                return taxiQrTransferService.doTrade(request);
            }
        };
        Result<TTaxiQrTransferResult> result = this.execute(action);
        TradeMetric.common(request.getBusinessType() + request.getThirdChannel(), TradeOrderTypeEnum.transfer.name(), result.getCode());
        return new TTaxiQrTransferResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    @LogOpen(value = "智慧码到账确认", pluto = true)
    @Frigate(name ="智慧码到账确认")
    @DisLock("'trade:'+#request.tradeOrderId")
    public TTaxiQrTransferConfirmResponse doTaxiQrTransferConfirm(TTaxiQrTransferConfirmRequest request) {
        ThriftServiceWithLogkeyAction<TTaxiQrTransferConfirmResult> action = new ThriftServiceWithLogkeyAction<TTaxiQrTransferConfirmResult>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, request.getBusinessType() + TradeOrderTypeEnum.transfer.name(), request.getTradeOrderId());
            }
            @Override
            public DidaCode checkParam() throws DException {
                return taxiQrTransferConfirmService.verifyParameter(request);
            }

            @Override
            public TTaxiQrTransferConfirmResult doAction() {
                Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getTradeOrderId())));
                DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                return taxiQrTransferConfirmService.doTrade(request);
            }
        };
        Result<TTaxiQrTransferConfirmResult> result = this.execute(action);
        TradeMetric.common(request.getBusinessType() + request.getThirdChannel(), TradeOrderTypeEnum.transfer.name(), result.getCode());
        return new TTaxiQrTransferConfirmResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    @LogOpen("创建分表")
    @DisLock("'trade:'+#request.tableName")
    public TCreateSubTableResponse createSubtable(TCreateSubTableRequest request) throws TException {
        /**
         * create_subtable.trade_bill.struct: 表结构
         * create_subtable.trade_bill.strategy:
         * create_subtable.trade_bill.jsonVal:
         */

        String tableName = request.getTable();
        tableManagerService.createTable(tableName);
        return null;
    }

    @Override
    @LogOpen("清洗数据")
    public TradeQueryCommonResponse cleanOrderData(TCleanOrderDataRequest request) {
        try {
            return cleanOrderDataService.cleanOrderData(request);
        } catch (Exception e) {
            log.error("清洗数据 error. request:{}", JsonMapper.toJson(request), e);
            return new TradeQueryCommonResponse().setCode(TradeErrorCode.SYSTEM_ERROR.getCode()).setMessage(TradeErrorCode.SYSTEM_ERROR.getMsg());
        }
    }

    @Override
    @LogOpen(value = "智慧码订单状态更新", pluto = true)
    @Frigate(name ="智慧码订单状态更新")
    @DisLock("'trade:'+#request.orderId")
    public TTaxiqrTradeOrderUpdateResponse updateTaxiqrTradeOrderStatus(TTaxiqrTradeOrderUpdateRequest request) throws TException {
        try {
            Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getOrderId())));
            DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
            return transferService.updateTaxiqrTradeOrderStatus(request);
        } catch (Exception e) {
            log.error("清洗数据 error. request:{}", JsonMapper.toJson(request), e);
            return new TTaxiqrTradeOrderUpdateResponse().setCode(TradeErrorCode.SYSTEM_ERROR.getCode()).setMessage(TradeErrorCode.SYSTEM_ERROR.getMsg());
        }
    }

    @Override
    public RefundSuccessCallbackResponse refundSuccessCallback(RefundSuccessCallbackRequest request) throws TException {
        return null;
    }

    @Override
    @LogOpen(value = "账户金额更新变更状态", pluto = true)
    @DisLock("'trade:'+#request.paymentOrderId")
    public TUpdateTradeAmountDetailReceiptStatusResponse updateTradeAmountDetailReceiptStatus(TUpdateTradeAmountDetailReceiptStatusRequest request) {
        ThriftServiceWithLogkeyAction<TUpdateTradeAmountDetailReceiptStatusResult> action = new ThriftServiceWithLogkeyAction<TUpdateTradeAmountDetailReceiptStatusResult>() {
            @Override
            public String addLogKey() {
                return String.format(LOG_KEY_FORMAT, TradeConstants.UPDATE_AMOUNT_STATUS, request.getPaymentOrderId());
            }
            @Override
            public DidaCode checkParam() throws DException {
                return updateTradeAmountDetailReceiptStatusService.verifyParameter(request);
            }

            @Override
            public TUpdateTradeAmountDetailReceiptStatusResult doAction() {
                Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getPaymentOrderId())));
                DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
                return updateTradeAmountDetailReceiptStatusService.doTrade(request);
            }
        };
        Result<TUpdateTradeAmountDetailReceiptStatusResult> result = this.execute(action);
        return new TUpdateTradeAmountDetailReceiptStatusResponse().setCode(result.getCode()).setMessage(result.getMessage()).setData(result.getData());
    }

    @Override
    @LogOpen(value = "间联预生成交易订单统一下单支付", pluto = true)
    @DisLock("'trade:'+#request.orderId")
    public PaymentResponse doPreOrderPayment(PreOrderPaymentRequest request) {
        try {
            Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getOrderId())));
            DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
            return paymentService.doPreOrderPayment(request);
        } catch (Exception e) {
            log.error("间联预生成交易订单统一下单支付 error. request:{}", JSONUtil.toJsonStr(request), e);
            return new PaymentResponse().setCode(TradeErrorCode.SYSTEM_ERROR.getCode()).setMessage(TradeErrorCode.SYSTEM_ERROR.getMsg());
        }
    }

    @Override
    @LogOpen(value = "腾讯顺风车账单同步", pluto = true)
    public TencentBillResponse tencentBillNotify(TencentBillRequest request) {
        try {
            Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getPayTradeNo())));
            DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.SLAVE, DatabaseEnum.order, routing);
            return tencentBillNotifyService.doTrade(request);
        }catch (Exception e){
            log.error("腾讯顺风车账单同步 error. request:{}", JSONUtil.toJsonStr(request), e);
            return new TencentBillResponse().setCode(TradeErrorCode.SYSTEM_ERROR.getCode()).setMessage(TradeErrorCode.SYSTEM_ERROR.getMsg());
        }
    }

    @Override
    @LogOpen(value = "腾讯先乘后付订单通知", pluto = true)
    @DisLock("'trade:'+#request.payTradeNo")
    public TencentPauOrderNotifyResponse tencentPauOrderNotify(TencentPauOrderNotifyRequest request) {
        try {
            Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getPayTradeNo())));
            DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);
            return tencentBillNotifyService.tencentPauOrderNotify(request);
        } catch (DException e) {
            return new TencentPauOrderNotifyResponse().setCode(e.getErrorCode().getCode()).setMessage(e.getErrorCode().getMsg());
        } catch (Exception e) {
            log.error("tencentPauOrderNotify error.", e);
            return new TencentPauOrderNotifyResponse().setCode(TradeErrorCode.SYSTEM_104_ERROR.getCode()).setMessage(TradeErrorCode.SYSTEM_104_ERROR.getMsg());
        }
    }

    @Override
    public Object after(MethodInvocation methodInvocation, CheckRiskClientResponse riskResponse) {
        log.info("IServerRiskCallback.after. methodName={} riskResponse={}", methodInvocation.getMethod().getName(), JsonMapper.toJson(riskResponse));
        if (methodInvocation.getMethod().getName().equals("doPayment")) {
            PaymentResponse paymentResponse = new PaymentResponse();
            paymentResponse.setCode(riskResponse.getCode());
            paymentResponse.setMessage(riskResponse.getMsg());
        }
        return null;
    }
}
