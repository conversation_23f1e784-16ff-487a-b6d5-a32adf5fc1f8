buildscript {
    ext {
        springBootVersion = '1.5.22.RELEASE'
    }
    repositories {
        mavenLocal()
        maven { url "http://mirror.didapinche.com/repository/maven-public/" }
        mavenCentral()
    }
    dependencies {
        classpath("io.qameta.allure:allure-gradle:2.5")
    }
}
plugins {
    id 'java'
}

//单测报告插件
apply plugin: 'jacoco'
apply plugin: 'io.qameta.allure'
//allure配置
allure {
    autoconfigure = true
    version = '2.6.0'
    useJUnit4 {
        version = '2.6.0'
    }
}

//jacoco配置
jacoco {
    toolVersion = "0.8.7"
    reportsDir = file("$buildDir/customJacocoReportDir")
}
jacocoTestReport {
    reports {
        html.enabled true
        csv.enabled true
        xml.enabled true
        html.destination file("$buildDir/jacocoHtml")
    }
}
check.dependsOn jacocoTestReport

group 'com.didapinche'

repositories {
//    maven { url "http://mirror.didapinche.com/repository/maven-public/" }
    mavenCentral()
}

dependencies {
    compile project(':application')
    compile('org.springframework.boot:spring-boot-starter-web')
    compile 'org.springframework.boot:spring-boot-starter-security'
    // eureka
    compile('org.springframework.cloud:spring-cloud-starter-eureka')

    testCompile('org.springframework.boot:spring-boot-test')
}
