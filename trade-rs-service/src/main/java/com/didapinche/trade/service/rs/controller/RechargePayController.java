package com.didapinche.trade.service.rs.controller;

import com.didapinche.agaue.common.exception.DException;
import com.didapinche.agaue.core.annotation.LogOpen;
import com.didapinche.server.commons.common.ReplyMap;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.server.commons.common.util.UserInfoUtil;
import com.didapinche.trade.application.BO.CommonResponseBO;
import com.didapinche.trade.application.service.support.platform.TradePlatformSupportService;
import com.didapinche.trade.infrastructure.exception.TradeErrorCode;
import com.didapinche.trade.infrastructure.util.TradePlatformGrayUtil;
import com.didapinche.trade.thrift.TradeQueryThriftService;
import com.didapinche.trade.thrift.TradeThriftService;
import com.didapinche.trade.thrift.entities.RechargePayResultReponse;
import com.didapinche.trade.thrift.entities.RechargeReponse;
import com.didapinche.trade.thrift.entities.RechargeRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 出租车司机充值
 */
@Slf4j
@RestController
public class RechargePayController {
    @Resource
    private TradeThriftService.Iface tradeThriftService;
    @Autowired
    private TradeQueryThriftService.Iface tradeQueryThriftService;
    @Resource
    private TradePlatformSupportService tradePlatformSupportService;

    @LogOpen(value = "出租车欠款充值", pluto = true)
    @RequestMapping(value = "/driver/charge/pay", method = {RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    public String taxiRecharge(@ModelAttribute("user_cid") String userCid,
                               @ModelAttribute("price") String price,
                               @ModelAttribute("payment_channel_id") String paymentChannelId) throws Exception {
        if (StringUtils.isAnyBlank(userCid, price, paymentChannelId)) {
            log.error("出租车欠款充值, 参数error. userCid:{},price:{},paymentChannelId:{}", userCid,price,paymentChannelId);
            CommonResponseBO responseBO = new CommonResponseBO();
            responseBO.setCode(102);
            responseBO.setMessage("参数错误");
            return JsonMapper.toJson(responseBO);
        }
        Integer userId = UserInfoUtil.getTaxiUseridByCid(userCid);
        if (userId == null || userId == 0) {
            log.error("出租车欠款充值 获取用户id error. userCid:{}", userCid);
            throw new DException(TradeErrorCode.USER_ID_ERROR);
        }
        if (TradePlatformGrayUtil.gray("taxi.recharge", Long.valueOf(userId), null, false)) {
            return tradePlatformSupportService.taxiArrearsRechargePayment(userCid, price, paymentChannelId);
        }


        // 校验用户信息
        RechargeRequest request = new RechargeRequest();
        request.setPrice(price);
        request.setPaymentChannelId(paymentChannelId);
        request.setDriverCid(userCid);
        RechargeReponse rechargeResponse = tradeThriftService.recharge(request);
        if (rechargeResponse.getCode() != 0) {
            CommonResponseBO responseBO = new CommonResponseBO();
            responseBO.setCode(rechargeResponse.getCode());
            responseBO.setMessage(rechargeResponse.getMessage());
            return JsonMapper.toJson(responseBO);
        }
        return rechargeResponse.getUnifiedOrderResult();
    }

    /**
     * 查询充值状态
     *
     * @param user_cid        用户cid
     * @param charge_trade_no 对应taxi_user_charge 出租车充值表 中的charge_trade_no（传给第三方的交易号）
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/driver/charge/chargeStatus", method = {RequestMethod.GET}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    @LogOpen("出租车欠款充值查询充值状态")
    public String queryChargeStatus(@RequestParam("user_cid") String user_cid,
                                    @RequestParam("charge_trade_no") String charge_trade_no) throws Exception {
        log.info("queryChargeStatus. user_cid={} charge_trade_no={}", user_cid, charge_trade_no);
        if (StringUtils.isBlank(user_cid) || StringUtils.isBlank(charge_trade_no)) {
            CommonResponseBO responseBO = new CommonResponseBO();
            responseBO.setCode(102);
            responseBO.setMessage("参数错误");
            return JsonMapper.toJson(responseBO);
        }
        Integer driverUid = UserInfoUtil.getTaxiUseridByCid(user_cid);
        RechargePayResultReponse reponse = tradeQueryThriftService.queryRechargePayResult(Long.parseLong(charge_trade_no), driverUid.longValue());
        if (reponse.getCode() != 0) {
            CommonResponseBO responseBO = new CommonResponseBO();
            responseBO.setCode(reponse.getCode());
            responseBO.setMessage(reponse.getMessage());
            return JsonMapper.toJson(responseBO);
        }
        ReplyMap replyMap = new ReplyMap();
        replyMap.success();
        replyMap.put("is_succ", "success".equals(reponse.getStatus()) ? 1 : "fail".equals(reponse.getStatus()) ? 0 : 2);
        return replyMap.toJson();
    }

    @RequestMapping(value = "/driver/charge/chargeInfo", method = {RequestMethod.GET}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    @LogOpen("出租车查询欠款和收银台")
    public String queryChargeInfo(@RequestParam("user_cid") String user_cid) throws Exception {
        return tradeQueryThriftService.queryChargeInfo(user_cid).getData();
    }

}
