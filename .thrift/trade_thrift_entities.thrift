namespace java com.didapinche.trade.thrift.entities

include "trade_thrift_enums.thrift"

    /**
     * 企业详情
     */
    struct TEnterpriseDetails{
        /**
         * 企业付公司id <br>
         * 顺风车：同程：1 车巴达：2
         * <br>
         * 出租车：各个公司id
         **/
        1:optional i32 companyId = 0;
        /**
         * 出租车企业付规则id <br>
         * 收银台接口无需上送
         **/
        2:optional i32 ruleId = 0;
        /**
         * 三方订单号 vip企业付需要上送 <br>
         * 收银台接口无需上送
         **/
        3:optional string partnerOrderId;
        /**
         * 服务费 单位元 保留小数点2位 <br>
         * 收银台接口无需上送
         **/
        4:optional string orderServiceFee;
        /**
         * 起点城市id vip企业付需要上送 <br>
         * 收银台接口无需上送
         **/
        5:optional i32 startCityId;
        /**
         * 出租车三方企业付Appid <br>
         **/
        6:optional string appId;
        /**
         * 等待支付成功通知 <br>
         * 默认=false 不等待通知,默认支付成功 <br>
         * =true 等待通知,未收到通知前,支付状态为支付中
         **/
        7:optional bool waitPaySuccessNotify = false;
         /**
         * 优惠金额 单位元 保留小数点2位 <br>
         * 目前只有腾讯顺风车有
         **/
        8:optional string discountAmount;

    }

    /**
     * 订单金额组成
     * <AUTHOR>
     * @date 2022/5/14 5:59 下午
     * @version v1.0
     * @since v1.0
     */
    struct TOrderAmountDetails{
       /**
        * 账户类型
        * @see com.didapinche.trade.thrift.enums.TOrderAccountTypeEnum
        **/
       1:required string type;
       /**
        * 金额,单位元 保留2位小数
        **/
       2:required string amount;

    }

     /**
      * 交易产品详情
      * <AUTHOR>
      * @date 2022/5/14 5:59 下午
      * @version v1.0
      * @since v1.0
      */
    struct TTradeProductDetails{
       /**
        * 产品id
        **/
       1:required string productId;
       /**
        * 商品类型
        * @see com.didapinche.trade.thrift.enums.TTradeProductTypeEnum
        **/
       2:required string type;
       /**
        * 产品价格,单位元两位小数
        **/
       3:required string price;
       /**
        * 操作action
        * @see com.didapinche.trade.thrift.enums.TTradeActionEnum
        **/
       4:optional string action;
       /**
        * 如果是商品，需要传值MKO开头的商品订单号
        **/
        5:optional string goodsOrderId;
        /**
        * 顺风车，顺风车运力外输，出租车，出租车三化，出租车企业付，出租车运力外输等行程订单支付的场景，需要传递行程起终点城市id THEA-766
        **/
        6:optional string startCityId;
        7:optional string endCityId;
    }

    /**
     * 优惠券详情
     *
     * <AUTHOR>
     * @date 2022/5/19 5:03 下午
     * @version v1.0
     * @since v1.0
     */
    struct TCouponDetails{
        /**
         * 优惠券id
         */
        1:required i64 couponId;
        /**
         * 优惠券类型
         *
         * @see com.didapinche.trade.thrift.enums.TCouponTypeEnum
         */
        2:required string couponType;
        /**
         * 优惠券setId
         */
        3:optional i32 couponSetId;
                /**
         * 新链路中券包对应的商品id
         */
        4:optional string orderCode;
        /**
         * :新/老系统商品标识 true:新 false:老
         */
        5: optional bool newGoodsFlag;

    }

    /**
     * 支付拓展字段
     *
     * <AUTHOR>
     * @date 2022/5/19 5:03 下午
     * @version v1.0
     * @since v1.0
     */
    struct TPaymentNote{
        /**
         * json格式 业务方上送的透传字段,订单系统不关心具体内容，回调直接返回给业务组 <br>
         * 如顺风车高速费等信息可放在这里
         */
        1:optional string extra;
        /**
         * 主副单标识 默认为true 副单不抵扣优惠券
         */
        2:optional bool majorFlag = true;
        /**
         * 候补路线id列表
         */
        3:optional list<string> candidateRideLineList;
        /**
         * 司机id/车主id <br>
         * 出租车智慧码广州微信支付方式必送
         */
        4:optional i64 driverId;
        /**
         * 下单推荐补单支付时上送，主支付订单id
         */
        5:optional i64 majorOrderId;
        /**
         * 计划出发时间 yyyy-MM-dd HH:mm:ss 完单后记录乘客支付总金额需要
         */
        6:optional string planStartTime;
        /**
        * 行程信息
        **/
        7:optional TRideInfo rideInfo;
        /**
        * 高速费补贴实验组名称，如果不传，就是默认组（兼容线上逻辑）
        **/
        8:optional string subsidyExpGroup;
    }

    /**
     * 西安银行支付请求参数
     * <AUTHOR>
     * @date 13:08 2022/6/7
     * @version v1.0.1
     **/
    struct XiAnBankPayRequest{
        /**
         * 司机在西安银行的唯一标识
         **/
        1:required string driverLicenseId;
        /**
         * 西银折扣金额,单位元
         **/
        2:required string discountAmount;
        /**
         * 西银用户openId
         **/
        3:required string openId;
    }
    /**
     * 微信支付请求参数
     * <AUTHOR>
     * @date 13:08 2022/6/7
     * @version v1.0.1
     **/
    struct WechatPayRequest{
        /**
         * 微信支付用户openId <br>
         * 小程序支付上送
         **/
        1:optional string openId;
    }

    /**
     * 微信支付广州服务商模式请求参数
     **/
    struct WechatGZPayRequest{
        /**
         * 微信支付用户openId <br>
         *
         **/
        1:required string openId;
        /**
         * 是否分账 默认需要分账
         **/
        2:optional bool sharing = true;
        /**
         * 司机微信openId
         **/
        3:required string driverOpenId;
        /**
         * 出租车公司id
         */
        4:required string companyId;
        /**
         * 司机姓名
         */
        5:required string driverName;
    }

    /**
     * 银盛请求参数
     **/
    struct YspayPayRequest{
        /**
         * 微信支付用户openId <br>
         *
         **/
        1:optional string openId;
        /**
         * 是否分账 默认需要分账
         **/
        2:optional bool sharing = true;
        /**
         * 司机微信openId
         **/
        3:optional string driverOpenId;
        /**
         * 出租车公司id
         */
        4:optional string companyId;
        /**
         * 司机姓名
         */
        5:optional string driverName;
    }

    /**
     * 支付宝支付请求参数
     * <AUTHOR>
     * @date 13:08 2022/6/7
     * @version v1.0.1
     **/
    struct AlipayRequest{
        /**
         * 支付宝营销活动标识
         */
        1:optional string promoCxBiz;
        /**
         * 禁用渠道，用户不可用指定渠道支付
         * 当有多个渠道时用“,”分隔
         * 注，与enable_pay_channels互斥
         */
        2:optional string disablePayChannels;
    }
    /**
     * 京东支付请求参数
     * <AUTHOR>
     * @date 13:08 2022/6/7
     * @version v1.0.1
     **/
    struct JdpayRequest{
        /**
         * 京东h5支付，商品名称
         **/
        1:optional string goodsName;
    }
    /**
     * 云闪付支付请求参数
     * <AUTHOR>
     * @date 13:08 2022/6/7
     * @version v1.0.1
     **/
    struct UnionpayRequest{
      /**
       * 银联营销活动标识
       */
      1:optional string discountCode;
    }
    /**
     * 分账出租车司机信息
     *
     * <AUTHOR>
     * @date 13:08 2022/6/7
     * @version v1.0.1
     **/
    struct ProfitSharingTaxiDriverDetails{
        /**
         * 司机姓名
         **/
        1:required string driverName;
        /**
         * 司机绑定微信的openId
         **/
        2:required string driverBindWxOpenId;
        /**
         * 司机id
         **/
        3:required i32 driverId;
    }

    /**
     * 三方支付请求参数
     * <AUTHOR>
     * @date 9:50 2022/6/6
     * @version v1.0.0
     **/
    struct ThirdPayRequest{
        /**
         * 支付渠道
         * @see com.didapinche.trade.thrift.enums.TChannelEnum
         **/
        1:required string channelType;
        /**
         * 微信下单参数
         **/
        2:optional WechatPayRequest wechatPayRequest;
        /**
         * 广州微信下单参数
         **/
        3:optional WechatGZPayRequest wechatGZPayRequest;
        /**
         * 西银下单参数
         **/
        4:optional XiAnBankPayRequest xiAnBankPayRequest;
        /**
         * 京东下单参数
         **/
        5:optional JdpayRequest jdpayRequest;
        /**
         * 云闪付下单参数
         **/
        6:optional UnionpayRequest unionpayRequest;
        /**
         * 支付宝下单参数
         **/
        7:optional AlipayRequest alipayRequest;
        /**
         * 支付完跳转地址
         **/
        8:optional string returnUrl;
        /**
         * 用户端ip 目前只有微信h5使用
         */
        9:optional string ip;
        /**
         * 银盛下单参数
         **/
        10:optional YspayPayRequest yspayPayRequest;
        /**
         * 先用后付业务
         * @see com.didapinche.trade.thrift.enums.TPauSceneEnum
         *
         */
        11:optional string pauScene;
        /**
        * 指定支付方式<br>
        * no_credit:限制用户不能使用信用卡支付
         **/
        12:optional list<string> limitPay;
        /**
         * 三方用户id
         * 微信支付用户openId <br>
         * 支付宝buyer_id
         **/
        13:optional string thirdUserId;
        /**
         * 取消跳转地址
         **/
        14:optional string cancelBackUrl;
    }
    /**
     * 转账请求参数
     * <AUTHOR>
     * @date 20:53 2022/6/29
     **/
    struct CommonTransferRequest{

        1:required string productId;

        2:required string businessType;
        /**
        *   金额 （余额和顺风金账户不能同时为空）
        **/
        3:optional string amount;
        /**
        * 账户类型 @see com.didapinche.trade.thrift.enums.TOrderAccountTypeEnum
        **/
        4:required string accountType;
        /**
        * 顺风金账户（余额和顺风金账户不能同时为空）
        **/
        5:optional string bonus;
        /**
        * 用户Id/公司id
        **/
        6:required i64 transferUserId;
        /**
        * 来源
        **/
        7:required string source;
        /**
        * 支付订单id
        **/
        8:optional i64 payTradeId;
        /**
        * 描述
        **/
        9:optional string desc;
        /**
        * 提现id，默认为0
        **/
        10:optional i64 withdrawId=0;
        /**
        * 操作人id 默认系统操作 151
        **/
        11:optional i32 sysOpId=151;
        /**
        * 账户变动相关说明参数
        **/
        12:optional string details;
        /**
        * 账户变动相关说明参数
        **/
        13:optional string remarks;
        /**
        * 账户变动相关说明参数
        **/
        14:optional string comment;
        /**
        * 补贴对应行程的支付用户id
        **/
        15:optional i64 payuserId;
    }
    /**
     * 余额转账请求参数(校验到账不能大于支付订单)
     * 顺风车车费，高速费，司机欠款充值
     **/
    struct TransferWithCheckPayRequest{

        1:required string productId;

        /**
        * 产品类型，车费到账：ride，奖励到账传奖励类型
        **/
        2:required string businessType;
        /**
        *   金额
        **/
        3:required string amount;
        /**
        * 账户类型 @see com.didapinche.trade.thrift.enums.TOrderAccountTypeEnum
        **/
        4:required string accountType;
        /**
        * 转账用户/公司id
        **/
        5:required i64 transferUserId;
        /**
        * 来源
        **/
        6:required string source;
        /**
        * 支付订单id
        **/
        7:required i64 payTradeId;
        /**
        * 描述
        **/
        8:optional string desc;
        /**
        * 操作人id 默认系统操作 151
        **/
        9:optional i32 sysOpId=151;
        /**
        * 到账设计订单集合，没有当前参数取payTradeId做校验
        **/
        10:optional list<i64> orderIdList;
         /**
         * 支付用户id
         **/
        11:required i64 payUserId;
        /**
         * 车主接单时服务费费应收金额（元）
         **/
        12:optional string serviceFee;
        /**
         * 车主接单时车费应收金额（元）
         **/
        13:optional string receivable;
        /**
         * 冻结金额
         **/
        14:optional string frozenAmount;
        /**
         * 是否存在免单
         **/
        15:optional bool existFree = false;
         /**
         * 到账标记 @see com.didapinche.trade.thrift.enums.TransferTagEnum
         **/
        16:optional string transferTag;
        /**
         * 最终服务费应收金额（元）不传取serviceFee
         * */
        17:optional string finalServiceFee;
        /**
         * 最终车费应收金额（元） 不传取receivable
         * */
        18:optional string finalReceivable;
        /**
        * 节假日服务费，包含与总服务费或者multiServiceFee中
        **/
        19:optional string holidayServiceFee;
        /**
        * 节假日服务费，车主收取到的部分
        **/
        20:optional string driverRecHoliServiceFee;
        /**
        * 冻结时间，>0代表需要冻结该笔到账
        **/
        21:optional i64 plantFreezeTime;
        /**
        * 冻结原因
        **/
        22:optional string freezeReason;

        23:optional bool autoCheatComplainFlag;//作弊申诉自动通过标志
        /**
         * 顺风金成本回收
         **/
        24: optional string bonusRecover;
        /**
         * 优惠券成本回收
         **/
        25: optional string couponRecover;
        /**
        * 作弊申诉时的乘客补贴成本回收
        **/
        26: optional string passengerSubsidyRecover;
        /**
        * 备注
        **/
        27: optional string comment;
        /**
         * 参与分佣的金额，比如行程
         * */
        28:optional string involvedAmount;
    }

    /**
     * 到账响应结果
     * <AUTHOR>
     * @date 20:53 2022/6/29
     **/
    struct TransferResponse{
        1: required i32 code;
        2: optional string message;
        3: optional TransferResult data;
    }
    /**
     * 到账响应结果数据
     * <AUTHOR>
     * @date 20:53 2022/6/29
     **/
    struct TransferResult{
        1: required i64 tradeOrderId;
        2: required string transferAmount;
    }

    /**
     * 没收请求参数
     * <AUTHOR>
     * @date 20:53 2022/6/29
     **/
    struct CommonConfiscateRequest{
        1:required string productId;

        2:required string businessType;
        /**
        *   金额 （余额和顺风金账户不能同时为空）
        **/
        3:optional string amount;
        /**
        * 账户类型 @see com.didapinche.trade.thrift.enums.TOrderAccountTypeEnum
        **/
        4:required string accountType;
        /**
        * 顺风金账户（余额和顺风金账户不能同时为空）
        **/
        5:optional string bonus;
        /**
        * 用户Id/公司id
        **/
        6:required i64 confiscateUserId;
        /**
        * 来源
        **/
        7:required string source;
        /**
        * 支付订单id
        **/
        8:optional i64 payTradeId;
        /**
        * 描述
        **/
        9:optional string desc;
        /**
        * 提现id，默认为0
        **/
        10:optional i64 withdrawId=0;
        /**
        * 操作人id 默认系统操作 151
        **/
        11:optional i32 sysOpId=151;
        /**
        * 账户变动相关说明参数
        **/
        12:optional string details;
        /**
        * 账户变动相关说明参数
        **/
        13:optional string remarks;
        /**
        * 账户变动相关说明参数
        **/
        14:optional string comment;
         /**
        * 补贴对应行程的支付用户id
        **/
        15:optional i64 payuserId;
    }
    /**
     * 没收响应结果
     * <AUTHOR>
     * @date 20:53 2022/6/29
     **/
    struct ConfiscateResponse{
        1: required i32 code;
        2: optional string message;
        3: optional ConfiscateResult data;
    }
    /**
     * 没收响应结果数据
     * <AUTHOR>
     * @date 20:53 2022/6/29
     **/
    struct ConfiscateResult{
        1: required i64 tradeOrderId;
    }

    /**
     * 支付请求参数
     * <AUTHOR>
     * @date 20:53 2022/6/29
     **/
    struct PaymentRequest {
        /**
        * 用户id
        **/
        1:required i64 userId;
        /**
        * 主产品id
        **/
        2:required string majorProductId;
        /**
        * 总金额 单位元 2位小数
        **/
        3:required string totalPrice;
        /**
        * 业务类型
        * @see com.didapinche.trade.thrift.enums.TBusinessEnum
        **/
        4:required string businessType;
        /**
        * 订单来源
        * @see com.didapinche.trade.thrift.enums.TOrderSourceEnum
        **/
        5:required string source;
        /**
        * 订单描述(1,30)
        **/
        6:required string desc;
        /**
        * 支付方式 区分普通支付 免密支付 快捷付 先享后付
        * @see com.didapinche.trade.thrift.enums.TPaymentWayEnum
        **/
        7:optional string paymentWay = 'normal';
        /**
        * 产品信息
        * 行程若包含随单券包,需上送随单券包信息
        * 行程若是多单推荐单，需要分开上送主行程与候补路线行程信息
        **/
        8:required list<TTradeProductDetails> tradeProductDetails;
        /**
        * 订单金额组成
        **/
        9:required list<TOrderAmountDetails> orderAmountDetails;
        /**
        * 三方支付信息 涉及三方支付上送
        **/
        10:optional ThirdPayRequest thirdPayDetails;
        /**
        * 优惠券信息 涉及优惠券信息上送
        **/
        11:optional TCouponDetails couponDetails;
        /**
        * 企业信息
        **/
        12:optional TEnterpriseDetails enterpriseDetails;
        /**
        * 拓展字段
        **/
        13:optional TPaymentNote paymentNote;
        /**
        * ddcInfo 解密后的json格式
        **/
        14:optional string ddcInfo;
        /**
        * ddcInfo原文 未解密的,部分接口需要上传
        **/
        15:optional string ddcInfoOriginal;
        /**
        * 原订单信息(双写阶段使用,rs层请求过来的信息原封不动封装为json结构体上送即可,完全上线后废弃)
        **/
        16:optional string originalOrdeInformation;
        /**
         * 新链路中券包对应的商品id
         */
        17:optional string orderCode;
        /**
         * :新/老系统商品标识 true:新 false:老
         */
        18: optional bool newGoodsFlag;
        /**
         * :守约订单支付标识 true:是 false:否
         */
        19:optional bool overduePayFlag;
        /**
        * 产品id，如行程ID,周卡ID等商品纬度的信息
        **/
        20:optional string productId;
        /**
         * 透传字段 <br>
         * 三方用户id key:thirdUserId
         *
         */
        21:optional map<string,string> penetrateParams;
         /**
        * 产品id，如行程ID,周卡ID等,用于把所有某个个产品下的支付给串起来
        **/
        22:optional string primaryProductId;
        /**
        * 业务模式
        * @see com.didapinche.trade.thrift.enums.TBusinessModelEnum
        **/
        23:optional string businessModel;

        /**
        * 计算ID
        **/
        24: optional i64 calcOrderId;
    }


    /**
     * 预生成交易订单统一下单支付请求参数
     **/
    struct PreOrderPaymentRequest {
        /**
        * 用户id
        **/
        1:required i64 orderId;
        /**
        * 主产品id
        **/
        2:optional string thirdpartyUserId;
    }

    /**
     * 先享后付支付请求参数
     **/
    struct TPauPaymentRequest{
        /**
        * 用户id
        **/
        1:optional i64 userId;
        /**
        * 主产品id
        **/
        2:optional string majorProductId;
        /**
        * 总金额 单位元 2位小数
        **/
        3:optional string totalPrice;
        /**
        * 业务类型
        * @see com.didapinche.trade.thrift.enums.TBusinessEnum
        **/
        4:optional string businessType;
        /**
        * 订单来源
        * @see com.didapinche.trade.thrift.enums.TOrderSourceEnum
        **/
        5:optional string source;
        /**
        * 订单描述(1,30)
        **/
        6:optional string desc;
        /**
        * 产品信息
        * 行程若包含随单券包,需上送随单券包信息
        * 行程若是多单推荐单，需要分开上送主行程与候补路线行程信息
        **/
        7:optional list<TTradeProductDetails> tradeProductDetails;
        /**
         * 支付渠道
         * @see com.didapinche.trade.thrift.enums.TChannelEnum
         **/
        8:optional string channelType;
        /**
        * 行程信息
        **/
        9:optional TRideInfo rideInfo;
        /**
        * 拓展字段
        **/
        10:optional TPaymentNote paymentNote;
        /**
        * 解密后的ddc
        **/
        11:optional string ddcInfo;
        /**
        * 上一单订单id
        **/
        12:optional i64 oldTradeOrderId;
        /**
        * 用户cid
        **/
        13:optional string userCid;
        /**
        * 产品id，如行程ID,周卡ID等商品纬度的信息
        **/
        14:optional string productId;
        /**
        * 业务模式
        * @see com.didapinche.trade.thrift.enums.TBusinessModelEnum
        **/
        15:optional string businessModel;
    }

    /**
     * 支付请求参数
     **/
    struct TPauAddPaymentRequest{
        /**
        * 用户id
        **/
        1:optional i64 userId;
        /**
        * 主产品id
        **/
        2:optional string majorProductId;
        /**
        * 支付订单id
        **/
        3:optional i64 tradeOrderId;
        /**
        * 券包详情
        **/
        4:optional TTradeProductDetails goodsInfo;
        /**
        * 订单金额组成
        **/
        5:optional list<TOrderAmountDetails> orderAmountDetails;
        /**
        * 优惠券信息 涉及优惠券信息上送
        **/
        6:optional TCouponDetails couponDetails;
        /**
        * 行程信息
        **/
        7:optional TRideInfo rideInfo;
        /**
        * 版本
        **/
        8:optional string version;
        /**
        * 感谢费
        **/
        9:optional list<TTradeProductDetails> extraFeeInfos;
        /**
         * 新链路中券包对应的商品id
         */
        10:optional string orderCode;
        /**
         * :新/老系统商品标识 true:新 false:老
         */
        11: optional bool newGoodsFlag;
        /**
        *产品id，如行程ID,周卡ID等商品纬度的信息
        **/
        12:optional string productId;
        /**
        * 业务模式
        * @see com.didapinche.trade.thrift.enums.TBusinessModelEnum
        **/
        13:optional string businessModel;
        /**
         * json格式 业务方上送的透传字段,订单系统不关心具体内容，回调直接返回给业务组 <br>
         * 如顺风车高速费等信息可放在这里
         */
        14:optional string extra;
    }
    /**
     * 前置调用先乘后付补充支付请求参数
     **/
    struct TPlatformPauAddPaymentRequest{
        /**
        * 用户id
        **/
        1:optional i64 userId;
        /**
        * 主产品id
        **/
        2:optional string majorProductId;
        /**
        * 支付订单id
        **/
        3:optional i64 tradeOrderId;
        /**
        * 新的计算id
        **/
        4:optional i64 newCaclOrderId;
        /**
        * 变更后的所有产品信息
        **/
        5:optional list<TTradeProductDetails> newAllProductDetails;
        /**
        * 新增的产品信息
        **/
        6:optional list<TTradeProductDetails> addProductDetails;
        /**
        * 变更后的订单金额组成
        **/
        7:optional list<TOrderAmountDetails> newOrderAmountDetails;
        /**
        * 优惠券信息 涉及优惠券信息上送
        **/
        8:optional TCouponDetails couponDetails;
        /**
        * 业务模式
        * @see com.didapinche.trade.thrift.enums.TBusinessModelEnum
        **/
        9:optional string businessModel;
        /**
         * json格式 业务方上送的透传字段,订单系统不关心具体内容，回调直接返回给业务组 <br>
         */
        10:optional string extra;
        /**
        * 车主id
        **/
        11:optional i64 driverId;
    }
    /**
     * 扣款请求参数
     **/
    struct TPauPaymentCutRequest{
        /**
        * 用户id
        **/
        1:optional i64 userId;
        /**
        * 主产品id
        **/
        2:optional string majorProductId;
        /**
        * 支付订单id
        **/
        3:optional i64 tradeOrderId;
        /**
        * 扣款金额
        **/
        4:optional string cutAmount;
        /**
        * 合拼返款参数
        **/
        5:optional CarpoolMultiRefundRequest carpoolMultiRefundRequest;
        /**
        * 退差价参数
        **/
        6:optional PriceDifferencesRefundRequest request;
        /**
        * 车主id
        **/
        7:optional i64 driverUserId;
        /**
        * 扣费前增加的费用项目
        **/
        8:optional list<TTradeProductDetails> extraFeeInfos;
        /**
        * 扣费前增加的费用项目
        **/
        9:optional string productId;
        /**
        * 顺风车邀请返利参数
        **/
        10:optional CarpoolInviteReductionRequest carpoolInviteReductionRequest;
    }
    /**
     * 中止先享后付扣款请求参数
     **/
    struct TFinishPauCutRequest{
        /**
        * 用户id
        **/
        1:optional i64 userId;
        /**
        * 主产品id
        **/
        2:optional string majorProductId;
        /**
        * 支付订单id
        **/
        3:optional i64 tradeOrderId;
    }
    /**
     * 没收
     **/
    struct TConfiscateBacktrackRequest{
        /**
        * 用户id
        **/
        1:optional i64 userId;
        /**
        * 主产品id
        **/
        2:optional string majorProductId;
        /**
        * 支付订单id
        **/
        3:optional i64 tradeOrderId;
        /**
        * 业务类型
        * @see com.didapinche.trade.thrift.enums.TBusinessEnum
        **/
        4:optional string businessType;
        /**
        *
        * @see com.didapinche.trade.thrift.enums.TOrderSourceEnum
        **/
        5:optional string source;
        /**
        * 订单描述
        **/
        6:optional string orderDesc;
        /**
        * 歉意金 单位元 保留小数点2位
        **/
        7:optional string compensationAmount;
    }
    /**
     * 判断是否继续扣款请求参数
     **/
    struct TPauPaymentCutJudgeContinueRequest{
        /**
        * 用户id
        **/
        1:optional i64 userId;
        /**
        * 主产品id
        **/
        2:optional string majorProductId;
        /**
        * 支付订单id
        **/
        3:optional i64 tradeOrderId;
    }
    /**
     * 线下支付请求参数
     **/
    struct PaymentOfflineRequest{
        /**
        * 用户id
        **/
        1:required i64 userId;
        /**
        * 主产品id
        **/
        2:required string majorProductId;
        /**
        * 业务类型
        * @see com.didapinche.trade.thrift.enums.TBusinessEnum
        **/
        3:required string businessType;
        /**
        * 订单来源
        * @see com.didapinche.trade.thrift.enums.TOrderSourceEnum
        **/
        4:required string source;
        /**
        * 订单描述(1,30)
        **/
        5:required string desc;
        /**
        * 拓展字段
        **/
        6:optional TPaymentNote paymentNote;
        /**
        * 线下支付传递行程信息
        **/
        7:optional TTradeProductDetails tradeProductDetail;
    }
    /**
     * 支付响应结果
     * <AUTHOR>
     * @date 20:53 2022/6/29
     **/
    struct PaymentResponse{
        1: required i32 code;
        2: optional string message;
        /**
        * 订单支付结果
        **/
        3: optional PaymentResult data;
    }
    /**
     * 支付响应结果数据
     **/
    struct PaymentResult{
        /**
        * 支付订单号
        **/
        1: optional string payTradeNo;
        /**
        * 是否支付成功
        **/
        2: optional bool paid = false;
        /**
        * 三方支付统一下单返回
        **/
        3: optional ThirdUnifiedResult thirdUnifiedResult;

        // 账单id
        4: optional string billId;
    }
    /**
     * 三方支付方式统一下单信息
     **/
    struct ThirdUnifiedResult{
        /**
        * 三方支付订单号 <br>
        * 部分三方支付方式会返回,如京东支付
        **/
        1: optional string thirdOrderNo;
        /**
        * 支付宝/京东/云闪付 统一下单签名串
        **/
        2: optional string unifiedOrderResult;
        /**
        * 微信细分支付方式 <br>
        * APP 解析 winXinUnifiedResult <br>
        * JSAPI 解析 winXinJsApiUnifiedResult <br>
        * H5 解析 winXinH5UnifiedResult <br>
        * @see com.didapinche.trade.thrift.enums.TWinXinPayTypeEnum
        **/
        3: optional string winXinPayType;
        /**
        * 微信APP统一下单返回
        **/
        4: optional WinXinUnifiedResult winXinUnifiedResult;
        /**
        * 微信jsapi统一下单返回 需转map解析 <br>
        * map中key值: package paySign appId signType nonceStr timeStamp
        **/
        5: optional string winXinJsApiUnifiedResult;
        /**
        * 微信H5统一下单返回 字符串
        **/
        6: optional string winXinH5UnifiedResult;
        /**
        * 支付宝细分支付方式 <br>
        * 西安银行微信 解析 xiAnBankWxUnifiedResult <br>
        * 西安银行支付宝 解析 xiAnBankAliUnifiedResult <br>
        * @see com.didapinche.trade.thrift.enums.TXianBankPayTypeEnum
        **/
        7: optional string xianBankPayType;
        /**
        * 西安银行微信统一下单返回 需转map解析 <br>
        * map中key值: package paySign appId signType nonceStr timeStamp
        **/
        8: optional string xiAnBankWxUnifiedResult;
        /**
        * 西安银行支付宝统一下单返回 需转map解析<br>
        * map中key值: tradeNO
        **/
        9: optional string xiAnBankAliUnifiedResult;
        /**
        * 是否创建先享后付单
        **/
        10: optional bool pauCreateOrderFlag = false;
        /**
        * payment_order.order_no
        **/
        11: optional string paymentNo;
        /**
        * merchant 商城支付京东返回
        **/
        12: optional string merchant;
        /**
        * appKey 商城支付京东返回
        **/
        13: optional string appKey;
    }

    /**
     * 微信统一下单信息
     **/
    struct WinXinUnifiedResult{
        /**
        * 嘀嗒拼车微信支付的appid
        **/
        1: required string appid;
        /**
        * noncestr字段
        **/
        2: required string noncestr;
        /**
        * 商户id
        **/
        3: required string partnerid;
        /**
        * pay id
        **/
        4: required string prepayid;
        /**
        * sign
        **/
        5: required string sign;
        /**
        * 时间戳
        **/
        6: required string timestamp;
        /**
        * package字段, 由于关键字原因转化为wxpackage
        **/
        7: required string wxpackage;
    }
    /**
     * 先享后付支付响应结果
     **/
    struct TPauPaymentResponse{
        1: required i32 code;
        2: optional string message;
        /**
        * 订单支付结果
        **/
        3: optional TPauPaymentResult data;
    }
    /**
     * 先享后付支付响应结果数据
     **/
    struct TPauPaymentResult{
        /**
        * 订单id
        **/
        1: optional string tradeOrderId;

        2: optional string billId;
    }
    /**
     * 先享后付补充支付响应结果
     **/
    struct TPauAddPaymentResponse{
        1: required i32 code;
        2: optional string message;
        /**
        * 订单支付结果
        **/
        3: optional TPauAddPauPaymentResult data;
    }
    /**
     * 先享后付补充支付响应结果数据
     **/
    struct TPauAddPauPaymentResult{
        /**
        * 抵扣金额
        **/
        1: optional string deductionMoney;
    }
    /**
     * 先享后付扣款响应结果
     **/
    struct TPauPaymentCutResponse{
        1: required i32 code;
        2: optional string message;
        /**
        * 结果
        **/
        3: optional TPauPaymentCutResult data;
    }
    /**
     * 先享后付扣款响应结果数据
     **/
    struct TPauPaymentCutResult{
    }
    /**
     * 中止先享后付扣款响应结果
     **/
    struct TFinishPauCutResponse{
        1: required i32 code;
        2: optional string message;
        /**
        * 结果
        **/
        3: optional TFinishPauCutResult data;
    }
    /**
     * 中止先享后付扣款响应结果数据
     **/
    struct TFinishPauCutResult{
    }
    /**
     * 没收响应结果
     **/
    struct TConfiscateBacktrackResponse{
        1: required i32 code;
        2: optional string message;
        /**
        * 结果
        **/
        3: optional TConfiscateBacktrackResult data;
    }
    /**
     * 没收响应结果数据
     **/
    struct TConfiscateBacktrackResult{
       /**
       * 扣款id
       **/
       1: optional i64 confiscateOrderId;
       /**
       * 实际扣款金额
       **/
       2: optional string actualConfiscateAmount;
       /**
       * 先享后付扣款金额 先享后付单返回
       **/
       3: optional string pauCutAmount;
    }
    /**
     * 先享后付是否可以继续扣款响应结果
     **/
    struct TPauPaymentCutJudgeContinueResponse{
        1: required i32 code;
        2: optional string message;
        /**
        * 结果
        **/
        3: optional TPauPaymentCutJudgeContinueResult data;
    }
    /**
     * 先享后付是否可以继续扣款响应结果数据
     **/
    struct TPauPaymentCutJudgeContinueResult{
        /**
        * 当前扣款状态
        **/
        1: optional string cutStatus;
        /**
        * 继续标识
        **/
        2: optional bool continueflag;
    }
    /**
     * 检查支付请求参数
     **/
    struct CheckPaymentRequest{
        /**
        * 用户支付信息
        **/
        1:required TUserPaymentParam userPaymentParam;
        /**
        * 总金额 单位元 保留2位小数
        **/
        2:required string totalPrice;
        /**
        * 支付方式 区分普通支付 免密支付 快捷付 先享后付
        * @see com.didapinche.trade.thrift.enums.TPaymentWayEnum
        **/
        3:optional string paymentWay = 'normal';
        /**
        * 行程信息 <br>
        * 顺风车普通单送ride信息 下单推荐单送支付价格最高的信息
        **/
        4:optional TRideInfo rideInfo;
        /**
        * 候补路线行程信息 <br>
        * 调用优惠券服务使用 <br>
        * 目前顺风车使用
        **/
        5:optional list<TCandidateLineInfo> candidateLineInfoList;
        /**
         * 优惠券id 有则送
         */
        6:optional i64 couponId;
        /**
        * 券包信息 有则送
        **/
        7:optional TGoodsInfo goodsInfo;
        /**
        * 订单金额组成
        **/
        8:required list<TOrderAmountDetails> orderAmountDetails;
        /**
         * 三方支付渠道
         * @see com.didapinche.trade.thrift.enums.TChannelEnum
         **/
        9:optional string channelType;
        /**
        * 企业信息
        **/
        10:optional TEnterpriseDetails enterpriseDetails;
        /**
         * 优惠券新老链路标识
         **/
        11:optional bool newGoodsFlag;
        /**
         * 选中的优惠券id
         **/
        12:optional string couponSetId;
        /**
        * 9.69.0增加随单购，故使用该字段代替7.TGoodsInfo 字段
        * 只有一个goodsInfo的情况也使用该字段，放弃7字段
        **/
        13:optional list<TGoodsInfo> goodsInfoList;
    }

    /**
     * 检查支付响应结果
     **/
    struct CheckPaymentResponse{
        1: required i32 code;
        2: optional string message;
        3: optional CheckPaymentResult data;
    }
    /**
     * 检查支付响应数据
     **/
    struct CheckPaymentResult{
        /**
        *  优惠券信息
        **/
        1: optional TCouponDetails couponDetails;
    }
    /**
     * 退款请求参数
     * <AUTHOR>
     * @date 20:53 2022/6/29
     **/
    struct RefundRequest{

    }
    /**
     * 退款响应结果
     * <AUTHOR>
     * @date 20:53 2022/6/29
     **/
    struct RefundResponse{
        1: required i32 code;
        2: optional string message;
        3: optional list<RefundResult> data;
    }
    /**
     * 退款响应结果数据
     * <AUTHOR>
     * @date 20:53 2022/6/29
     **/
    struct RefundResult{
        /**
        *  支付订单id
        **/
        1: required i64 payOrderId;
        /**
        *  支付订单id
        **/
        2: required i64 refundOrderId;
        /**
        *  优惠券id (注意：只有优惠券退回才会返回当前字段，部分退款场景可能优惠券会有抵扣金额，但是不会退优惠券)
        **/
        3: optional i64 couponId;
        /**
        *  三方退款渠道，third > 0 返回
        **/
        4: optional string thirdChannel;
        /**
        *  退款总金额
        **/
        5: optional string totalPrice;
        /**
        *  优惠券退款金额
        **/
        6: optional string coupon;
        /**
        *  第三方退款金额
        **/
        7: optional string third;
        /**
        *  乘客可提现账户退款金额
        **/
        8: optional string passenger;
        /**
        *  乘客不可体现账户退款金额
        **/
        9: optional string passengerNotWithdraw;
        /**
        * 顺风金退款金额
        **/
        10: optional string bonus;
        /**
        * 出租车api企业
        **/
        11: optional string tEnterpriseVip;
         /**
        * 顺风车api企业
        **/
        12: optional string cEnterpriseVip;
        /**
       * 出租车非api企业
       **/
        13: optional string tEnterprise;
        /**
         * 顺风金成本回收
         **/
        14: optional string bonusRecover;
         /**
         * 优惠券成本回收
         **/
         15: optional string couponRecover;
         /**
         * 补贴成本回收金额
         **/
         16: optional string passengerSubsidyAmountRecover;
    }

/**
 * 智慧码到账请求参数
 **/
struct TTaxiQrTransferRequest{
    /**
    * 乘客用户id
    **/
    1:optional i64 userId;
    /**
    * 主产品id
    **/
    2:optional string majorProductId;
    /**
    * 支付订单id
    **/
    3:optional i64 tradeOrderId;
    /**
    * 三方渠道
    **/
    4:optional string thirdChannel;
    /**
    * 业务类型
    * @see com.didapinche.trade.thrift.enums.TBusinessEnum
    **/
    5:optional string businessType;
    /**
    * 订单来源
    * @see com.didapinche.trade.thrift.enums.TOrderSourceEnum
    **/
    6:optional string source;
    /**
    * 是否重试 默认不重试
    **/
    7:optional bool profitSharingRetry = false;
}
/**
 * 智慧码到账响应结果
 **/
struct TTaxiQrTransferResponse{
    1: required i32 code;
    2: optional string message;
    3: optional TTaxiQrTransferResult data;
}
/**
 * 智慧码到账响应结果数据
 **/
struct TTaxiQrTransferResult{
    /**
    * 转账订单id
    **/
    1:optional i64 transferTradeOrderId;
    /**
    * 转账状态
    **/
    2:optional string status;
}


/**
 * 智慧码到账确认请求参数
 **/
struct TTaxiQrTransferConfirmRequest{
    /**
    * 乘客用户id
    **/
    1:optional i64 userId;
    /**
    * 主产品id
    **/
    2:optional string majorProductId;
    /**
    * 支付订单id
    **/
    3:optional i64 tradeOrderId;
    /**
    * 乘客用户id
    **/
    4:optional i64 driverUserId;
    /**
    * 乘客用户id
    **/
    5:optional string driverName;
    /**
    * 转账订单id
    **/
    6:optional i64 transferTradeOrderId;
    /**
    * 三方渠道
    **/
    7:optional string thirdChannel;
    /**
    * 业务类型
    * @see com.didapinche.trade.thrift.enums.TBusinessEnum
    **/
    8:optional string businessType;
    /**
    * 订单来源
    * @see com.didapinche.trade.thrift.enums.TOrderSourceEnum
    **/
    9:optional string source;
    /**
    * 是否重试 默认不重试
    **/
    10:optional bool profitSharingRetry = false;
    /**
    * mq重新消费次数
    **/
    11:optional i32 reConsumeTimes;
}
/**
 * 智慧码到账确认响应结果
 **/
struct TTaxiQrTransferConfirmResponse{
    1: required i32 code;
    2: optional string message;
    3: optional TTaxiQrTransferConfirmResult data;
}
/**
 * 智慧码到账确认响应结果数据
 **/
struct TTaxiQrTransferConfirmResult{
    /**
    * 转账状态
    **/
    1:optional string status;
}

/**
 * 赔付请求参数
 * <AUTHOR>
 * @date 20:53 2022/6/29
 **/
struct CompensateRequest{
    /**
    * 业务订单id，会被用于幂等操作，如果属于不同的赔付单据，那么传递不同的值
    **/
    1:required string productId;
    /**
    * 产品类型，赔付
    * businessType=TBusinessEnum.compensate_api_to_user
    **/
    2:required string businessType;
    /**
    * 顺风车：TOrderSourceEnum.carpool
    * 出租车：TOrderSourceEnum.taxi
    **/
    3:required string source;
    /**
     * 出款人Id
     * API外输赔付时，传递各自的三方渠道id，同外输支付时的companyId
     **/
    4:required i64 payOutUserId;
    /**
     * 收款人id
     **/
    5:required i64 payInUserId;
    /**
    * 赔付金额（元）
    **/
    6:required string compensateAmount;
    /**
    * 手续费金额（元）
    * 没有传递0元
    **/
    7:required string commissionAmount;
    /**
    * 赔付行程id,用于分库
    **/
    8:required string compensateProductId;
    /**
    * 赔付的车费支付订单id
    **/
    9:required string compensateOrderId;
    /**
    * 扩展信息，其他没那么重要的信息写到扩展里
    * json格式
    **/
    10:optional string extendMap;
    /**
    * 备注
    **/
    11:optional string comment;
    /**
    * 操作人id 默认系统操作 151
    **/
    12:optional i32 sysOpId=151;
}
    /**
     * 赔付响应结果
     * <AUTHOR>
     * @date 20:53 2022/6/29
     **/
    struct CompensateResponse{
        1: required i32 code;
        2: optional string message;
        3: optional CompensateResult data;
    }
    /**
     * 赔付响应结果数据
     * <AUTHOR>
     * @date 20:53 2022/6/29
     **/
    struct CompensateResult{

    }

/**
* 赔付记录查询请求
**/
struct QueryCompensateRequest{
    /**
    * 产品类型，赔付
    * businessType=TBusinessEnum.compensate_api_to_user
    **/
    1:optional string businessType;
    /**
    * 顺风车：TOrderSourceEnum.carpool
    * 出租车：TOrderSourceEnum.taxi
    **/
    2:optional string source;
    /**
     * 收款人id,用于分表
     **/
    3:optional i64 payInUserId;
    /**
    * 原始行程id,用于分库
    **/
    4:optional string compensateProductId;
}

/**
* 赔付记录响应
**/
struct QueryCompensateResponse{
    1: required i32 code;
    2: optional string message;
    3: optional list<CompensateDetail> data;
}

struct CompensateDetail{
   /**
   * 赔付记录内部id
   **/
   1: optional i64 id;
   /**
   * 赔付金额（元）
    **/
   2: optional string amount;
   /**
   * 业务类型
   **/
   3: optional string businessType;
}

    /**
     * 收银台请求参数
     **/
    struct QueryCashierRequest {
        /**
        * 用户Cid
        **/
        1:required string userCid;
        /**
        * 用户支付信息
        **/
        2:required TUserPaymentParam userPaymentParam;
        /**
        * 券包信息
        **/
        3:optional list<TGoodsInfo> goodsInfoList;
        /**
        * 行程信息 <br>
        * 顺风车普通单送ride信息 下单推荐单送支付价格最高的信息
        **/
        4:optional TRideInfo rideInfo;
        /**
        * 顺风车候补路线行程信息 <br>
        * 调用优惠券服务使用
        **/
        5:optional list<TCandidateLineInfo> candidateLineInfoList;
        /**
        * 企业信息
        **/
        6:optional TEnterpriseDetails enterpriseDetails;
        /**
        *  场景定义：
        *  @see com.didapinche.trade.thrift.enums.QueryCashierSceneEnum
        *
        *  不传默认普通收银台场景
        **/
        7:optional string scene;
        /**
        * ddc info
        **/
        8:optional string ddcInfo;
        /**
        * 顺风车预支付流程优化灰度场景：命中上送：test1,未命中上送：control1
        * \n其他场景不传
        **/
        9:optional string abType;
    }
    /**
    * 用户支付参数
    **/
    struct TUserPaymentParam {
        /**
        * 用户id <br>
        * 注意代付订单没有支付用户id 获取该字段可能未空
        **/
        1:optional i64 userId;
        /**
        * 主产品id
        **/
        2:required string majorProductId;
        /**
        * 候补路线ids <br>
        * 目前顺风车下单推荐使用
        **/
        3:optional string candidateRideLineIds;
        /**
        * 业务类型
        * @see com.didapinche.trade.thrift.enums.TBusinessEnum
        **/
        4:required string businessType;
        /**
        * 订单来源
        * @see com.didapinche.trade.thrift.enums.TOrderSourceEnum
        **/
        5:required string source;
        /**
        * 用户的版本 ddcinfo中读取
        **/
        6:optional string version = "0";
        /**
        * 设备号 ddcinfo中读取 有则传
        **/
        7:optional string deviceNo;
        /**
        * ip
        **/
        8:optional string ipHost = "0";
        /**
        * 业务模式
        * @see com.didapinche.trade.thrift.enums.TBusinessModelEnum
        **/
        9:optional string businessModel;
    }
    /**
    * 券包信息
    **/
    struct TGoodsInfo {
        /**
        * 券包id
        **/
        1:required string goodsId;
        /**
        * 券包类型 目前只有随单券包和无忧卡
        * @see com.didapinche.trade.thrift.enums.TTradeProductTypeEnum
        **/
        2:required string goodsType;
        /**
        * 券包价格 单位元 保留2位小数
        **/
        3:required string goodsPrice;
        /**
        * checkPayment()接口上送 单位元 保留2位小数<br>
        * 支付时调用活动组接口获取到的金额
        **/
        4:optional string cashPrice;
        /**
         * 新系统商品的唯一标识
         */
        5: optional string goodsCode;
        /**
        * :新/老系统商品标识 true:新 false:老
        */
        6: optional bool newGoodsFlag;
    }
    /**
    * 行程信息
    **/
    struct TRideInfo{
        /**
        * 用户id
        **/
        1:optional i64 userId;
        /**
        * 司机id 出租车使用
        **/
        2:optional i64 driverId = 0;
        /**
        * 行程需要支付的金额 单位元 保留2位小数
        * 出租车:singlePrice+thankPrice+tollPrice
        **/
        3:optional string rideMoney;
        /**
        * 行程价格 单位元 保留2位小数 <br>
        * 顺风车对应suggestPrice <br>
        * 出租车对应singlePrice
        **/
        4:optional string singlePrice;
        /**
         * 出租车调度费/顺风车感谢费 单位元 保留2位小数
         */
        5:optional string thankPrice;
        /**
         * 出租车调度费 数据库记录值 <br>
         * 用于校验端上上送的与实际库里记录的是否一致
         */
        6:optional string thankPriceDB;
        /**
         * 出租车过路费 单位元 保留2位小数
         */
        7:optional string tollPrice;
        /**
        * 行程类型
        **/
        8:optional string rideType = "0";
        /**
        * 起点经度
        **/
        9:optional string startLon = "0";
        /**
        * 起点纬度
        **/
        10:optional string startLat = "0";
         /**
         * 终点经度
         **/
        11:optional string endLon = "0";
        /**
        * 终点纬度
        **/
        12:optional string endLat = "0";
        /**
        * 起点城市id
        **/
        13:optional i32 startCityId = 0;
        /**
        * 计划出发时间 yyyyMMddHHmmss
        **/
        14:optional string planStartTime;
        /**
        * 顺风金是否可用 默认false <br>
        * 目前只有顺风车使用
        **/
        15:optional bool bonusAble = false;
        /**
        * 主单标识 默认为true <br>
        * 顺风车下单推荐补单送false
        **/
        16:optional bool majorFlag = true;
        /**
        * 作弊单标识 默认为false queryCashier接口使用<br>
        * 目前只有出租车使用
        **/
        17:optional bool cheatFlag = false;
        /**
        * 出租车司机发起的收款金额信息 checkPayment接口使用<br>
        * 目前只有出租车使用
        **/
        18:optional string driverPriceInfo;
        /**
        *  最低价格 （油电会有两个价格）
        **/
        19:optional string lowestSinglePrice;
        /**
        *  订单是否接单标识
        **/
        20:optional bool replied = false;
        /**
        * 顺风车下单时间 yyyyMMddHHmmss
        **/
        21:optional string createTime;
        /**
        * 平台给乘客的补贴金额，单位元
        **/
        22:optional string passengerSubsidyAmount;
        /**
        * 出租车拼车特有字段，临时附加费，单位元
        **/
        23:optional string tempSurcharge;
        /**
        * 行程价格list
        **/
        24:optional list<TPriceInfo> singlePriceList;
    }
    /**
    * 价格信息
    **/
    struct TPriceInfo{
        /**
        * id
        **/
        1:optional string id;
        /**
        * 行程价格 单位元 保留2位小数 <br>
        * 顺风车对应suggestPrice <br>
        * 出租车对应singlePrice
        **/
        2:optional string singlePrice;
    }
    /**
    * 候补路线行程信息
    **/
    struct TCandidateLineInfo{
        /**
        * 行程价格 单位元 保留2位小数 <br>
        * 顺风车对应suggestPrice <br>
        * 出租车对应singlePrice
        **/
        1:required string singlePrice;
        /**
        * 起点经度
        **/
        2:optional string startLon;
        /**
        * 起点纬度
        **/
        3:optional string startLat;
         /**
         * 终点经度
         **/
        4:optional string endLon;
        /**
        * 终点纬度
        **/
        5:optional string endLat;
    }
    /**
     * 收银台响应结果
     **/
    struct QueryCashierResponse{
        1: required i32 code;
        2: optional string message;
        /**
        * 收银台响应结果
        **/
        3: optional QueryCashierResult data;
    }

    /**
     * 收银台响应结果
     **/
    struct QueryCashierResult{
        /**
        * 用户支付账户信息
        **/
        1: optional TUserPayAccountInfo userPayAccountInfo;
        /**
        * 可用优惠券数量
        **/
        2: optional i32 availableCouponsCount = 0;
        /**
        * 默认优惠券信息
        **/
        3: optional string defaultCouponInfo;
        /**
        * 三方支付方式信息
        **/
        4: optional string thirdPaymentInfo;
        /**
        * 企业规则信息list <br>
        * 出租车企业付收银台返回
        **/
        5: optional list<TEnterpriseRuleInfo> enterpriseRuleInfoList;
        /**
        * 收银台推荐开通芝麻先享后付
        **/
        6: optional string zhimaPauPaymentType;
        /**
        * 支付宝芝麻先享后付及推荐下单并签约免密支付信息
        **/
        7: optional TZhimaPauPaymentInfo zhimaPauPaymentInfo;
        /**
        * 微信先享后付和芝麻先享后付的引导
        **/
        8: optional string guidePaymentType;
        /**
        * 保留对芝麻先享后付的引导zhima_pau，规避微信合规问题
        **/
        9: optional string reserveGuidePaymentType;
        /**
        * 先用后付开通状态
        **/
        10: optional bool pauOpenStatus;
        /**
        * 支付渠道优惠，顺风车透传该字段给前端，这是一个JSON字段
        **/
        11: optional string suggestPaymentWay;
        /**
        * 业务直接透传给APP
        **/
        12: optional string payinfoKey;
        /**
        * 0：不弹，1：弹，支付渗透
        **/
        13: optional i32 popupWindow;
    }
    /**
    * 支付宝芝麻先享后付及推荐下单并签约免密支付信息
    **/
    struct TZhimaPauPaymentInfo{
        /**
         * 	芝麻支付类型，pas：先享后付，pws：免密代付
         */
        1:  optional string zhimaPaymentType;
        /**
         * 芝麻支付展示文案
         */
        2:  optional string zhimaPaymentComment;
        /**
         * 	芝麻对应支付方式是否推荐。（0.普通状态，1.推荐）
         */
        3:  optional i32 zhimaState;
        /**
         * 	芝麻推荐文案
         */
        4:  optional string zhimaStateComment;
        /**
         *  默认选中（1:选中，0 非选中）
         */
        5:  optional i32 zhimaselected;
    }
    /**
    * 企业规则信息
    **/
    struct TEnterpriseRuleInfo{
        /**
        * 权限id
        **/
        1: required i32 ruleId;
        /**
        * 权限名称
        **/
        2: required string ruleName;
        /**
        * 权限是否可用（0-否 1-是）
        **/
        3: required i32 ruleAvailable;
        /**
        * 权限本单可抵扣金额(单位：分)
        **/
        4: optional i32 rulePayMoney;
        /**
        * 权限不可用原因
        **/
        5: optional string ruleMsg;
        /**
        * 不能完全抵扣原因
        **/
        6: optional string payMoneyMsg;
    }

    /**
    * 用户支付账户信息
    **/
    struct TUserPayAccountInfo {
        /**
        * 用户cid
        **/
        1: required string userCid;
        /**
        * 账户类型标识，1：乘客账户，2车主账户
        **/
        2: required i32 balanceIdentifying = 1;
        /**
        * 账户合计余额 单位元 保留2位小数 <br>
        * 对应老接口Total_balance和User_balance
        **/
        3: required string accountBalance = "0.00";
        /**
        * 顺风金余额 单位元 保留2位小数 <br>
        * 对应老接口bonus_cent
        **/
        4: required string bonusBalance = "0.00";
        /**
        * 顺风金即将过期（0-否 1-是）
        **/
        5: required i32 isBonusAboutExpire = 0;
        /**
        * 顺风金使用超过十次（0-否 1-是）
        **/
        6: required i32 bonusUseMoreTen = 0;
        /**
        * 顺风金是否默认勾选（0-否 1-是）
        **/
        7: required i32 bonusSelected = 0;
    }

    /**
     * 按账户（三方 、油费、 余额、优惠券、顺风金）顺序退款（cms退款）
     * 券包商品不可退
     **/
    struct CmsRefundRequest{
        /**
        * 需要退款的订单集合
        **/
        1: required list<i64> orderIdList;
        /**
        *  乘客id
        **/
        2: required i64 userId;
        /**
        *  乘客退款总金额
        **/
        3: required string userRefundFee;
        /**
        *  司机id
        **/
        4: required i64 driverId;
        /**
        *  司机/车主退款总金额
        **/
        5: optional string driverRefundFee;
        /**
        * 业务类型
        * @see com.didapinche.trade.thrift.enums.TBusinessEnum
        **/
        6: required string businessType;
        /**
        *
        * @see com.didapinche.trade.thrift.enums.TOrderSourceEnum
        **/
        7: optional string source;
        /**
        * 订单描述
        **/
        8: optional string orderDesc;
        /**
        * 业务侧id
        **/
        9:required string productId;
        /**
        * 业务侧关联记录交易id
        **/
        10:required i64 majorOrderId;
        /**
        * 是否是全部退
        **/
        11:required bool allRefundFlag;
        /**
        * 原接口信息
        **/
        13:optional string originalInfo;
        /**
        * 操作人id 默认系统操作 151
        **/
        14:optional i32 sysOpId=151;
        /**
        *  到账时的majorProductId
        **/
        15:required string transferProductId;
        /**
        *  补贴数据
        **/
        16: optional list<RideSubsidyInfo> rideSubsidyInfo;
        /**
        *
        **/
        17: optional string remarks;
        /**
        *
        **/
        18: optional string details;
        /**
        *
        **/
        19: optional string comment;
        /**
        *
        **/
        20: optional string serviceFee;
        /**
         * json格式 业务方上送的透传字段,订单系统不关心具体内容，回调直接返回给业务组 <br>
         * 如顺风车ID,周卡ID等信息
         **/
        21: optional string extra;
    }

    struct RideSubsidyInfo{
        /**
        * 补贴对应的扣款类型
        **/
        1: required string businessType;
        /**
        *  司机/车主扣款（补贴）金额
        **/
        2: required string subsidyFee;
    }


    /**
     * 三方和余额按照订单顺序退款 （补偿金）
     * 券包商品不可退
     **/
    struct ThirdCashRefundRequest{
        /**
        * 需要退款的订单集合
        **/
        1: required list<i64> orderIdList;
        /**
        *  乘客id
        **/
        2: required i64 userId;
        /**
        *  乘客退款总金额
        **/
        3: required string userRefundFee;
        /**
        * 业务类型
        * @see com.didapinche.trade.thrift.enums.TBusinessEnum
        **/
        4: required string businessType;
        /**
        *
        * @see com.didapinche.trade.thrift.enums.TOrderSourceEnum
        **/
        5: optional string source;
        /**
        * 订单描述
        **/
        6: optional string orderDesc;
        /**
        * 业务侧id
        **/
        7:required string productId;
        /**
        * 业务侧关联记录交易id
        **/
        8:required i64 majorOrderId;
        /**
        * 原订单信息
        **/
        9:optional string originalInfo;
    }

    /**
     * 顺风车合拼返款
     * 券包商品不可退
     **/
    struct CarpoolMultiRefundRequest{
        /**
        * 需要退款的订单集合
        **/
        1: required list<i64> orderIdList;
        /**
        *  乘客id
        **/
        2: required i64 userId;
        /**
        *  乘客退款金额
        **/
        3: required string userRefundFee;
        /**
        * 业务类型
        * @see com.didapinche.trade.thrift.enums.TBusinessEnum
        **/
        4: required string businessType;
        /**
        *
        * @see com.didapinche.trade.thrift.enums.TOrderSourceEnum
        **/
        5: optional string source;
        /**
        * 订单描述
        **/
        6: optional string orderDesc;
        /**
        * 业务侧id
        **/
        7:required string productId;
        /**
        * 业务侧关联记录交易id
        **/
        8:required i64 majorOrderId;
        /**
        * 原订单信息
        **/
        9:optional string originalInfo;
        /**
        *  成本回收金额
        **/
        10: optional string recoveryFee;
        /**
        * 当前订单使用的顺风金金额 (元)
        **/
        11:required string bonus;
        /**
        * 当前订单使用的优惠券金额 (元)
        **/
        12:required string coupon;
        /**
        * 当前订单使用的乘客补贴金额，单位元
        **/
        13:optional string passengerSubsidyAmount;
    }

    /**
     * 顺风车邀请返利
     **/
    struct CarpoolInviteReductionRequest{
         /**
        * 产品id，如行程ID,周卡ID等,用于把所有某个个产品下的支付给串起来
        **/
        1:optional string primaryProductId;
        /**
        *  乘客id
        **/
        2: required i64 userId;
        /**
        * 业务侧关联记录交易id
        **/
        3:required i64 majorOrderId;
        /**
        *  乘客退款金额
        **/
        4: required string userRefundFee;
        /**
        * 订单描述
        **/
        5: optional string orderDesc;
        /**
        * 三方+余额抵扣金额 单位元
        **/
        6: required string paymentRealAmount;
        /**
        * 优惠券抵扣金额 单位元
        **/
        7: required string couponRealAmount;
    }

    /**
     * 当前订单全部退款（重复支付）
     *  包包含券包
     **/
    struct AllRefundRequest{
        /**
        * 需要退款的订单集合
        **/
        1: required i64 orderId;
        /**
        *  乘客id
        **/
        2: required i64 userId;
        /**
        * 业务类型
        * @see com.didapinche.trade.thrift.enums.TBusinessEnum
        **/
        3: required string businessType;
        /**
        *
        * @see com.didapinche.trade.thrift.enums.TOrderSourceEnum
        **/
        4: optional string source;
        /**
        * 订单描述
        **/
        5: optional string orderDesc;
        /**
        * 业务侧id
        **/
        6:required string productId;
        /**
        * 原接口信息
        **/
        7:optional string originalInfo;
    }

    /**
     * 三方和余额按照订单顺序抵扣剩余金额退回 （取消订单退款）
     *  券包商品不可退
     **/
    struct ThirdCashDeductRefundRequest{
        /**
        * 需要退款的订单集合
        **/
        1: required list<i64> orderIdList;
        /**
        *  乘客id
        **/
        2: required i64 userId;
        /**
        * 业务类型
        * @see com.didapinche.trade.thrift.enums.TBusinessEnum
        **/
        3: required string businessType;
        /**
        * 来源
        * @see com.didapinche.trade.thrift.enums.TOrderSourceEnum
        **/
        4: optional string source;
        /**
        * 订单描述
        **/
        5: optional string orderDesc;
        /**
        *  补偿金金额
        **/
        6: required string compensationFee;
        /**
        * 业务侧id
        **/
        7:required string productId;
        /**
        * 业务侧关联记录交易id
        **/
        8:required i64 majorOrderId;
        /**
        * 原接口信息
        **/
        9:optional string originalInfo;
        /**
        *  场景定义：
        *  systemReorder: 系统取消 系统重新下单的先乘后付订单，如果有随单券包需要把券包退掉
        **/
        10:optional string scene;
    }


    /**
     *  只退第三方相关金额
     **/
    struct ThirdRefundRequest{

        /**
        *  用户id
        **/
        1: required i64 userId;
        /**
        * 业务类型
        * @see com.didapinche.trade.thrift.enums.TBusinessEnum
        **/
        2: required string businessType;
        /**
        *
        * @see com.didapinche.trade.thrift.enums.TOrderSourceEnum
        **/
        3: optional string source;
        /**
        * 订单描述
        **/
        4: optional string orderDesc;
        /**
        *  退款金额
        **/
        5: required string refundFee;
        /**
        * 业务侧id
        **/
        6:required string productId;
        /**
        * 业务侧关联记录交易id
        **/
        7:required i64 orderId;
        /**
        * 原接口信息
        **/
        8:optional string originalInfo;
    }

    /**
     *  顺风车高速费退款接口
     **/
    struct CarpoolHighwayResponse{

        1: required i32 code;
        2: optional string message;
        3: optional list<RefundResult> refundData;
        4: optional ConfiscateResult confiscateData;
    }

    /**
     *  顺风车高速费退款接口
     **/
    struct CarpoolHighwayRequest{

        /**
        *  用户id
        **/
        1: required i64 userId;
        /**
        * 业务类型
        * @see com.didapinche.trade.thrift.enums.TBusinessEnum
        **/
        2: required i64 driverId;
        /**
        *
        * @see com.didapinche.trade.thrift.enums.TOrderSourceEnum
        **/
        3: optional string source;
        /**
        * 订单描述
        **/
        4: optional string orderDesc;
        /**
        *  退款金额
        **/
        5: required string refundFee;
        /**
        * 业务侧id
        **/
        6:required string productId;
        /**
        * 业务侧关联支付记录交易id
        **/
        7:required i64 orderId;
        /**
        * 原接口信息
        **/
        8:optional string originalInfo;
    }

    /**
     * 按订单金额退回多支付的金额（退差价）
     * 券包商品不可退
     **/
    struct PriceDifferencesRefundRequest{
        /**
        * 需要退款的订单集合
        **/
        1: required list<i64> orderIdList;
        /**
        *  乘客id
        **/
        2: required i64 userId;
        /**
        * 业务类型
        * @see com.didapinche.trade.thrift.enums.TBusinessEnum
        **/
        3: required string businessType;
        /**
        *
        * @see com.didapinche.trade.thrift.enums.TOrderSourceEnum
        **/
        4: optional string source;
        /**
        * 订单描述
        **/
        5: optional string orderDesc;
        /**
        * 三方+余额抵扣金额 单位元
        **/
        6: required string paymentRealAmount;
        /**
        * 优惠券抵扣金额 单位元
        **/
        7: required string couponRealAmount;
        /**
        * 顺风金抵扣金额 单位元
        **/
        8: required string bonusRealAmount;
        /**
        * 业务侧id
        **/
        9:required string productId;
        /**
        * 业务侧关联记录交易id
        **/
        10:required i64 majorOrderId;
        /**
        * 原订单信息
        **/
        11:optional string originalInfo;
        /**
        * 平台给乘客的补贴金额，单位元
        **/
        12:optional string passengerSubsidyRealAmount;
    }

    /**
    * 查询灰度开关
    **/
    struct GrayScaleRequest{
        /**
        *  乘客id
        **/
        1: required i64 userId;
        /**
        *  支付订单id
        **/
        2: required i64 orderId;
    }

    /**
    * 查询灰度结果
    **/
    struct GrayScaleResponse{
       1: required i32 code;
       2: optional string message;
       3: optional GrayScaleResult data;
    }

    /**
    * 查询灰度结果
    **/
    struct GrayScaleResult{
       1: required bool grayScale;
    }

    /**
    * 查询灰度结果
    **/
    struct GraySwitchRequest{
       1: required string businessType;
       2: required string source;
       3: required i64 userId;
    }
    /**
    * 查询灰度结果
    **/
    struct GraySwitchResponse{
       1: required i32 code;
       2: optional string message;
       3: optional string data;
       4: optional list<GraySwitch> dataList;
    }

    /**
     * 汇款
     **/
    struct RemittanceRequest{

        /**
        * 业务订单id
        **/
        1:required string productId;
        /**
        * 产品类型，赔付
        **/
        2:required string businessType;
        /**
        * 原支付订单id
        **/
        3:required i64 payTradeId;
        /**
         * 出款人Id
         **/
        4:optional i64 payUserId;
        /**
         * 出款人账户类型 @see com.didapinche.trade.thrift.enums.TOrderAccountTypeEnum
         **/
        5:optional string payAccountType;
        /**
         * 收款人id
         **/
        6:required i64 incomeUserId;
        /**
         * 收款人账户类型 @see com.didapinche.trade.thrift.enums.TOrderAccountTypeEnum
         **/
        7:optional string incomeAccountType;
        /**
        * 总金额
        **/
        8:required string sumFee;
        /**
        * 支付金额（元）
        **/
        9:optional string payFee;
        /**
        * 付款金额（元）
        **/
        10:required string incomeFee;
        /**
        * 描述
        **/
        11:optional string desc;
        /**
        * 操作人id 默认系统操作 151
        **/
        12:optional i32 sysOpId=151;
        /**
        * 到账设计订单集合，没有当前参数取payTradeId做校验
        **/
        13:optional list<i64> orderIdList;
        /**
        * 内部使用的备注
        **/
        14:optional string remarks;
        /**
        * 明细
        **/
        15:optional string details;
        /**
        * 备注
        **/
        16:optional string comment;
        /**
        * 原订单信息
        **/
        17:optional string originalInfo;
        /**
        * 原支付订单关联的外部行程号或者活动交易号
        **/
        18: optional string originPayMajorProductId;
    }

    /**
    * 汇款结果
    **/
    struct RemittanceResponse{
       1: required i32 code;
       2: optional string message;
       3: optional RemittanceResult data;
    }

    /**
    * 汇款结果
    **/
    struct RemittanceResult{
        1: required i64 tradeOrderId;
    }


    /**
     * 顺风车免单
     **/
    struct CarpoolFreeRequest{

        /**
        * 业务订单id
        **/
        1:required string productId;
        /**
        * 原支付订单id
        **/
        2:required i64 payTradeId;
        /**
         * 车主id
         **/
        3:required i64 driverId;
        /**
         * 乘客id
         **/
        4:required i64 passengerId;
        /**
        * 总金额
        **/
        5:required string sumFee;
        /**
        * 车主扣款金额
        **/
        6:optional string backFromDriver;
        /**
        * 乘客收款金额
        **/
        7:required string backToUser;
        /**
        *  高速费金额
        **/
        8:optional string extraFee;
        /**
        * 高速费订单id
        **/
        9:optional i64 extraPayTradeId;
        /**
        * 到账设计订单集合，没有当前参数取payTradeId做校验
        **/
        10:optional list<i64> orderIdList;
        /**
        * 当前订单使用的顺风金金额 (元)
        **/
        11:required string bonus;
        /**
        * 当前订单使用的优惠券金额 (元)
        **/
        12:required string coupon;
        /**
        * 原订单信息
        **/
        13:optional string originalInfo;
        /**
        * 业务类型
        **/
        14:required string businessType;
        /**
        * 当前订单使用的乘客补贴金额 (元)
        **/
        15:optional string passengerSubsidyAmount;
    }

    /**
    * 顺风车免单
    **/
    struct CarpoolFreeResponse{
       1: required i32 code;
       2: optional string message;
       3: optional CarpoolFreeResult data;
    }

    /**
    * 顺风车免单结果
    **/
    struct CarpoolFreeResult{
        1: required i64 tradeOrderId;
        2: required string backFromDriver;
    }


    /**
    * 查询退款订单
    **/
    struct QueryRefundTradeRequest{
        /**
        *  乘客id
        **/
        1: required i64 userId;
        /**
        *  支付订单id
        **/
        2: required list<i64> orderIdList;
        /**
        *  是否包含商城退款,默认是false
        **/
        3: required bool includeCouponMallRefund = false;
        /**
        *  包含的类型
        **/
        4: optional list<string> includeBusinessTypes;
        /**
        *  不包含的类型
        **/
        5: optional list<string> excludeBusinessTypes;
        /**
        * 是否需要明细
        **/
        6: optional bool detailsShow = false;
        /**
        * 是否过滤非全部退款的优惠券金额
        **/
        7: optional bool filterNotAllRefundCouponAmount = false;
    }

    /**
    * 查询灰度结果
    **/
    struct QueryRefundTradeResponse{
       1: required i32 code;
       2: optional string message;
       3: optional list<QueryRefundTradeResult> data;
    }

    /**
    * 查询退款结果
    **/
    struct QueryRefundTradeResult{
       /**
       * 支付订单号
       **/
       1: required i64 payOrderId;
       /**
        * 查询交易订单号
       **/
       2: required i64 refundOrderId;
       /**
       * 交易金额
        **/
       3: required string amount;
       /**
       * 业务类型
        **/
       4: required string businessType;
       /**
       * 明细数据
       **/
       5: optional RefundResult refundDetails;
       /**
       * 退款成功结果
       **/
       6: optional bool refundSuccess;
      /**
       * 描述
        **/
       7: optional string orderDesc;
    }

    /**
    * 查询订单
    **/
    struct QueryTradeRequest{
        /**
        *  乘客id
        **/
        1: required i64 userId;
        /**
        *  订单id
        **/
        2: required list<i64> orderIdList;
        /**
        * 是否需要明细
        **/
        3: optional bool detailsAmountShow = false;
        /**
        * 是否需要明细
        **/
        4: optional bool detailsProductShow = false;
        /**
        *  是否包含券包数据,默认是false
        **/
        5: optional bool mallContains = false;
        /**
        *  是否包含优惠券数据,默认是false
        **/
        6: optional bool detailsCouponShow = false;
    }

    /**
    * 查询订单
    **/
    struct QueryTradeByMajorProductIdRequest{
        /**
        *  乘客id
        **/
        1: required i64 userId;
        /**
        *  业务侧id
        **/
        2: required string majorProductId;
        /**
        *  业务类型
        **/
        3: optional string businessType;
        /**
        *  交易类型
        *  @see com.didapinche.trade.thrift.enums.TradeOrderTypeEnum
        **/
        4: optional string tradeType;
        /**
        *  交易状态
        *  @see com.didapinche.trade.thrift.enums.TradeOrderStatusEnum
        **/
        5: optional string status;
        /**
        *  路由id，没有得换根据majorProductId路由
        **/
        6: optional string routingKey;
        /**
        * 是否需要明细
        **/
        7: optional bool detailsAmountShow = false;
        /**
        * 是否需要明细
        **/
        8: optional bool detailsProductShow = false;
        /**
        *  是否包含券包数据,默认是false
        **/
        9: optional bool mallContains = false;
    }

    /**
    * 查询灰度结果
    **/
    struct QueryTradeResponse{
       1: required i32 code;
       2: optional string message;
       3: optional list<QueryTradeResult> data;
    }

    /**
    * 查询结果
    **/
    struct QueryTradeResult{
       /**
       * 交易订单号
       **/
       1: required i64 id;
       /**
       * 交易金额
        **/
       3: required string amount;
       /**
       * 业务类型
        **/
       4: required string businessType;
       /**
        * 三方id
       **/
       5: optional string thirdTradeNo;
       /**
       * 明细数据
       **/
       6: optional QueryAmountResult amountDetails;
       /**
         * 商品明细数据
         **/
       7:optional list<QueryProductResult> productDetails;
        /**
        * 交易类型
        * @See @see com.didapinche.trade.thrift.enums.TradeOrderTypeEnum
        **/
        8:optional string tradeType;
        /**
        * 交易成功时间 yyyyMMddHHmmss
        **/
        9:optional string tradeTime;
        /**
        * 交易状态
        * @see com.didapinche.trade.thrift.enums.TradeOrderStatusEnum
        **/
        10:optional string status;
         /**
        *  用户id
        **/
        11:optional i32 userId;
       /**
       * 外部订单号
       **/
       12:optional string majorProductId;
       /**
        * 是否是先乘后付订单
        **/
       13:optional bool isPauOrder = false;
       /**
       * 先乘后付是否扣款成功
       **/
       14:optional bool isPauCutSuccess = false;
       /**
       * 先乘后付扣款成功时间
       **/
       15:optional string pauCutTime;
       /**
       *  先乘后付扣款状态,老系统的状态值0,1,2,3,4
       **/
       16:optional string pauCutStatus;
       /**
        *  原支付交易订单号
        **/
        17:optional i64 payTradeNo;
        /**
       * 实际扣款金额
       **/
       18: optional string pauCutAmount;
       /**
       *  支付来源
       **/
       19: optional string source;
       /**
       *  是否挂起
       **/
       20: optional bool holdStatus;
       /**
       *  订单组别
       **/
       21: optional i32 sysOrderGroup;
       /**
       *  商户号
       **/
       22: optional string merchantId;
       /**
       *  企业付信息
       **/
       23: optional TEnterpriseDetails enterpriseDetails;
       /**
       *  创建时间
       **/
       24: optional string createTime;
      /**
       *  优惠券信息
       **/
       25: optional TCouponDetails couponDetails;
       /**
        * 是否是运力外输先乘后付订单
        **/
       26:optional bool isEnterpriseVipPauOrder = false;
    }

    /**
    * 查询支付内容
    **/
    struct QueryPayInfoCarpoolRequest{
        /**
        *  乘客id
        **/
        1: required i64 userId;
        /**
        *  行程id
        **/
        2: required i64 rideId;
        /**
        *  支付订单id
        **/
        3: required i64 orderId;
        /**
        * 是否是新订单系统支付 默认false
        **/
        4: required bool tradeOrder = false;
    }

    /**
    * 查询支付内容结果
    **/
    struct QueryPayInfoCarpoolResponse{
       1: required i32 code;
       2: optional string message;
       3: optional QueryPayInfoCarpoolResult data;
    }

    /**
    * 查询支付内容结果
    **/
    struct QueryPayInfoCarpoolResult{
       /**
       * 支付金额明细数据
       **/
       1: optional QueryAmountResult amountDetails;
       /**
       * 折扣金额
        **/
       2: optional string discount;
       /**
       * 是否支付过
        **/
       3: optional bool pay = false;
    }

    /**
     * 查询支付响应结果数据
     **/
    struct QueryAmountResult{
        /**
        *  优惠券id (注意：只有优惠券退回才会返回当前字段，部分退款场景可能优惠券会有抵扣金额，但是不会退优惠券) (元)
        **/
        1: optional i64 couponId;
        /**
        *  三方退款渠道，third > 0 返回
        **/
        2: optional string thirdChannel;
        /**
        *  总金额(元)
        **/
        3: optional string totalPrice;
        /**
        *  优惠券金额(元)
        **/
        4: optional string coupon;
        /**
        *  第三方金额(元)
        **/
        5: optional string third;
        /**
        *  乘客可提现账户金额(元)
        **/
        6: optional string passenger;
        /**
        *  乘客不可体现账户金额(元)
        **/
        7: optional string passengerNotWithdraw;
        /**
        * 顺风金金额(元)
        **/
        8: optional string bonus;
        /**
        * 出租车api企业(元)
        **/
        9: optional string tEnterpriseVip;
         /**
        * 顺风车api企业(元)
        **/
        10: optional string cEnterpriseVip;
        /**
       * 出租车非api企业 (元)
       **/
        11: optional string tEnterprise;
        /**
       * 顺风金成本回收金额
       **/
        12: optional string bonusRecover;
        /**
       * 优惠券成本回收金额
       **/
        13: optional string couponRecover;
        /**
        * 平台给乘客的补贴金额，单位元
        **/
        14: optional string passengerSubsidyAmount;
        /**
        * 顺风车车主（元）
        **/
        15: optional string driver;
        /**
        * 出租车司机（元）
        **/
        16: optional string taxi;
         /**
        * 三方优惠金额（元）
        **/
        17: optional string discountAmount;
        /**
        * 支付方式子类型
        **/
        18: optional string thirdSubType;
    }

    /**
     * 商品信息
     **/
    struct QueryProductResult{
        /**
        *  商品信息
        **/
        1: optional string type;
        /**
        *  商品信息
        **/
        2: optional string productId;
        /**
        *  价格 (元)
        **/
        3: optional string price;

    }

    /**
    * 查询灰度开关
    **/
    struct GraySwitch{
       /**
        * 组别（支付类型的需要获取白名单和灰度）
        */
       1: required string group;

       /**
        * 业务类型
        */
       2: required string businessType;

       /**
        * 业务渠道来源
        */
       3: required string source;

       /**
        * 灰度开关 （shut_off 强制走旧逻辑，turn_on根据实际情况判断新旧逻辑
        */
       4: required string switchType;

       /**
        * 白名单以€分割(支付业务使用)
        */
       5: optional string whiteList;

       /**
        * 灰度百分比(支付业务使用)
        */
       6: optional i32 percentage;

       /**
       * 描述
       */
      7: optional string comment;
    }

    /**
    * 退差价查询结果
    **/
    struct QueryPriceDifferencesRefundTradeResponse{
       1: required i32 code;
       2: optional string message;
       3: optional QueryPriceDifferencesRefundTradeResult data;
    }

    /**
    * 退差价明细
    **/
    struct QueryPriceDifferencesRefundTradeResult{
       1: required string payPaymentAmount;
       2: required string payMoneyAmount;
       3: required string payCashAmount;
       4: required string payNoCashAmount;
       5: required string payBonusAmount;
       6: required string payCouponAmount;
       7: required string needRefundPaymentAmount;
       8: required string needRefundMoneyAmount;
       9: required string needRefundCashAmount;
       10: required string needRefundNoCashAmount;
       11: required string needRefundBonusAmount;
       12: required string needRefundCouponAmount;
        /**
        * 平台给乘客的补贴金额，单位元
        **/
       13: optional string payPassengerSubsidyAmount;
       14: optional string needRefundPassengerSubsidyAmount;
    }

    /**
    * 顺风车查询大额优惠券产生的额外支付单
    **/
    struct QueryBigCouponOtherCarpoolTradeOrderRequest{
        /**
        *  乘客id
        **/
        1: required i64 userId;
        /**
        *  行程id
        **/
        2: required i64 rideId;
    }

    /**
    * 查询支付内容结果
    **/
    struct QueryBigCouponOtherCarpoolTradeOrderResponse{
       1: required i32 code;
       2: optional string message;
       3: optional QueryBigCouponOtherCarpoolTradeOrderResult data;
    }

    /**
    * 查询支付内容结果
    **/
    struct QueryBigCouponOtherCarpoolTradeOrderResult{
        /**
         * 支付单ids
         */
        1:optional list<i64> orderIdList;
    }

    /**
    * 垫付请求参数
    **/
    struct PayAdvanceRequest{
        1:required i64 passengerId;//乘客ID
        2:required i64 driverId;//司机ID
        3:required i64 taxiRideId;//行程ID
        4:required string advanceAmount;//垫付金额,两位小数的字符串金额
    }

    /**
    * 垫付请求结果
    **/
    struct PayAdvanceReponse{
       1: required i32 code;
       2: optional string message;
    }

    /**
    * 出租车扫码支付请求参数
    **/
    struct ScanPayRequest{
        1: required string driverCid;//司机cid
        2: required string money;//金额
        3: optional string openId;//行程ID
        4: optional string appidType;//
        5: optional string paymentChannelId;//支付渠道
        6: optional string passengerCid;//userCid
        /**
        * ddc
        **/
        7: optional string ddcInfo;
    }


    /**
    * 出租车扫码支付请求结果
    **/
    struct ScanPayReponse{
       1: required i32 code;
       2: optional string message;
       /**
       * 统一下单签名数据
       **/
       3: optional string unifiedOrderResult;
    }

    /**
    * 出租车充值支付请求参数
    **/
    struct RechargeRequest{
        1: required string price;//金额
        2: optional string paymentChannelId;//支付渠道
        3: optional string driverCid;//userCid
    }

    /**
    * 出租车充值支付请求结果
    **/
    struct RechargeReponse{
       1: required i32 code;
       2: optional string message;
       /**
       * 统一下单签名数据
       **/
       3: optional string unifiedOrderResult;
    }

    struct ScanPayResultReponse{
       1: required i32 code;
       2: optional string message;
       /**
       * 支付状态结果:success表示成功
       **/
       3: optional string status;
    }

    struct RechargePayResultReponse{
       1: required i32 code;
       2: optional string message;
       /**
       * 支付状态结果:success表示成功
       **/
       3: optional string status;
    }

    struct QueryTableSuffixReponse{
       1: required i32 code;
       2: optional string message;
       /**
       * 表后缀
       **/
       3: optional i32 suffix;
    }

    struct RechargeChargeInfoResponse{
       /**
       * 欠款充值信息
       **/
       1: required string data;
    }

    struct ZhifufenGrayResponse{
       1: required i32 code;
       2: optional string message;
       /**
       * 是否灰度
       **/
       3: optional bool gray;
    }

    /**
    * 支付成功回调请求参数
    **/
    struct PaymentSuccessCallbackRequest{
        /**
        * 支付订单号 order_id
        **/
        1: optional string payTradeNo;
        /**
        * 支付中台商家订单号
        **/
        2: optional string orderNo;
        /**
        * 用户id
        **/
        3: optional i64 userId;
        /**
        * 第三方支付单号
        **/
        4: optional string thirdpartyOrderNo;
        /**
        * 支付金额
        **/
        5: optional string paymentAmount;
        /**
        * 实际支付金额
        **/
        6: optional string actualPaymentAmount;
        /**
        * 用户在第三方的支付用户id
        **/
        7: optional string thirdpartyUserId;
        /**
        * 拓展参数 map
        **/
        8: optional string paymentExtra;
        /**
        * 支付时间  yyyy-MM-dd HH:mm:ss
        **/
        9: optional string payTime;
        /**
        * 商户号
        **/
        10:optional string merchantId;
        /**
        * 子商户号
        **/
        11:optional string subMerchantId;
        /**
        * 先乘后付标识 =1 普通订单或者先乘后付下单成功通知 =2 先乘后付支付成功通知
        **/
        12:optional string zhimaOrderFlag;
        /**
        * 同payTradeNo一致支付订单号 order_id
        **/
        13:optional string tradeNo;
        /**
        * 订单类型 1.vip腾讯出行订单
        **/
        14:optional string orderType;
    }

    /**
    * 支付成功回调响应参数
    **/
    struct PaymentSuccessCallbackResponse{
       1: required i32 code;
       2: optional string message;
       3: optional PaymentSuccessCallbackResult data;
    }
    /**
     * 支付回调响应结果数据
     **/
    struct PaymentSuccessCallbackResult{

    }

    struct ZhifufenEnableResponse{
       1: required i32 code;
       2: optional string message;
       /**
       * 是否使能支付分
       **/
       3: optional bool enable;
    }

    /**
    * 支付风控请求校验
    **/
    struct PayRiskRequest{
        1: optional i64 userId;
        /**
        * 例如，微信的openid
        **/
        2: optional string thirdpartyUserId;
        /**
        * 支付渠道：weixin/alipay
        **/
        3: optional string payChannel;
        /**
        * 支付产品：pau,表示先享后付，不传表示普通支付
        **/
        4: optional string payProduct;
    }

    struct PayRiskResponse{
        1: required i32 code;
        2: optional string message;
        3: optional bool hasRisk;
        4: optional string riskMessage;
    }

    /**
    * 查询退款订单
    **/
    struct QueryRefundTradeByOrderIdRequest{
        /**
        *  乘客id
        **/
        1: required i64 userId;
        /**
        *  支付订单id
        **/
        2: required i64 orderId;
    }

    /**
    * 查询退款结果
    **/
    struct QueryRefundTradeByOrderIdResponse{
       1: required i32 code;
       2: optional string message;
       3: optional QueryRefundTradeByOrderIdResult data;
    }

    /**
    * 查询退款结果
    **/
    struct QueryRefundTradeByOrderIdResult{
       /**
        * 是否全部退款
       **/
       1: required bool allRefund;
    }

    /**
    * 查询先乘后付灰度
    **/
    struct QueryPauGrayRequest{
        /**
        *  用户id
        **/
        1: optional i64 userId;
        /**
        *  用户cid
        **/
        2: optional string userCid;
        /**
        *  渠道
        *  @see com.didapinche.trade.thrift.enums.TChannelEnum
        **/
        3: optional string channel;
        /**
        *  是否是小程序
        **/
        4: optional bool minipg = false;
    }

    struct QueryPauGrayResponse{
       1: required i32 code;
       2: optional string message;
       /**
       * 是否灰度
       **/
       3: optional bool gray;
    }

    struct TCheckHighwaySubsidyConditionReq {
        1:required i64 userId;
        2:required i64 startTime;
        3:required i64 endTime;
        /**
        * 实验组名称，如果不传，就是默认组（兼容线上逻辑）
        **/
        4:optional string expGroup;
    }

    struct THighwaySubsidyCondition{
        1: required i32 totalCount;
        2: required i32 usedCount;
        3: required i32 totalMoney;
        4: required i32 usedMoney;
        5: required bool canUseFlag;
    }
    struct TCheckHighwaySubsidyConditionRsp {
        1:required i32 code;
        2:optional string message;
        3:optional THighwaySubsidyCondition condition;
    }

    struct TCancelFreepayReq {
        1:required i64 userId;//用户ID
        2:required string thirdPartyChannelName;//三方渠道
        3:required string deviceId;//设备ID
    }

    struct TCancelFreepayResponse {
        1:required i32 code;
        2:optional string message;
        3:optional string result;//返回结果，序列号的字符串
    }

    struct QueryConfirmUserProfitRequest {
        /**
        *  用户id
        **/
        1: optional i32 userId;
        /**
        *  用户cid
        **/
        2: optional string userCid;
        /**
        * 设备类型
        **/
        3: optional string deviceType;
        /**
        * 设备号
        **/
        4: optional string deviceId;
        /**
        * 身份
        **/
        5: optional string identifier;
        /**
        * 查询来源
        **/
        6: optional i32 querySource;
        /**
        * 三方金额
        **/
        7: optional string thirdPrice;
    }

    struct QueryConfirmUserProfitResponse{
        1: optional i32 code;
        2: optional string message;
        3: optional RtaInfoResult data;
    }

    struct RtaInfoResult {
        /**
         * 投放的Tag列表
         **/
        1: optional list<string> rtaInfoList;

        /**
         * 选中的tag
         **/
        2: optional string tag;

        /**
        * 投放的银⾏信息
        **/
        3: optional BankInfo bankInfo;

        /**
         * 内部优惠顺风车首页展示
         **/
        4: optional string carpoolCouponDealsContent;

         /**
         * 首页优惠券类型，有优惠券返回优惠券，只有顺风金的话返回顺风金
         **/
        5: optional string carpoolDeductionType;

    }

    struct BankInfo {
        /**
         * 银⾏logo图⽚链接
         **/
        1: optional string banklogo;
        /**
        * 银⾏卡名称
        **/
        2: optional string bankCardName;
        /**
        * 曝光⽂案
        **/
        3: optional string exposeText;
        /**
        * 门槛金额
        **/
        4: optional string overAmount;
        /**
        * 立减金额
        **/
        5: optional string reductionAmount;

    }

    struct QueryUserProfitByRideInfoRequest {
        1: optional string userCid;
        2: optional string suggestPrice;
        3: optional string multiPrice;
        4: optional string cosyFen;
        5: optional string singlePrice;
        6: optional string ecoSuggestPrice;
        7: optional string ecoMultiPrice;
        8: optional string ecoCosyFen;
        9: optional string ecoSinglePrice;
        10: optional string deviceType;
        11: optional string deviceId;
        12: optional string ip;
        13: optional string startLongitude;
        14: optional string startLatitude;
        15: optional string endLongitude;
        16: optional string endLatitude;
        17: optional string planStartTime;
        18: optional string innerRideType;
        19: optional string identifier;
        20: optional string distanceType;
        /**
        * 拼团价格
        **/
        21: optional string groupFen;
        /**
        * 站点拼车价
        **/
        22: optional string stationPrice;
    }

    struct EcoPriceEntity {
        1: optional string couponPrice;
        2: optional string couponPriceMulti;
        3: optional string couponPriceCosy;
        4: optional string couponPriceSingle;
        5: optional string couponPriceStation;
    }

    struct QueryUserProfitByRideInfoResponse {
        1: required i32 code;
        2: optional string message;
        3: optional string exposeText;
        4: optional string overAmount;
        5: optional string deductionAmount;
        6: optional string couponPrice;
        7: optional string couponPriceMulti;
        8: optional string couponPriceCosy;
        9: optional string couponPriceSingle;
        10: optional EcoPriceEntity ecoPriceEntity;
        /**
        * 拼团优惠券价格
        **/
        11: optional string couponPriceGroup;
        12: optional string couponPriceStation;
    }


    /**
    * 查询入账中金额
    **/
    struct QueryBillingIncomeRequest{
        /**
        *  用户id
        **/
        1: optional i64 userId;
    }

    struct QueryBillingIncomeResponse{
       1: required i32 code;
       2: optional string message;
       /**
       * 金额 单位元
       **/
       3: optional string amount;
    }

    /**
    * 更新入账中金额
    **/
    struct UpdateBillingIncomeRequest{
        /**
        *  用户id
        **/
        1: optional i64 userId;
        /**
        *  操作类型 init/add/sub
        **/
        2: optional string actionType;
        /**
        *  更新金额 单位元
        **/
        3: optional string updateAmount;
        /**
        *  幂等唯一key 有订单id用订单id
        **/
        4: optional string uniqueKey;
        /**
        *  操作业务类型 支付后发起到账：startTransfer 到账后扣除 endTransfer
        **/
        5: optional string actionBusinessType;
    }

    /**
    * 更新入账中金额响应
    **/
    struct UpdateBillingIncomeResponse{
       1: required i32 code;
       2: optional string message;
       3: optional UpdateBillingIncomeResult data;
    }

    /**
    * 更新入账中金额结果
    **/
    struct UpdateBillingIncomeResult{
       /**
       * 更新前金额 单位元
       **/
       1: optional string orgAmount;
       /**
       * 更新后金额 单位元
       **/
       2: optional string nowAmount;
    }



    /**
    * 查询到账风控
    **/
    struct QueryTaxiTransferRiskRequest{
        /**
        *  用户id
        **/
        1: optional i64 driverId;
        /**
        *  行程id
        **/
        2: optional i64 rideId;
    }

    struct QueryTaxiTransferRiskResponse{
       1: required i32 code;
       2: optional string message;
       /**
       * forbid
       **/
       3: optional string forbidValue;
       /**
       * delay
       **/
       4: optional string delayValue;
    }
    /**
    * 更新到账风控
    **/
    struct UpdateTaxiTransferRiskRequest{
        /**
        *  用户id
        **/
        1: optional i64 driverId;
        /**
        *  行程id
        **/
        2: optional i64 rideId;
        /**
        *  forbid
        **/
        3: optional i32 forbidValue;
        /**
        *  delay
        **/
        4: optional i32 delayValue;
    }

    /**
    * 更新到账风控响应
    **/
    struct UpdateTaxiTransferRiskResponse{
       1: required i32 code;
       2: optional string message;
       3: optional UpdateTaxiTransferRiskResult data;
    }

    /**
    * 更新到账风控结果
    **/
    struct UpdateTaxiTransferRiskResult{
       /**
       * 更新前Forbid
       **/
       1: optional i32 orgForbidValue;
       /**
       * 更新后Forbid
       **/
       2: optional i32 nowForbidValue;
       /**
       * 更新前Delay
       **/
       3: optional i32 orgDelayValue;
       /**
       * 更新后Delay
       **/
       4: optional i32 nowDelayValue;
    }

    /**
    * 查询支付坐标
    **/
    struct QueryTaxiUpdateCoordRequest{
        /**
        *  行程id
        **/
        1: optional i64 taxiRideId;
    }

    struct QueryTaxiUpdateCoordResponse{
       1: required i32 code;
       2: optional string message;
       /**
       * 值
       **/
       3: optional string value;
    }
    /**
    * 更新支付坐标
    **/
    struct UpdateTaxiUpdateCoordRequest{
        /**
        *  行程id
        **/
        1: optional i64 taxiRideId;
        /**
        *  值
        **/
        2: optional string value;
        /**
        *  缓存时间 秒 不送则默认600s
        **/
        3: optional i32 seconds;
    }

    /**
    * 更新支付坐标响应
    **/
    struct UpdateTaxiUpdateCoordResponse{
       1: required i32 code;
       2: optional string message;
       3: optional UpdateTaxiUpdateCoordResult data;
    }

    /**
    * 更新支付坐标结果
    **/
    struct UpdateTaxiUpdateCoordResult{
       /**
       * 更新前值
       **/
       1: optional string orgValue;
       /**
       * 更新后值
       **/
       2: optional string nowValue;
    }


    /**
    * 查询订单支付情况
    **/
    struct QueryActualOrderCutResponse{
       1: required i32 code;
       2: optional string message;
       3: optional string orderCutScene; //paid:已支付，unpaid:待扣款，old:旧订单
    }

    /**
    * 查询订单支付情况
    **/
    struct QueryActualOrderCutRequest{
        /**
        *  乘客id
        **/
        1: required i64 userId;
        /**
        *  订单id
        **/
        2: required i64 orderId;
    }


struct PriceRequest{
    /**
    * 拼车1+1 未拼成价:suggest_price
    * 拼车1+1 拼成价:multi_price
    * 优享1+1 价格:cosy_price
    * 独享价  single_price
    * 针对电，增加eco_前缀
    **/
    1:optional string priceType;
    2:optional string originPrice;
    3:optional string couponPrice;
 }

 struct SubsidyResult{
    1:optional string priceType;
    2:optional string subsidyPrice;
 }

 struct QuerySubsidyFromAIRequest{
    /**
    * 用户id
    **/
    1:optional i32 userId;
    /**
    * 起点经度
    **/
    2:optional string startLongitude;
    /**
    * 起点纬度
    **/
    3:optional string startLatitude;
    /**
    * 终点经度
    **/
    4:optional string endLongitude;
    /**
    * 终点纬度
    **/
    5:optional string endLatitude;
    /**
    * 出发时间
    **/
    6:optional string planStartTime;
    /**
    * 行程类型
    **/
    7:optional string rideType;
    /**
    * 1市内，2城际
    **/
    8:optional string cityType;
    /**
    * ip:优惠券服务使用
    **/
    9:optional string ip;
    /**
    * ddc info
    **/
    10:optional string ddcInfo;
    /**
    * 价格列表
    **/
    11:optional list<PriceRequest> priceRequest;
 }

 struct QuerySubsidyFromAIResponse{
       1: required i32 code;
       2: optional string message;
       3: optional list<SubsidyResult> subsidyResults;
 }


/**
* 查询计算顺风金抵扣金额
**/
struct QueryBonusCalculateAmountRequest{
    /**
    *  行程id
    **/
    1: optional i64 rideId;
    /**
    *  用户id
    **/
    2: optional i64 userId;
    /**
    *  行程单价 除去感谢费的价格
    **/
    3: optional string suggestPrice;
    /**
    *  使用优惠券金额
    **/
    4: optional string couponAmount;
    /**
    *  使用账户金额
    **/
    5: optional string balanceAmount;
}

struct QueryBonusCalculateAmountResponse{
   1: required i32 code;
   2: optional string message;
   /**
   * 使用顺风金金额
   **/
   3: optional string bonusAmount;
}

struct TradeQueryCommonResponse {
    1: required i32 code;
    2: optional string message;
    3: optional string ret; //通用结果
}

struct TradeCommonResponse {
    1: required i32 code;
    2: optional string message;
    3: optional string ret; //通用结果
}

/**
* 根据行程号查询订单流水，包含支付未支付
**/
    struct QueryPayInfoRequest{
        /**
        *  乘客id
        **/
        1: optional i64 userId;
        /**
        *  订单id
        **/
        2: optional string majorProductId;
        /**
        * 交易类型
        **/
        3: optional string tradeType;
        /**
        * 业务类型
        **/
        4: optional string businessType;
    }

    struct TTradeOrderModel{
       /**
        * 支付id
        **/
       1: optional string id;
       /**
       * 关联业务id
        **/
       2: optional string majorProductId;
       /**
        * 状态
        **/
       3: required string status;
       /**
        * 金额
       **/
       4: optional string totalPrice;
       /**
       * 支付时间
       **/
       5: optional string successTime;
    }

    struct PayInfoResult {
       1: optional list<TTradeOrderModel> payList;
    }

    struct QueryPayInfoResponse {
        1: optional i32 code;
        2: optional string message;
        3: optional PayInfoResult data;
    }


/**
* 更新支付坐标
**/
struct SaveTradeInfoToRedisRequest{
    /**
    * 用户id
    **/
    1:optional i64 userId;
    /**
    * 主产品id
    **/
    2:optional string majorProductId;
    /**
    *  goodsId
    **/
    3: optional string taxiGoodsInfo;
    /**
    *  价格
    **/
    4: optional string taxiSinglePrice;
}

/**
* 保存交易信息响应
**/
struct SaveTradeInfoToRedisResponse{
   1: required i32 code;
   2: optional string message;
   3: optional SaveTradeInfoToRedisResult data;
}

/**
* 保存交易信息结果
**/
struct SaveTradeInfoToRedisResult{
}


/**
 * 查询智慧码订单请求参数
 **/
struct QueryQRTaxiOrderDetailListRequest{
    /**
    * 用户cid
    **/
    1:optional string userCid;
    /**
    * 状态 区分入账中和已入账
    **/
    2:optional i32 statusType;
    /**
    * 日期
    **/
    3:optional string date;
    /**
    * 开始时间
    **/
    4:optional string startTime;
    /**
    * 结束时间
    **/
    5:optional string endTime;
    /**
     * 交易类型 区分西安银行和其他智慧码收单
     */
    6:optional i32 tradeType;
    /**
    * 页码
    **/
    7:optional i32 page;
    /**
    * pageSize
    **/
    8:optional i32 pageSize;
}
/**
 * 查询智慧码订单响应结果
 **/
struct QueryQRTaxiOrderDetailListResponse{
    1: required i32 code;
    2: optional string message;
    3: optional QueryQRTaxiOrderDetailListResult data;
}
/**
 * 查询智慧码订单响应数据
 **/
struct QueryQRTaxiOrderDetailListResult{
    /**
    *  总金额
    **/
    1: optional string totalAmount;
    /**
    *  总金额
    **/
    2: optional list<OrderDetail> orderDetailList;
}

/**
 * 查询智慧码订单响应数据
 **/
struct OrderDetail{
    /**
    *  产品id
    **/
    1: optional string majorProductId;
    /**
    *  支付id
    **/
    2: optional i64 orderId;
    /**
    *  交易金额
    **/
    3: optional string money;
    /**
    *  订单状态
    **/
    4: optional i32 orderStatus;
    /**
    *  订单支付时间 yyyy-MM-dd HH:mm:ss
    **/
    5: optional string payTime;
}


/**
* 查询未到账订单list请求
**/
struct QueryNotTransferOrderListRequest{
   /**
   * 行程信息list
   **/
   1:optional list<RideInfo> rideInfoList;
}

/**
* 行程信息
**/
struct RideInfo{
   /**
   * 用户id
   **/
   1:optional i64 userId;
   /**
   * 司机id
   **/
   2:optional i64 driverId;
   /**
   * 行程id
   **/
   3:optional i64 rideId;
   /**
   * 支付单id
   **/
   4:optional i64 orderId;
}

/**
* 查询未到账订单list响应
**/
struct QueryNotTransferOrderListResponse{
      1: required i32 code;
      2: optional string message;
      /**
      * 未到账订单行程id集合
      **/
      3: optional list<string> rideIdList;
}

struct RefundCompensationRequest{
    /**
    * 开始时间，格式：yyyy-MM-dd HH:mm:ss，必传
    **/
    1:optional string startTime;
    /**
    * 结束时间，格式：yyyy-MM-dd HH:mm:ss，必传
    **/
    2:optional string endTime;
    /**
    * 幂等用的id，必传
    **/
    3:optional string idempotentId;
}

struct RefundCompensationResponse{
   1: required i32 code;
   2: optional string message;
}

struct PauOrderProductInfo{
   1: required i32 code;
   2: optional string message;
   3: optional string majorProductId;
   4: optional string needPayMoney;
   5: optional string businessType;
}


/**
* 查询扫码信息请求参数
**/
struct QueryScanTradeOrderListRequest{
    /**
    * 司机cid
    **/
    1:optional string driverCid;
    /**
    * 日期 20230911
    **/
    5:optional string date;
}

/**
* 查询扫码信息响应
**/
struct QueryScanTradeOrderListResponse{
   1: required i32 code;
   2: optional string message;
   3: optional QueryScanTradeOrderListResult result;
}

/**
* 扫码信息结果
**/
struct QueryScanTradeOrderListResult{
    /**
    * 总金额
    **/
    1: optional string totalMoney;
    /**
    * 扫码信息
    **/
    2: optional list<TScanOrdersInfo> scanOrdersInfo;
}

/**
* 扫码信息
**/
struct TScanOrdersInfo{
    /**
    * 支付宝收款，微信收款
    **/
    1: optional string channelInfo;
    /**
    * (9:30,10:20)
    **/
    2: optional string scanTime;
    /**
    * 扫码支付的钱，保留两位小数
    **/
    3: optional string scanMoney;
    /**
    * 司机ID
    */
    4: optional i64 driverId;
    /**
    * 乘客ID
    */
    5: optional i64 passengerId;
    /**
    * 渠道
    */
    6: optional string thirdChannel;
    /**
    * 交易号
    */
    7: optional string thirdTradeNo;
    /**
    * 交易成功时间 yyyy-mm-dd HH:mm:ss
    */
    8: optional string successTime;
}

/**
* 路由参数
**/
struct RoutParam{
    /**
    * 司机cid
    **/
    1:optional string driverCid;
    /**
    * 路由id
    **/
    2:optional string routId;
    /**
    * 路由首选使用cid. false则代表使用routId.driverCid和routId不可都为空  默认true
    **/
    3:optional bool routByCid = true;
}

/**
* remittance_order查询
**/
struct QueryRemittanceOrderByProductIdRequest{
    /**
    *  业务侧id
    **/
    1: required string productId;
    /**
    *  业务类型
    **/
    2: required string businessType;
    /**
    * 是否需要明细 note
    **/
    3: optional bool noteShow = false;
    /**
    * 是否需要备注 comment
    **/
    4: optional bool commentShow = false;

}

struct QueryRemittanceOrderResponse{
   /**
    *  表id
    **/
   1: required i64 id;
   /**
   *  业务侧id
   **/
   2: optional string productId;
   /**
   *  业务类型
   **/
   3: optional string businessType;
   /**
   * 原支付交易订单号
   **/
   4: optional i64 payTradeNo;
   /**
   * 支付人ID
   **/
   5: optional i32 payUserId;
   /**
   * 收取人ID
   **/
   6: optional i32 incomeUserId;
   /**
   * 操作总金额
   **/
   7: optional string sumFee;
   /**
   *  汇款总金额
   **/
   8: optional string payFee;
   /**
   *  收取总金额
   **/
   9: optional string incomeFee;
   /**
   * 创建时间 yyyyMMddHHmmss
    **/
   10: optional string createTime;
   /**
   * 备注
    **/
   11: optional string comment;
   /**
   * 扩展字段  todo 有需要的地方需要定义thrift对象关联
   **/
   12: optional string note;
   13: required i32 code;
   14: optional string message;
}

/**
 * 修改交易数据参数
 **/
struct TChangeTradeInfoRequest {
    /**
    * 用户id
    **/
    1:optional i64 userId;
    /**
    * 主产品id
    **/
    2:optional string majorProductId;
    /**
    * 支付订单id
    **/
    3:optional i64 tradeOrderId;
    /**
    * 增量操作的订单购买产品组成
    **/
    4:optional list<TTradeProductDetails> extraFeeInfos;
}


/**
* 修改订单组别请求
**/
struct TSetSysOrderGroupRequest{
    /**
    * 乘客用户id
    **/
    1:optional i64 userId;
    /**
    * 订单id
    **/
    2:optional i64 tradeOrderId;
    /**
    * 业务类型
    * @see com.didapinche.trade.thrift.enums.TBusinessEnum
    **/
    3:optional string businessType;
    /**
    * 操作来源
    * @see com.didapinche.trade.thrift.enums.TOrderSourceEnum
    **/
    4:optional string source;
    /**
    * 老订单组别
    **/
    5:optional i32 oldSysOrderGroup;
    /**
    * 新订单组别
    **/
    6:optional i32 newSysOrderGroup;
    /**
    *  来源 记录操作历史使用
    **/
    7: optional string sourceId;
    /**
    *  操作人 记录操作历史使用
    **/
    8: optional i32 sysOpId;
    /**
    *  备注 记录操作历史使用
    **/
    9: optional string comment;
}

/**
* 修改订单组别响应
**/
struct TSetSysOrderGroupResponse{
   1: required i32 code;
   2: optional string message;
   3: optional TSetSysOrderGroupResult data;
}

/**
* 修改订单组别结果
**/
struct TSetSysOrderGroupResult{
}


/**
* 挂起或取消挂起请求
**/
struct TUpdateHoldOrderStatusRequest{
    /**
    * 乘客用户id
    **/
    1:optional i64 userId;
    /**
    * 主产品id
    **/
    2:optional string majorProductId;
    /**
    * 订单id
    **/
    3:optional i64 tradeOrderId;
    /**
    * 业务类型
    * @see com.didapinche.trade.thrift.enums.TBusinessEnum
    **/
    4:optional string businessType;
    /**
    * 操作来源
    * @see com.didapinche.trade.thrift.enums.TOrderSourceEnum
    **/
    5:optional string source;
    /**
    * ture 代表挂起，false代表取消挂起
    *
    **/
    6:optional bool hold = false;
    /**
    *  来源 记录操作历史使用
    **/
    7: optional string sourceId;
    /**
    *  操作人 记录操作历史使用
    **/
    8: optional i32 sysOpId;
    /**
    *  备注 记录操作历史使用
    **/
    9: optional string comment;
    /**
    *  给司机到账的钱（车费+补贴）单位元
    **/
    10: optional string income;
    /**
    *  司机id 不可为空
    **/
    11: optional i64 driverId;
}

/**
* 挂起或取消挂起响应
**/
struct TUpdateHoldOrderStatusResponse{
   1: required i32 code;
   2: optional string message;
   3: optional TUpdateHoldOrderStatusResult data;
}

/**
* 挂起或取消挂起结果
**/
struct TUpdateHoldOrderStatusResult{
    /**
    * 订单状态
    **/
    1:optional string status;
    /**
    * 支付时间 yyyyMMddHHmmss
    **/
    2:optional string payTime;
}

/**
* 订单作弊并扣款请求
**/
struct TOrderSetTagAndConfiscateRequest{
    /**
    * 乘客用户id
    **/
    1:optional i64 userId;
    /**
    * 主产品id
    **/
    2:optional string majorProductId;
    /**
    * 支付订单id
    **/
    3:optional i64 tradeOrderId;
    /**
    * 业务类型
    * @see com.didapinche.trade.thrift.enums.TBusinessEnum
    **/
    4:optional string businessType;
    /**
    * 订单来源
    * @see com.didapinche.trade.thrift.enums.TOrderSourceEnum
    **/
    5:optional string source;
    /**
    * 订单描述 代替老接口debitContent
    **/
    6:optional string orderDesc;
    /**
    *  操作人 记录操作历史使用
    **/
    7: optional i32 sysOpId;
    /**
    *  扣款类型 代替老接口debitType
    **/
    8: optional i32 confiscateType;
    /**
    *  扣款金额,单位元 代替老接口debitMoney
    **/
    9: optional string confiscateAmount;
}

/**
* 订单作弊并扣款响应
**/
struct TOrderSetTagAndConfiscateResponse{
   1: required i32 code;
   2: optional string message;
   3: optional TOrderSetTagAndConfiscateResult data;
}

/**
* 订单作弊并扣款结果
**/
struct TOrderSetTagAndConfiscateResult{
   /**
   * 扣款id
   **/
   1: optional i64 confiscateOrderId;
   /**
   * 实际扣款金额
   **/
   2: optional string actualConfiscateAmount;
}

/**
* alice扣款发送二清消息请求
**/
struct TSendAliceMqToLiquidationRequest{
    /**
    * 乘客用户id
    **/
    1:optional i64 userId;
    /**
    * 支付订单id
    **/
    2:optional i64 tradeOrderId;
    /**
    * 车主id
    **/
    3:optional i64 driverId;
    /**
    * 没收订单id
    **/
    4:optional i64 confiscateOrderId;
}

/**
* alice扣款发送二清消息响应
**/
struct TSendAliceMqToLiquidationResponse{
   1: required i32 code;
   2: optional string message;
   3: optional TSendAliceMqToLiquidationResult data;
}

/**
* alice扣款发送二清消息结果
**/
struct TSendAliceMqToLiquidationResult{

}




/**
 * 退差价金额查询请求参数
 **/
struct TPriceDifferencesRefundQueryRequest{
    /**
    * 业务侧id
    **/
    1: optional string majorProductId;
    /**
    * 主订单id
    **/
    2: optional i64 majorOrderId;
    /**
    * 需要退款的订单集合
    **/
    3: optional list<i64> orderIdList;
    /**
    *  乘客id
    **/
    4: optional i64 userId;
    /**
    * 业务类型
    * @see com.didapinche.trade.thrift.enums.TBusinessEnum
    **/
    5: optional string businessType;
    /**
    *
    * @see com.didapinche.trade.thrift.enums.TOrderSourceEnum
    **/
    6: optional string source;
    /**
    * 三方+余额抵扣金额 单位元
    **/
    7: optional string paymentRealAmount;
    /**
    * 优惠券抵扣金额 单位元
    **/
    8: optional string couponRealAmount;
    /**
    * 顺风金抵扣金额 单位元
    **/
    9: optional string bonusRealAmount;
    /**
    * 平台给乘客的补贴金额，单位元
    **/
    10:optional string passengerSubsidyRealAmount;
}

/**
* 退差价金额查询响应
**/
struct TPriceDifferencesRefundQueryResponse{
   1: required i32 code;
   2: optional string message;
   3: optional TPriceDifferencesRefundQueryResult data;
}

/**
* 退差价金额查询结果
**/
struct TPriceDifferencesRefundQueryResult{

}


struct TransferToUserDetail{
   /**
    * 用户ID，被赔付的用户
    **/
    1:optional i64 userId;
    /**
     * 行程号
     **/
    2:optional string rideId;
    /**
     * 转账金额,单位元,两位小数
     **/
    3:required string amount;
    /**
     * 转账备注，最多32个字符，超长会被截取
     **/
    4:optional string transferRemark;
    /**
     * 是否平台成本
     **/
    5:optional bool isPlatCost;
}

struct TransferToUserRequest{
   /**
    * 赔付明细
    **/
    1:optional list<TransferToUserDetail> details;
    /**
    * 操作人ID
    **/
    2:optional i32 sysOpId;
    /**
    * 操作人姓名
    **/
    3:optional string sysOpName;
}

struct TransferToUserResponse{
   1: required i32 code;
   2: optional string message;
}


struct TCreateSubTableRequest{
    /**
    * 表名称
    **/
   1: required string table;
   /**
    * 表结构
    **/
   2: optional string tableStruct;
}


struct TCreateSubTableResponse{
   1: required i32 code;
   2: optional string message;
}

struct TQueryOrderThirdpartyInfoRequest{
        /**
        *  乘客id
        **/
        1: required i32 userId;
        /**
        *  支付订单id
        **/
        2: required i64 orderId;
        /**
        * 要到账的车主id或司机id
        **/
        3: optional i32 driverId;
}

struct TQueryOrderThirdpartyInfoResponse {

    1: required i32 code;
    2: optional string message;
    3: optional list<QueryOrderThirdpartyInfo> orderThirdpartyInfoList;
}
struct QueryOrderThirdpartyInfo {
        /**
		* 支付时间
		*/
		1:required string payTime;
		/**
		 * 支付方式
		 */
		2:required string payChannelName;
		/**
		 * 支付金额
		 */
		3:required string payAmount;
		/**
		 * 付款交易号
		 */
		4:required string payThirdNo;
		/**
		 * 类型
		 */
		5:required string type;
		/**
		 * 状态
		 */
		6:required string status;
		/**
         * 先乘后付扣款状态
         */
        7:optional string pauCutStatus;
        /**
         * 支付方式
         */
        8:required string payChannel;
}

struct TQueryOrderActualPaymentSumRequest {
        /**
        *  乘客id
        **/
        1: required i32 userId;
        /**
        * 业务类型
        **/
        2: optional string businessType;
        /**
        * 日期，yyyy-MM-dd
        **/
        3: required string date;
}



/**
* 清洗订单数据请求
**/
struct TCleanOrderDataRequest {
   /**
   *  清洗类型
   *  1.时间区间
   *  2.商品维度
   *  3.用户维度+时间区间
   **/
   1: required i32 type;
   /**
   * 开始时间
   **/
   2: optional string startDate;
   /**
   * 结束日期
   **/
   3: optional string endDate;
   /**
   * 用户id
   **/
   4: optional i64 userId;
   /**
   * 用户cid
   **/
   5: optional string userCid;
   /**
   * 主商品id
   **/
   6: optional string majorProductId;
   /**
   * 业务类型
   **/
   7: optional string businessType;
}

struct TTaxiqrTradeOrderUpdateResponse{
    1: required i32 code;
    2: optional string message;
}

struct TTaxiqrTradeOrderUpdateRequest{
    /**
    *  乘客id
    **/
    1: required i64 userId;
    /**
    *  支付订单id
    **/
    2: required i64 orderId;
    /**
    * 要到账的车主id或司机id
    **/
    3: optional i64 driverId;
}

struct RefundSuccessCallbackRequest{
    1: optional i64 userId;
    2: optional string payTradeNo;
    3: optional string paymentRefundNo;
    4: optional string thirdRefundNo;
    5: optional string refundAmount;
    6: optional string actualRefundAmount;
    /**
    * 毫秒时间戳
    **/
    7: optional i64 refundTime;
    8: optional string refundExtra;
    9: optional string refundTradeNo;
}

struct RefundSuccessCallbackResponse{
    1: optional i32 code;
    2: optional string message;
}


//queryTradeOrderRecord接口参数信息
struct TradeOrderRecordRequest{
    /**
    *  乘客id
    **/
    1: required i64 userId;
    /**
    *  支付订单id
    **/
    2: required i64 orderId;
    /**
    * 行程ID
    **/
    3: optional i64 rideId;
}

struct TradeOrderRecordResponse{
    1: required i32 code;
    2: optional string message;
    3: optional string traceId;
    4: optional TradeOrderRecordData data;
}

struct TradeOrderRecordData{
    1: optional i64 userId;//用户ID
    2: optional i64 orderId;//交易单ID
    3: optional string openId;//三方ID
    4: optional string businessType;//类型类型
    5: optional string tradeType;//交易类型
    6: optional string status;//支付状态
    7: optional string amount;//支付金额
    8: optional string thirdChannel;//支付渠道
    9: optional string xxOrderStatus;//xxx_order状态
    10: optional i64 billId;
    11: optional TradeOrderBillData billData;

}

struct TradeOrderBillData{
    1: optional i64 id;
    2: optional string paymentAmount;//实付金额
    3: optional string refundAmount;//退款金额
    4: optional string receiptsAmount;//车主实收金额
    5: optional string payState;//支付状态
    6: optional string transferState;//到账状态
    7: optional string riskTag;//风控标识
    8: optional string freeTag;//免单标识
    9: optional string pend;//挂起标识
    10: optional string refundState;//退款状态
}


/**
* es操作请求
**/
struct TElasticsearchActionRequest {
   /**
   *  操作类型
   **/
   1: required string actionType;
   /**
   * 交易凭证
   **/
   2: required string auth;
  /**
   * 插入的数据
   **/
   3: optional string data;
   /**
   * 插入的数据
   **/
   4: optional string esId;
}

/**
* 查询bill_order_mapping_in_es 复杂条件
**/
struct TQueryBillOrderMappingInEsByIdRequest {
   /**
   *  id
   **/
   1: optional i64 id;
}

/**
* 查询bill_order_mapping_in_es 组合条件
**/
struct TQueryBillOrderMappingInEsByParamRequest {
   /**
   *  id
   **/
   1: optional i64 id;
   /**
   *  trade_bill的ID
   **/
   2: optional i64 billId;
   /**
   *  trade_order的ID
   **/
   3: optional i64 tradeOrderId;
   /**
   *  业务类型
   **/
   4: optional string businessType;
   /**
   *  交易类型
   **/
   5: optional string tradeType;
   /**
   *  用户ID
   **/
   6: optional i64 userId;
   /**
   *  开始时间
   **/
   7: optional string startTime;
   /**
   *  结束时间
   **/
   8: optional string endTime;
}

//查询车主收入金额
struct DriverIncomRequest{
    1: required i64 driverId;
    2: optional list<i64> orderIdList;
    3: optional string beginDate;
    4: optional string endDate;
}
//查询车主收入金额响应
struct DriverIncomResponse{
    1: required i32 code;

    2: optional string message;
    3: optional string traceId;
    4: optional string amount;
}

struct QueryUserOrderRequest{
    /**
    *  乘客id
    **/
    1: required i64 userId;
    /**
    *  支付订单id
    **/
    2: required i64 orderId;
}

struct QueryUserOrderResponse{
    1: required i32 code;
    2: optional string message;
    3: optional string userOrderJson;
}

struct QueryOrderCouponGoodsInfoRequest{
    /**
    *  乘客id
    **/
    1: optional i64 userId;
    /**
    *  支付订单id
    **/
    2: optional i64 orderId;
    /**
    * 出租车：taxiPay
    * 顺风车：carpoolPay
    **/
    3: optional string sourceCid;
}

struct QueryOrderCouponGoodsInfoResponse{
    1: required i32 code;
    2: optional string message;
    3: optional string goodsTitle;
    4: optional string goodsPrice;
}

struct QueryUserPaymentRequest{
    1:optional i64 userId;
    2:optional list<string> businessTypes;
    3:optional list<string> thirdChannels;
    /**
    * 限制查询的数量
    **/
    4:optional i32 size;
}

struct UserPaymentItem{
   /**
   *  trade_order的ID
   **/
    1: optional i64 tradeOrderId;
    2: optional string majorProductId;
    /**
    * 总金额 单位元 2位小数
    **/
    3: optional string totalPrice;
    /**
    * 业务类型
    * @see com.didapinche.trade.thrift.enums.TBusinessEnum
    **/
    4: optional string businessType;
    /**
    * 支付状态，
    **/
    5: optional string status;
    /**
    * 三方支付渠道
    **/
    7: optional string thirdChannel;
    /**
    * 创建时间 yyyyMMddHHmmss
    **/
    8: optional string createTime;
    /**
    * 成功时间 yyyyMMddHHmmss
    **/
    9: optional string successTime;
}

struct QueryUserPaymentResponse{
    1: required i32 code;
    2: optional string message;
    3: optional list<UserPaymentItem> userPayments;
}

/**
* 查询order
**/
struct TQueryOrderInShardingByParamRequest {
   /**
   *  表名字
   **/
   1: required string tableName;
   /**
   *  支付用户id
   **/
   2: optional list<i64> paymentUserId;
   /**
   *  产品id
   **/
   3: optional list<string> productIds;
   /**
   *  trade_bill的ID
   **/
   4: optional i64 billId;
   /**
   *  trade_order的ID
   **/
   5: optional list<i64> tradeOrderIds;
   /**
   *  业务类型
   **/
   6: optional string businessType;
   /**
   *  交易类型
   **/
   7: optional string tradeType;
   /**
   *  开始时间
   **/
   8: optional string startTime;
   /**
   *  结束时间
   **/
   9: optional string endTime;
}

//查询退款可退金额
struct RefundableDetailsRequest{
    /**
    *  用户id
     **/
    1: required i64 userId;
     /**
     *  订单id
     **/
    2: required i64 orderId;
    /**
    *  补偿金金额（元）
    **/
    3: optional string compensation;
}
//查询退款可退金额相应
struct RefundableDetailsResponse{
    1: required i32 code;
    2: optional string message;
    3: optional RefundableDetails refundableDetails;
}

//查询退款可退金额明细
struct RefundableDetails{
    /**
    *  余额
    **/
    1: optional string balance;
    /**
    *  第三方金额
    **/
    2: optional string thirdMoney;
    /**
    *  支付渠道
    **/
    3: optional string payChannel;
}

struct QuerySavedMoneyRequest {
    1: required i32 userId;
}

struct QuerySavedMoneyResponse {
    1: required i32 code;
    2: optional string message;
    3: optional i32 savedMoney;
}

struct CMSQueryScanTradeOrderListRequest {
    1: optional string scanOrderCid;
    2: optional i64 driverId;
    3: optional string startPayTime;
    4: optional string endPayTime;
    5: optional i32 pageNo;
    6: optional i32 pageSize;
}

struct CMSQueryScanTradeOrderListResponse {
   1: required i32 code;
   2: optional string message;
   3: optional list<TScanOrdersInfo> scanOrdersInfo;
   4: optional i32 totalNum;
}


struct TQuerySumPaymentParamRequest {
        /**
        * 用户userId
        **/
        1:required i64 userId;

        /**
        * 需要查询的月份，格式 yyyyMM
        **/
        2:required string month;
        /**
        *  金额展示应用场景，当前普通场景传值 common （不包含钉钉顺风车订单，出租车三化订单）dingtalk (钉钉顺风车订单)
        **/
        3:required string scene;
}

struct TUpdateTradeAmountDetailReceiptStatusRequest{
    /**
    *  乘客id
    **/
    1: required i64 userId;
    /**
    *  支付订单id-加锁
    **/
    2: required i64 paymentOrderId;
    /**
    *  交易订单id(支付/退款/到账...)
    **/
    3: required i64 orderId;
    /**
    * 支付组成类型
    * @see com.didapinche.trade.thrift.enums.TOrderAccountTypeEnum
    **/
    4: required string amountType;
    /**
    * 回执状态
    * @see com.didapinche.trade.thrift.enums.AmountReceiptStatusEnum
    **/
    5: required string receiptStatus;
    /**
    * 回执时间 yyyy-MM-dd HH:mm:ss receiptStatus=cut_success必输字段
    **/
    6: optional string receiptTime;
    /**
    * 回执ID，下游系统生成的ID
    **/
    7: optional string receiptId;
    /**
    * 三方单号，下游与外部交互时，外部生成的单号
    **/
    8: optional string receiptSn;
    /**
    * 真实退款金额 先乘后付未扣款成功时为0
    **/
    9: optional string actualRefundAmount;
    /**
    * 先乘后付取消操作
    **/
    10: optional bool pauCancelFlag;
}

struct TUpdateTradeAmountDetailReceiptStatusResponse{
    1: required i32 code;
    2: optional string message;
    3: optional TUpdateTradeAmountDetailReceiptStatusResult data;
}

struct TUpdateTradeAmountDetailReceiptStatusResult{
}

struct TradeQueryPayAfterUseRequest{
    /**
    * 用户userId
    **/
   1: required i64 userId;
   /**
   * 是否需要历史订单
   **/
   2: optional bool queryHistoryOrder = false;
   /**
   * 来源
   **/
   3: optional string source;
   /**
   * 行程id
   **/
   4: optional i64 rideId;
   /**
   * 价格列表，用于预估支付优惠，结构体中的couponPrice可不用传递，支付根据rideInfo重新获取
   **/
   5:optional list<PriceRequest> priceRequest;
   /**
   * 行程信息，用于查询优惠券
   **/
   6:optional PauRideInfo rideInfo;
   /**
   * ddc info,必传
   **/
   7:optional string ddcInfo;
}

struct PauRideInfo{
    /**
    * 起点经度
    **/
    1:optional string startLongitude;
    /**
    * 起点纬度
    **/
    2:optional string startLatitude;
    /**
    * 终点经度
    **/
    3:optional string endLongitude;
    /**
    * 终点纬度
    **/
    4:optional string endLatitude;
    /**
    * 出发时间
    **/
    5:optional string planStartTime;
    /**
    * 行程类型
    **/
    6:optional string rideType;
    /**
    * 1市内，2城际
    **/
    7:optional string cityType;
}

struct TradeQueryPayAfterUseResponse{
    1: required i32 code;
    2: optional string message;
    /**
    * 先享后付开通渠道，weixin/alipay，有值开通，无值表示未开通，如果都开通，会返回一个默认的
    **/
    3: optional string channel;
    /**
     * 是否存在未扣款成功的单
     **/
    4: optional bool exitsNotCutSuccessOrder = false;
    /**
     * 是否走新订单系统
     **/
    5: optional bool useTrade = false;
    /**
    * 进行中的的productIds
    **/
    6: optional list<string> notCutSuccessProductIdList;
}

struct TradePayChannelDiscountInfo{
    /**
    * 优惠的支付渠道
    **/
    1: optional string payDiscountChannel;
    /**
    * 优惠的支付金额，单位：元
    **/
    2: optional string payDiscountAmount;
    /**
    * 支付优惠文案
    **/
    3: optional string payDiscountText;
    /**
    * 价格类型
    **/
    4: optional string priceType;
}

struct TradePayChannelDiscountInfoRequest{
    /**
    * 用户userId
    **/
    1: required i64 userId;
    /**
    * 场景：首页（无价格）、询价（含有部分价格）、支付（含有部分价格）、收银台（含有确定价格）
    **/
    2:optional string scene;
    /**
    * 价格列表，用于请求三方获取优惠
    **/
    3:optional list<PriceRequest> priceRequest;
    /**
    * ddc info,必传
    **/
    4:optional string ddcInfo;
    /**
    * 接口调用来源
    **/
    5:optional string source;
}

struct TradePayChannelDiscountInfoResponse{
    1: required i32 code;
    2: optional string message;
    /**
    * 就是入参的场景参数
    *
    * 场景：首页（无价格）、询价（含有部分价格）、支付（含有部分价格）、收银台（含有确定价格）
    **/
    3: optional string scene;
    /**
    * 支付渠道优惠信息
    **/
    4: optional list<TradePayChannelDiscountInfo> discountInfoList;
    /**
    * 建议使用的的支付渠道优惠，通常是最大值
    **/
    5: optional TradePayChannelDiscountInfo suggestDiscountInfo;
}

struct RealCashierRequest{
    1: required string userCid;
    /**
    * 透传获取的queryCashier接口返回的payinfoKey
    **/
    2: required string payinfoKey;
    /**
    * 算价结果中返回的需要三方支付的金额，单位：分
    **/
    3: required i32 needPayFen;
    /**
    * 是否查询支付渠道优惠金额
    **/
    4: bool queryPayDiscount;
}

struct TencentBillRequest{
    /**
    * 用户id
    **/
    1: optional i64 userId;
    /**
    *   原支付交易订单号
    **/
    2: optional i64 payTradeNo;
    /**
    *   交易类型   TradeOrderTypeEnum
    **/
    3: optional string tradeType;
    /**
    *  退款来源 1.合拼返款  2.赔付  3.其他
    **/
    4: optional i32 refundSource;
    /**
    *  退款的交易的请求id
    **/
    5: optional string requestId;
    /**
    *   账单信息
    **/
    6: optional string billInfo;
    /**
    *  支付/退款 账单流水号
    **/
    7: optional string transactionId;
}


struct TencentBillResponse{
    1: optional i32 code;
    2: optional string message;
}



struct LastOnePayRequest{
    /**
     * 用户cid
     */
    1: optional string userCid;
    /**
     * 用户id
     */
    2: optional i64 userId;
    /**
     * 用户版本号
     */
    3: optional string version;
    /**
     * 业务
     */
    4: optional string businessType;
    /**
     * 来源
     */
    5: optional string source;
    /**
     * 经纬度
     */
    6: optional string ddcInfo;
}

struct CountTradedRequest{
    1: optional string majorProductId;
    2: optional string businessType;
    3: optional i64 userId;
}

struct CountTradedResponse{
    1: optional i32 code;
    2: optional string message;
    3: optional i32 count;
}


struct TencentPauOrderNotifyRequest{
    /**
    * 用户id
    **/
    1: required i64 userId;
    /**
    *   原支付交易订单号
    **/
    2: required i64 payTradeNo;
    /**
    *   通知类型\n
    *   1.发起扣款\n
    *   2.取消扣款通知\n
    **/
    3: required string notifyType;
    /**
    *  通知时间  格式：yyyy-MM-dd HH:mm:ss
    **/
    4: optional string notifyTime;
    /**
    *  发起扣款金额
    **/
    5: optional string cutAmount;
}


struct TencentPauOrderNotifyResponse{
    1: optional i32 code;
    2: optional string message;
}

































