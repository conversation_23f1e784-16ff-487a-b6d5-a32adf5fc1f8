package com.didapinche.trade.service.account.hook.subscriber;

import com.didapinche.commons.rocketmq.AbstractRocketMQSubscriber;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.thrift.drivercoupon.DrivercouponThriftService;
import com.didapinche.trade.infrastructure.enums.RocketMQGroupEnum;
import com.didapinche.trade.infrastructure.enums.RocketMQTopicEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Component
@DependsOn({ "ZEUS-carpoolwriteThriftService"})
@Slf4j
public class DriverCouponSendAtCertify extends AbstractRocketMQSubscriber<Map<String, Object>> {

    @Resource
    private DrivercouponThriftService.Iface driverService;

    @Override
    public void init() {
        setGroup(RocketMQGroupEnum.C_BDC_PAYMENT_DRIVER_CERTIFY_COUPON_SEND.name());
        setTopic(RocketMQTopicEnum.t_server_user_driver_pass_success.name());
    }

    @Override
    public void execute(Map<String, Object> params) throws Exception {
        if (params == null || params.get("userId") == null) {
            return;
        }
        driverService.addCouponAtCertify(params.get("userId").toString());
        log.info("车主注册发送优惠券end. params:{}", JsonMapper.toJson(params));
    }
}
