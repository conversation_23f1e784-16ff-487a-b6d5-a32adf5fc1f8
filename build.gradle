buildscript {
    ext {
        springBootVersion = '1.5.22.RELEASE'
        horaeSpringbootBom = '1.2.0'
    }
    repositories {
        maven { url "http://mirror.didapinche.com/repository/maven-public" }
        mavenCentral()
        mavenLocal()
    }
    dependencies {
        classpath "io.spring.gradle:dependency-management-plugin:1.0.10.RELEASE"
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
//        classpath("io.qameta.allure:allure-gradle:2.5")
    }
}

allprojects {

    apply plugin: 'io.spring.dependency-management'

}

group 'com.didapinche'

subprojects {
    apply plugin: 'java'
    apply plugin: 'maven'
    apply plugin: 'jacoco'
    apply plugin: 'org.springframework.boot'
    sourceCompatibility = 1.8
    targetCompatibility = 1.8
    ext {
        springBootVersion = '1.5.22.RELEASE'
        agaueVersion = '0.0.12'
        hutoolCoreVersion = "5.7.16"
        springCloudVersion = 'Edgware.SR2'
        openfeignVersion = "9.5.0"
    }
    repositories {
        maven { url "http://mirror.didapinche.com/repository/maven-public" }
        maven { url "https://plugins.gradle.org/m2/" }
        mavenCentral()
        mavenLocal()
    }
    dependencyManagement {
        imports {
            // https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-dependencies
//            mavenBom "org.springframework.boot:spring-boot-dependencies:${springBootVersion}"
//            mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
            mavenBom("com.didapinche.fi:horae-springboot-bom:${horaeSpringbootBom}")
        }
    }
    tasks.withType(JavaCompile) {
        options.encoding = 'UTF-8'
    }

    processResources {
        from('src/main/java') {
            include '**/*.xml'
        }
    }
    task copyXml(type: Copy) {
        from('build/resources/main/com') {
            include '**/*'
        }
        into('build/classes/java/main/com')
    }
    jar.dependsOn(copyXml)

    task packageSources(type: Jar) {
        classifier = 'sources'
        from sourceSets.main.allSource
    }

    install {
        outputs.upToDateWhen { true }
        repositories.mavenInstaller {}
    }

    artifacts.archives packageSources


    dependencies {

        implementation("com.didapinche.fi:horae-springboot-bom:${horaeSpringbootBom}")

        compile('com.didapinche:server-risk-sdk:1.0.1-SNAPSHOT')

        //部门公共包
        compile 'com.didapinche:com.didapinche.commons.lang:1.5.1-SNAPSHOT'
        compile("com.didapinche:server-commons-common")
        compile('com.didapinche:server-commons-mapper:1.3.5')
        compile('com.didapinche:autoconfigure-zeus:2.3.2')
        compile('com.didapinche.payment:async-assist:1.1.2-SNAPSHOT')
        compile('com.didapinche.payment:frigate-assist-starter:1.1.2.4-SNAPSHOT')
        compile('com.didapinche.payment:dislock-assist-starter:1.0.5-SNAPSHOT')
        compile('com.didapinche:autoconfigure-apollo:1.2.0')
        compile('com.didapinche:autoconfigure-common:1.3.3')
        compile('com.didapinche:autoconfigure-push:1.0.0-SNAPSHOT')
        compile("com.didapinche:maia-jedis2-spring-boot-starter")
        compile("redis.clients:jedis:2.10.2")
//        compile('com.didapinche:autoconfigure-redis-cluster:1.1.0-SNAPSHOT')
        compile group: 'com.didapinche.pay.arch.common', name: 'pay-arch', version: '1.0.5-SNAPSHOT'
        //orm
        compile "com.baomidou:mybatis-plus-boot-starter:3.5.1"
//        compile 'org.mybatis.spring.boot:mybatis-spring-boot-starter:2.2.0'
//        compile "mysql:mysql-connector-java:5.1.44"
        compile('com.didapinche:autoconfigure-rocketmq:1.3.1')
        compile('com.didapinche:finance-mq-client:********-SNAPSHOT')
        compile('com.didapinche:financethea-entity:1.0.2-SNAPSHOT')

        compile('com.ctrip.framework.apollo:apollo-client')
        compile('com.alibaba:fastjson')
        compile('com.didapinche:zeus-apollo-spring-boot-starter')
        compile('com.uber.tchannel:tchannel-core:0.8.10-dida')
        compile("com.didapinche:rocketmq-client-spring-boot-starter")
        compile("com.didapinche:com.didapinche.commons.rocketmq:1.8.0")
        compile ('io.netty:netty-all:4.1.78.Final'){
            force = true
        }

        compile('com.didapinche:dida-metrics-core:1.0-SNAPSHOT')

        //agaue组件包信息,不再应用autocommon的组件包信息
        compile("com.didapinche:agaue-common:0.0.26-SNAPSHOT")
        compile("com.didapinche:agaue-core:0.0.26-SNAPSHOT")
        compile("com.didapinche:agaue-spring:0.0.25-SNAPSHOT")
        compile("com.didapinche:agaue-datasource:${agaueVersion}")
        compile("com.didapinche:agaue-tools:${agaueVersion}")
        //thrift服务包
        compile 'com.didapinche.thrift.api:taxiwritepay_api:1.2.1-SNAPSHOT'
        compile 'com.didapinche.thrift.api:taxiread_api:2.0.6-SNAPSHOT'
        compile 'com.didapinche.thrift.api:taxidriver_api:1.5.11-SNAPSHOT'
        compile 'com.didapinche.thrift.api:payorderread_thrift_service_api:1.3.11-SNAPSHOT'
        compile 'com.didapinche.thrift.api:carpoolwritepay_thrift_service_api:1.3.0-SNAPSHOT'
        compile 'com.didapinche.thrift.api:accountread_api:1.4.6-SNAPSHOT'
        compile('com.didapinche.thrift.api:blackinfo_api:1.2.0')
        compile('com.didapinche.thrift.api:carpoolread_thrift_service_api:2.2.10-SNAPSHOT')
        compile 'com.didapinche.thrift.api:rechargewrite_api:1.0.5-SNAPSHOT'
        compile 'com.didapinche.thrift.api:trade_thrift_api:1.3.19-SNAPSHOT'
        compile 'com.didapinche.thrift.api:coupon_thrift_service_api:1.0.15-SNAPSHOT'
        compile 'com.didapinche.thrift.api:pay_thrift_api:1.3.8-SNAPSHOT'
        compile 'com.didapinche.thrift.api:touch_server_api_send:1.1.0-SNAPSHOT'
        compile 'com.didapinche.touch:touch-common:1.1.1-SNAPSHOT'
        compile 'com.didapinche.touch:touch-server-client:1.1.1-SNAPSHOT'
        compile 'com.didapinche.thrift.api:usercid2id_thrift_service_api:3.0.0-SNAPSHOT'
        compile('com.didapinche.thrift.api:thirdpartypayconfig_api:1.0.1-SNAPSHOT')
        compile 'com.didapinche.thrift.api:accountwrite_api:1.4.4-SNAPSHOT'
        compile 'com.didapinche.thrift.api:account_thrift_api:1.1.1-SNAPSHOT'
        compile('com.didapinche.thrift.api:userpermission_api:1.1.1')
        compile 'com.didapinche.thrift.api:enterpriseaccread_api:1.0.2-SNAPSHOT'
        compile 'com.didapinche.thrift.api:enterpriseaccwrite_api:1.0.7-SNAPSHOT'
        compile 'com.didapinche.thrift.api:pay_query_thrift_service:1.0.13-SNAPSHOT'
        compile 'com.didapinche.thrift.api:active_api:1.1.5'
        compile 'com.didapinche.thrift.api:activity_platform_api:1.5.9-SNAPSHOT'
        compile 'com.didapinche.thrift.api:user_api:3.0.0-SNAPSHOT'
        compile('com.didapinche.thrift.api:didapincheread_api:1.0.0-SNAPSHOT')
        compile 'com.didapinche.thrift.api:pay_authentication_api:1.0.6-SNAPSHOT'
        compile 'com.didapinche.thrift.api:bca_rhea_diversion_thrift_service_api:1.1.0-SNAPSHOT'
        compile 'com.didapinche.thrift.api:risk_thrift_service:1.0.4-SNAPSHOT'
        compile 'com.didapinche.thrift.api:thirdpartypayconfig_api:1.0.4-SNAPSHOT'
        compile 'com.didapinche.thrift.api:jifen_api:1.0.14-SNAPSHOT'
        compile 'com.didapinche.thrift.api:ai_go_order_subsidy_service_api:1.0.0-SNAPSHOT'
        compile 'com.didapinche.thrift.api:taxi_usercar_api:1.0.0-SNAPSHOT'
        compile 'com.didapinche.thrift.api:carpoolwrite_thrift_service_api:1.7.25-SNAPSHOT'
        compile 'com.didapinche.thrift.api:taxiwrite_api:1.3.9-SNAPSHOT'
        compile 'com.didapinche.thrift.api:websocket-thrift-service:1.0.0-SNAPSHOT'
        compile 'com.didapinche.thrift.api:drivercoupon_thrift_service_api:1.0.5-SNAPSHOT'
        compile 'com.didapinche.thrift.api:taxidriver_api:1.5.4-SNAPSHOT'
        compile 'com.didapinche.thrift.api:pay_mozi_thrift_service:1.0.0-SNAPSHOT'
        compile('com.didapinche.taxi:taxiride-client:1.1.0-SNAPSHOT')
        compile('com.didapinche.thrift.api:offlineread_api:1.1.0-SNAPSHOT')
        compile('com.didapinche.thrift.api:taxiride_query_platform_thrift_api:1.0.0-SNAPSHOT')
        compile 'com.didapinche.thrift.api:tradeplatform_requests:1.0.1-SNAPSHOT'
        compile 'com.didapinche.thrift.api:tradeplatform_responses:1.0.0-SNAPSHOT'
        compile 'com.didapinche.thrift.api:tradeplatform_thrift_api:1.0.3-SNAPSHOT'
        compile 'com.didapinche.thrift.api:activity_right_thrift_api:2.0.19-SNAPSHOT'
        compile 'com.didapinche.thrift.api:thirdpartypayconfig_api:1.0.5-SNAPSHOT'

        //出租车公共包
        compile ('com.didapinche:taxi-bean:1.5.0-SNAPSHOT')

        //双写公共包
        compile 'com.didapinche.doublewritecheck:dwc-common:1.0-SNAPSHOT'
        //双写接口
        compile 'com.didapinche.thrift.api:doublewritecheck_api:1.0.0-SNAPSHOT'

        //顺风车公共包
        compile('com.didapinche:carpool-commons-entity:1.0.0-SNAPSHOT')
        compile('com.didapinche:payment-liquidation-common:0.0.5-SNAPSHOT')


        compile('org.springframework.cloud:spring-cloud-starter-openfeign:1.4.3.RELEASE')
        compile('org.springframework.cloud:spring-cloud-context')

        //es
        compile 'org.elasticsearch.client:elasticsearch-rest-high-level-client:7.15.2'
        compile 'org.elasticsearch:elasticsearch:7.15.2'

        //shardingsphere
        compile("org.apache.shardingsphere:shardingsphere-jdbc-core-spring-boot-starter:5.1.2")

        testCompile 'com.didapinche.thrift.api:globalseq_api:1.2.0'

        compile group: 'com.flipkart.zjsonpatch', name: 'zjsonpatch', version: '0.4.16'

        //openfeign
        compile("io.github.openfeign:feign-core:${openfeignVersion}") {
            force = true
        }
        compile("io.github.openfeign:feign-slf4j:${openfeignVersion}") {
            force = true
        }
        compile("io.github.openfeign:feign-okhttp:${openfeignVersion}") {
            force = true
        }
        compile("io.github.openfeign:feign-jackson:${openfeignVersion}"){
            force = true
        }

        //支付基础包
        implementation 'org.apache.commons:commons-collections4:4.1'
        compile ('com.didapinche.id.generator:id-generator:0.0.3-SNAPSHOT')

        //bean util...
        compile("cn.hutool:hutool-core:${hutoolCoreVersion}")
        compile("cn.hutool:hutool-poi:${hutoolCoreVersion}")
        compile("cn.hutool:hutool-json:${hutoolCoreVersion}")

        compile 'com.github.pagehelper:pagehelper-spring-boot-starter:1.4.0'

        compile ('com.google.guava:guava')
        compile ('com.squareup.okhttp3:okhttp')
        compile "org.apache.curator:curator-framework:2.12.0"

        // swagger
        compile group:'io.springfox',name:'springfox-swagger2',version:'2.9.2'
        compile group:'io.springfox',name:'springfox-swagger-ui',version:'2.9.2'

        //lombok
        compileOnly('org.projectlombok:lombok:1.18.22')
        annotationProcessor("org.projectlombok:lombok:1.18.22")
        testAnnotationProcessor("org.projectlombok:lombok:1.18.22")
        testCompileOnly("org.projectlombok:lombok:1.18.22")

        compile 'org.mapstruct:mapstruct:1.5.5.Final'
        annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final'
        compile 'org.projectlombok:lombok-mapstruct-binding:0.2.0'

        //unit test
        testCompile('org.springframework.boot:spring-boot-starter-test')
        testCompile('junit:junit:4.12')
        testCompile('org.mockito:mockito-core:2.8.9')
        testCompile('org.powermock:powermock-module-junit4:1.7.4')
        testCompile('org.powermock:powermock-api-mockito2:1.7.4')
        testCompile "org.powermock:powermock-module-junit4-rule:1.7.4"
        testCompile "org.powermock:powermock-classloading-xstream:1.7.4"


    }

    configurations.all {
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
        resolutionStrategy { force 'xml-apis:xml-apis:1.0.b2' }
        exclude module: 'slf4j-log4j12'
        exclude module: 'dida-metrics-thrift'
        exclude module: 'dida-metrics-context'
        exclude module: 'dida-metrics-executor'
//        exclude module: 'dida-metrics-core'
        exclude module: 'dida-metrics-druid'
        exclude module: 'dida-metrics-jvm'
        exclude module: 'dida-metrics-tomcat'
        exclude module: 'dida-metrics-mvc'
        exclude module: 'promeconsul-sb1'
        exclude module: 'autoconfigure-redis-cluster'
//        exclude module: 'autoconfigure-common'
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    }
}