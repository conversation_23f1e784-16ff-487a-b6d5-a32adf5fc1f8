package com.didapinche.trade.domin.DO;

import com.didapinche.trade.infrastructure.tbl.RemittanceOrderModel;
import com.google.common.base.Preconditions;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/8/25 18:13
 * @Version 1.0
 */
@Data
public class RemittanceOrderModelDO {

    public RemittanceOrderModelDO(){}

    public RemittanceOrderModelDO(RemittanceOrderModel remittanceOrderModel){
        Preconditions.checkArgument(remittanceOrderModel != null,"remittanceOrderModel is empty");
        this.id = remittanceOrderModel.getId();
        this.businessType = remittanceOrderModel.getBusinessType();
        this.payTradeNo = remittanceOrderModel.getPayTradeNo();
        this.payUserId = remittanceOrderModel.getPayUserId();
        this.incomeUserId = remittanceOrderModel.getIncomeUserId();
        this.payFee = remittanceOrderModel.getPayFee();
        this.incomeFee = remittanceOrderModel.getIncomeFee();
        this.comment = remittanceOrderModel.getComment();
        this.note = remittanceOrderModel.getNote();
        this.createTime = remittanceOrderModel.getCreateTime();
        this.productId = remittanceOrderModel.getProductId();
        this.sumFee = remittanceOrderModel.getSumFee();
    }

    public RemittanceOrderModel convertRemittanceOrderModel(){
        RemittanceOrderModel remittanceOrderModel = new RemittanceOrderModel();
        remittanceOrderModel.setId(this.id);
        remittanceOrderModel.setProductId(this.productId);
        remittanceOrderModel.setBusinessType(this.businessType);
        remittanceOrderModel.setPayTradeNo(this.payTradeNo);
        remittanceOrderModel.setPayUserId(this.payUserId);
        remittanceOrderModel.setIncomeUserId(this.incomeUserId);
        remittanceOrderModel.setSumFee(this.sumFee);
        remittanceOrderModel.setPayFee(this.payFee);
        remittanceOrderModel.setIncomeFee(this.incomeFee);
        remittanceOrderModel.setComment(this.comment);
        remittanceOrderModel.setNote(this.note);
        remittanceOrderModel.setCreateTime(this.createTime);
        return remittanceOrderModel;
    }


    private long id;
    /**
     * 业务侧id
     */
    private String productId;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 支付订单号
     */
    private Long payTradeNo;
    /**
     * 出账用户id
     */
    private Long payUserId;
    /**
     * 入账用户id
     */
    private Long incomeUserId;

    /**
     * 总金额
     */
    private BigDecimal sumFee;

    /**
     * 出账金额
     */
    private BigDecimal payFee;

    /**
     * 入账金额
     */
    private BigDecimal incomeFee;

    /**
     * 备注
     */
    private String comment;
    /**
     * 扩展字段
     */
    private String note;

    /**
     * 创建时间
     */
    private Date createTime;
}
