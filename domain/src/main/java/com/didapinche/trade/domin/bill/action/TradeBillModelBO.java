package com.didapinche.trade.domin.bill.action;

import com.didapinche.trade.infrastructure.tbl.TradeBillModel;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class TradeBillModelBO extends TradeBillModel {
    public TradeBillModelBO(TradeBillModel billModel) {
        if(billModel==null){
            return;
        }
        this.setId(billModel.getId());
        this.setSn(billModel.getSn());
        this.setProductId(billModel.getProductId());
        this.setProductBiz(billModel.getProductBiz());
        this.setInitialPrice(billModel.getInitialPrice());
        this.setPrice(billModel.getPrice());
        this.setPayerId(billModel.getPayerId());
        this.setPaymentAmount(billModel.getPaymentAmount());
        this.setPayerRefundAmount(billModel.getPayerRefundAmount());
        this.setPayType(billModel.getPayType());
        this.setPayTypeTag(billModel.getPayTypeTag());
        this.setPayeeId(billModel.getPayeeId());
        this.setReceiptsAmount(billModel.getReceiptsAmount());//收款金额
        this.setPayeeReturnAmount(billModel.getPayeeReturnAmount());//收款人退款金额
        this.setPayeePenalty(billModel.getPayeePenalty());//对收款人的罚金
        this.setInvolvedCommission(billModel.getInvolvedCommission());//参与分佣金额，并不所有的金额都给平台分佣的
        this.setPlatformCommission(billModel.getPlatformCommission());//平台佣金
        this.setPlatformReturnCommission(billModel.getPlatformReturnCommission());//平台退回佣金，完单后退款时，平台退回的佣金
        this.setPayState(billModel.getPayState());//支付状态
        this.setPend(billModel.getPend());//挂起状态
        this.setTransferState(billModel.getTransferState());//到账状态
        this.setRiskTag(billModel.getRiskTag());//风险标签
        this.setFreeAmount(billModel.getFreeAmount());
        this.setFreeTag(billModel.getFreeTag());
        this.setExt(billModel.getExt());
        this.setVersion(billModel.getVersion());
    }
}
