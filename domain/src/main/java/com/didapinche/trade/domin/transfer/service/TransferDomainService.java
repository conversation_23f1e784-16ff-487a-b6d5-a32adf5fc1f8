package com.didapinche.trade.domin.transfer.service;

import com.didapinche.agaue.common.exception.DException;
import com.didapinche.agaue.datasource.common.Routing;
import com.didapinche.agaue.datasource.enums.DatabaseEnum;
import com.didapinche.agaue.datasource.enums.DatabaseTypeEnum;
import com.didapinche.agaue.datasource.toolkit.DynamicDataSourceContextHolder;
import com.didapinche.agaue.tools.convert.AmountConvertUtil;
import com.didapinche.doublewritecheck.DoubleWriteCheckParam;
import com.didapinche.doublewritecheck.entity.carpool.CarpoolTransferParam;
import com.didapinche.doublewritecheck.entity.recharge.RechargeParam;
import com.didapinche.doublewritecheck.entity.taxi.TaxiTransferParam;
import com.didapinche.finance.mq.context.enums.TheaExtraInfoKeyEnum;
import com.didapinche.finance.mq.context.enums.TradeTypeEnum;
import com.didapinche.liquidate.common.enums.pingan.LiquidationAccountTypeEnums;
import com.didapinche.pay.arch.common.threadpool.SharedThreadPool;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.server.commons.common.util.LoadPropertyUtil;
import com.didapinche.trade.domin.DO.RemittanceOrderModelDO;
import com.didapinche.trade.domin.DO.TradeOrderDO;
import com.didapinche.trade.domin.DO.common.CommonResultDO;
import com.didapinche.trade.domin.DO.payment.AdvanceOrderDO;
import com.didapinche.trade.domin.bill.action.mapping.BillOrderMappingVisitor;
import com.didapinche.trade.domin.bill.action.sharing.SharingActionVisitor;
import com.didapinche.trade.domin.bill.action.sharing.bo.SharingElementBO;
import com.didapinche.trade.domin.bean_new.TradeAmountDetailsMultiple;
import com.didapinche.trade.domin.entity.ScanExtraNote;
import com.didapinche.trade.domin.entity.TransferResultEntity;
import com.didapinche.trade.domin.liquidation.message.ConficateLiquidationMessage;
import com.didapinche.trade.domin.liquidation.message.TransferLiquidationMessage;
import com.didapinche.trade.domin.liquidation.message.TransferWithOrderLiquidationMessage;
import com.didapinche.trade.domin.mozi.context.MoziTransferContext;
import com.didapinche.trade.domin.mozi.service.MoziService;
import com.didapinche.trade.domin.payment.pau.PaymentPayAfterUseDomainService;
import com.didapinche.trade.domin.refund.DO.CalculateOrderDO;
import com.didapinche.trade.domin.refund.service.CalculateOrderDomainService;
import com.didapinche.trade.domin.repository.RemittanceOrderRepository;
import com.didapinche.trade.domin.repository.TradeAmountDetailsRepository;
import com.didapinche.trade.domin.repository.TradeOrderRepository;
import com.didapinche.trade.domin.thea.message.CommonTradeOrderTheaMessage;
import com.didapinche.trade.domin.transfer.DO.TransferDO;
import com.didapinche.trade.domin.transfer.request.TransferDomainRequest;
import com.didapinche.trade.infrastructure.entities.TradeOrderNote;
import com.didapinche.trade.infrastructure.enums.order.TradeOrderStatusEnum;
import com.didapinche.trade.infrastructure.enums.order.TradeOrderTypeEnum;
import com.didapinche.trade.infrastructure.exception.OrderErrorCode;
import com.didapinche.trade.infrastructure.exception.TradeErrorCode;
import com.didapinche.trade.infrastructure.metric.TradeMetric;
import com.didapinche.trade.infrastructure.mq.RocketMqSendUtils;
import com.didapinche.trade.infrastructure.service.impl.TradeAmountDetailsModelService;
import com.didapinche.trade.infrastructure.service.impl.TradeProductDetailsModelService;
import com.didapinche.trade.infrastructure.support.TradeOrderIdFactory;
import com.didapinche.trade.infrastructure.tbl.TradeAmountDetailsModel;
import com.didapinche.trade.infrastructure.tbl.TradeOrderModel;
import com.didapinche.trade.infrastructure.tbl.TradeProductDetailsModel;
import com.didapinche.trade.infrastructure.thrift.RoutingThriftSupportService;
import com.didapinche.trade.infrastructure.util.AccountChangeUtil;
import com.didapinche.trade.infrastructure.util.BigDecimalUtil;
import com.didapinche.trade.infrastructure.util.LogProxy;
import com.didapinche.trade.infrastructure.util.accountchangefactory.bean.InnerAccountChangeItem;
import com.didapinche.trade.thrift.enums.TBusinessEnum;
import com.didapinche.trade.thrift.enums.TChannelEnum;
import com.didapinche.trade.thrift.enums.TOrderAccountTypeEnum;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.didapinche.trade.infrastructure.constants.TradeConstants.CARPOOL_ORDER_FREE_BUSINESS_TYPE;
import static com.didapinche.trade.infrastructure.exception.TradeErrorCode.FREE_ERR;
import static com.didapinche.trade.infrastructure.exception.TradeErrorCode.NOT_FUND_PAY_ORDER_ERROR;
import static com.didapinche.trade.infrastructure.exception.TradeErrorCode.NO_SUPPORT_TRANSFER_CHANNEL_ERROR;
import static com.didapinche.trade.infrastructure.exception.TradeErrorCode.ORDER_ERROR;
import static com.didapinche.trade.infrastructure.exception.TradeErrorCode.ORDER_NOTEXIT_ERROR;
import static com.didapinche.trade.infrastructure.exception.TradeErrorCode.PARAM_ERROR;
import static com.didapinche.trade.infrastructure.exception.TradeErrorCode.REMITT_AMOUNT_ERROR;
import static com.didapinche.trade.infrastructure.util.SystemUtils.validNum;

/**
 * <AUTHOR>
 * @Date 2022/8/9 11:15
 * @Version 1.0
 *
 */
@Service
@Slf4j
public class TransferDomainService {


    @Autowired
    private TradeOrderRepository tradeOrderRepository;
    @Autowired
    private TradeAmountDetailsRepository tradeAmountDetailsRepository;
    @Autowired
    private TradeOrderIdFactory tradeOrderIdFactory;
    @Autowired
    private CalculateOrderDomainService calculateOrderDomainService;
    @Autowired
    private RemittanceOrderRepository remittanceOrderRepository;
    @Resource
    private PaymentPayAfterUseDomainService paymentPayAfterUseDomainService;


    @Autowired
    SharingActionVisitor sharingActionVisitor;
    @Autowired
    BillOrderMappingVisitor billOrderMappingVisitor;

    @Resource
    private MoziService moziService;

    @Resource
    protected TradeAmountDetailsModelService tradeAmountDetailsModelService;

    @Resource
    private TradeProductDetailsModelService tradeProductDetailsModelService;

    @Resource
    private RoutingThriftSupportService routingThriftService;

    /**
     * 加钱接口
     */
    public CommonResultDO<TransferResultEntity> transfer(TransferDomainRequest transferDomainRequest) throws Exception {
        log.info("transfer start. request={}", JsonMapper.toJson(transferDomainRequest));
        CommonResultDO<TransferResultEntity> commonResultDO = new CommonResultDO<>();

        TransferDO transferDO = transferDomainRequest.getTransferDO();
        TradeOrderDO tradeOrderDO = transferDomainRequest.getTradeOrderDO();
        if (transferDO.getAmount().compareTo(BigDecimal.ZERO)< 0 || transferDO.getBonus().compareTo(BigDecimal.ZERO)< 0 || transferDO.getAmount().add(transferDO.getBonus()).compareTo(BigDecimal.ZERO) < 1){
            log.warn("productId:{} amount:{} bonus:{}转账参数错误",tradeOrderDO.getMajorProductId(),transferDO.getAmount().toString(),transferDO.getBonus().toString());
            throw new DException(PARAM_ERROR);
        }
        if (transferDO.getAmount().compareTo(LoadPropertyUtil.getProperty("max_common_transfer_amount", BigDecimal.class, BigDecimal.valueOf(2000))) > 0) {
            log.warn("productId:{} amount:{} 超过最大转账金额",tradeOrderDO.getMajorProductId(),transferDO.getAmount().toString());
            throw new DException(REMITT_AMOUNT_ERROR);
        }
        //业务check和处理
        
        commonBusinessCheckAndProcess(tradeOrderDO, transferDomainRequest.getPayUserId(), transferDO);
        List<TradeOrderDO> tradeOrderDos = tradeOrderRepository.queryTradeOrderList(tradeOrderDO.getMajorProductId(), TradeTypeEnum.transfer.name(), tradeOrderDO.getBusinessType(), transferDO.getUserId());
        if (!CollectionUtils.isEmpty(tradeOrderDos)){
            log.warn("{}：{}重复转账",tradeOrderDO.getBusinessType(),tradeOrderDO.getMajorProductId());
            TradeOrderDO existDO = tradeOrderDos.get(0);
            return commonResultDO.setDidaCode(ORDER_ERROR).setT(new TransferResultEntity(existDO.getTotalPrice(),existDO.getId())).setTradeOrderId(existDO.getId());
        }
        // 添加id

        long id = validNum(tradeOrderDO.getPayTradeNo())?
                tradeOrderIdFactory.genUniqueId(tradeOrderDO.getPayTradeNo()+"")
                :tradeOrderIdFactory.genUniqueId(tradeOrderDO.getMajorProductId()+"");
        tradeOrderDO.setId(id);
        transferDO.setOrderId(id);
        // 账户处理
        accountDeal(transferDO);
        //入库
        tradeOrderDO.setTradeType(TradeTypeEnum.transfer.name());
        tradeOrderDO.setStatus(TradeOrderStatusEnum.success.name());
        Date now = new Date();
        tradeOrderDO.setCreateTime(now);
        tradeOrderDO.setUpdateTime(now);
        tradeOrderDO.setSuccessTime(now);
        tradeOrderDO.setTotalPrice(transferDO.getAmount().add(transferDO.getBonus()));
        log.info("保存trade_order. tradeOrderDO={}", JsonMapper.toJson(tradeOrderDO));
        TradeOrderModel tradeOrderModel = tradeOrderRepository.save(tradeOrderDO);
        List<TradeAmountDetailsModel> amountDetailsModels = tradeAmountDetailsRepository.save(transferDO, tradeOrderDO.getId());
        //双写
        doubleWriteV2(transferDO,tradeOrderDO);
        // thea+
        CommonTradeOrderTheaMessage commonTradeOrderTheaMessage = new CommonTradeOrderTheaMessage();
        commonTradeOrderTheaMessage.setTradeOrder(tradeOrderModel)
                .setAmountDetails(amountDetailsModels)
                .send();
        
        if(notSendLiquidationMessage(tradeOrderModel.getBusinessType())){
        }else{
            // 二清
            TransferLiquidationMessage liquidationMessage = new TransferLiquidationMessage();
            liquidationMessage.setTradeOrder(tradeOrderModel)
                    .setAfterTrade(message ->{
                        if (TBusinessEnum.carpool_carefree.name().equals(tradeOrderModel.getBusinessType())){
                            Preconditions.checkArgument(transferDomainRequest.getPayUserId() != null && transferDomainRequest.getPayUserId() > 0);
                            message.setPayOutUserId(transferDomainRequest.getPayUserId().toString());
                            message.setPayOutTypeEnum(LiquidationAccountTypeEnums.passenger.name());
                            message.setBalance(0);
                            message.setMarketBalance(AmountConvertUtil.yuan2penny(tradeOrderModel.getTotalPrice()));
                        }
                    })
                    .send();
        }

        commonResultDO.setT(new TransferResultEntity(tradeOrderDO.getTotalPrice(),tradeOrderDO.getId()))
                .setTradeOrderId(tradeOrderDO.getId());
        return commonResultDO;
    }

    private boolean notSendLiquidationMessage(String businessType) {
        return StringUtils.equalsAny(businessType, TBusinessEnum.taxi_arrears_recharge.name());
    }

    /**
     * 到账接口
     * 会检查到账金额少于支付-退款金额
     * 目前涉及业务 顺风车费/高速费/补偿金到账
     */
    public CommonResultDO<TransferResultEntity> transferWithCheckPay(TransferDomainRequest transferDomainRequest) throws Exception {
        CommonResultDO<TransferResultEntity> commonResultDO = new CommonResultDO<>();

        TransferDO transferDO = transferDomainRequest.getTransferDO();//收款方信息
        TradeOrderDO tradeOrderDO = transferDomainRequest.getTradeOrderDO();//到账订单记录

        // 根据主订单加锁
        TradeOrderDO orderDO = tradeOrderRepository.queryWithLock(tradeOrderDO.getPayTradeNo(), transferDomainRequest.getPayUserId());
        if (orderDO == null){
            throw new DException(NOT_FUND_PAY_ORDER_ERROR);
        }
        // 幂等性校验
        List<TradeOrderDO> tradeOrderDos = tradeOrderRepository.queryTradeOrderList(tradeOrderDO.getMajorProductId(), TradeTypeEnum.transfer.name(), tradeOrderDO.getBusinessType(), transferDO.getUserId());
        if (!CollectionUtils.isEmpty(tradeOrderDos)){
            log.warn("{}：{}重复转账",tradeOrderDO.getBusinessType(),tradeOrderDO.getMajorProductId());
            TradeOrderDO existDO = tradeOrderDos.get(0);
            return commonResultDO.setDidaCode(ORDER_ERROR).setT(new TransferResultEntity(existDO.getTotalPrice(),existDO.getId())).setTradeOrderId(existDO.getId());
        }
        //业务
        AdvanceOrderDO advanceOrderDO = businessCheckAndProcess(tradeOrderDO, orderDO);
        // 出租车如果有垫付就不到账
        BigDecimal taxiAdvance = BigDecimal.ZERO;
        if (TBusinessEnum.taxi_fee.name().equals(tradeOrderDO.getBusinessType())){
            List<TradeOrderDO> advanceTradeOrderDos = tradeOrderRepository.queryTradeOrderList(tradeOrderDO.getMajorProductId(), TradeTypeEnum.transfer.name(), TBusinessEnum.taxi_advance.name(), transferDO.getUserId());
            if (!CollectionUtils.isEmpty(advanceTradeOrderDos)){
                transferDO.setAmount(BigDecimal.ZERO);
                tradeOrderDO.setTotalPrice(BigDecimal.ZERO);
                for (TradeOrderDO entity : advanceTradeOrderDos){
                    taxiAdvance = taxiAdvance.add(entity.getTotalPrice());
                }
            }
            // 银盛西安广州微信不可以到账
            if (StringUtils.equalsAny(orderDO.getThirdChannel(), TChannelEnum.gz_weixin.name(),TChannelEnum.yspay.name(),TChannelEnum.xianbank.name())){
                throw new DException(NO_SUPPORT_TRANSFER_CHANNEL_ERROR);
            }
        }

        //顺风车到账添加车主id，用于先享后付终止扣款时判断是否到账
        if (StringUtils.equals(TBusinessEnum.carpool.name(), orderDO.getBusinessType())) {
            TradeOrderModel tradeOrderModel = new TradeOrderModel();
            tradeOrderModel.setId(orderDO.getId());
            tradeOrderModel.setUserId(orderDO.getUserId());
            TradeOrderNote note = TradeOrderNote.create(orderDO.getNote());
            note.setDriverId(transferDO.getUserId());
            if (transferDomainRequest.getAutoCheatComplainFlag() != null && transferDomainRequest.getAutoCheatComplainFlag()) {
                TradeOrderNote.AutoCheatComplain autoCheatComplain = new TradeOrderNote.AutoCheatComplain();
                autoCheatComplain.setBonusRecover(transferDomainRequest.getBonusRecover().toString());
                autoCheatComplain.setCouponRecover(transferDomainRequest.getCouponRecover().toString());
                autoCheatComplain.setPassengerSubsidyRecover(BigDecimalUtil.zeroIfNull(transferDomainRequest.getPassengerSubsidyRecover()).toString());
                note.setAutoCheatComplain(autoCheatComplain);
            }
            //先乘后付作弊冻结判断
            Long plantFreezeTime = getPlantFreezeTime(transferDO.getPlantFreezeTime(), note);
            transferDO.setPlantFreezeTime(plantFreezeTime);
            if (plantFreezeTime > 0) {
                transferDO.setOrderSource(orderDO.getSource());
                note.setPauCheatFreezeFlag("free");
                log.info("该笔订单需冻结到账金额, payTradeNo:{}, cutFlag:{}", tradeOrderDO.getPayTradeNo(), note.getPauCutFlag());
            }
            tradeOrderModel.setNote(note.toJson());
            tradeOrderRepository.updateById(tradeOrderModel);
        }

        long id = tradeOrderIdFactory.genUniqueId(tradeOrderDO.getMajorProductId()+"");
        tradeOrderDO.setId(id);
        tradeOrderDO.setBillId(orderDO.getBillId());//关联账户ID
        transferDO.setOrderId(id);
        // 组装入库参数
        tradeOrderDO.setTradeType(TradeTypeEnum.transfer.name());
        tradeOrderDO.setStatus(TradeOrderStatusEnum.success.name());
        tradeOrderDO.setTotalPrice(transferDO.getAmount());
        Date now = new Date();
        tradeOrderDO.setCreateTime(now);
        tradeOrderDO.setUpdateTime(now);
        tradeOrderDO.setSuccessTime(now);
        tradeOrderDO.setTotalPrice(transferDO.getAmount());
        if (transferDomainRequest.getHolidayServiceFee().compareTo(BigDecimal.ZERO) > 0) {
            Map<String, Object> noteMap = new HashMap<>();
            noteMap.put("holidayFee", transferDomainRequest.getHolidayServiceFee());
            noteMap.put("driverHolidayFee", transferDomainRequest.getDriverRecHoliServiceFee());
            tradeOrderDO.setNote(JsonMapper.toJson(noteMap));
        }

        // 账户操作
        accountDeal(transferDO);
        //入库,可能到账金额为0
        List<TradeAmountDetailsModel> amountList = new ArrayList<>();
        if (transferDO.getAmount().compareTo(BigDecimal.ZERO)>0){
            amountList = tradeAmountDetailsRepository.save(transferDO, tradeOrderDO.getId());
        }
        TradeOrderModel tradeOrderModel = tradeOrderRepository.save(tradeOrderDO);
        //双写
        doubleWrite(tradeOrderDO);

        //二清
        // key 支付订单id value 原订单支付时间和剩余金额
        BigDecimal remainingAllAmount = liquidationSend(transferDomainRequest, tradeOrderDO, taxiAdvance, tradeOrderModel, advanceOrderDO, orderDO);
        log.info("payTradeNo={} remainingAllAmount={}", tradeOrderDO.getPayTradeNo(), remainingAllAmount);

        //thea+
        theaSend(transferDomainRequest, transferDO, taxiAdvance, amountList, tradeOrderModel, orderDO);

        // 高速费是立即到账，退款后还会到账
        // 补偿金到账金额可能公司会垫一部分，可能退款后到账，校验没有意义
        if (!StringUtils.equals(tradeOrderDO.getBusinessType(),TBusinessEnum.compensation_fee.name())){
            // 到账金额 （顺风车到账金额不包含服务费）
            BigDecimal allAmount = transferDO.getAmount();
            if(StringUtils.equals(TBusinessEnum.carpool_fee.name(),tradeOrderDO.getBusinessType())){
                allAmount = allAmount.add(transferDomainRequest.getFinalServiceFee());
            }
            //高速费支付到账同时触发，有可能退款业务侧没办法拦截住，所以退款后支持到账，用支付金额做校验
            if (StringUtils.equals(tradeOrderDO.getBusinessType(),TBusinessEnum.carpool_highway_fee.name())){
                remainingAllAmount = orderDO.getTotalPrice();
            }
            //allAmount 表示请求到账金额
            //remainingAllAmount 表示剩余可到账金额（退款/免单等金额不给用户到账）
            log.info("payTradeNo={} transferDO.getAmount()={} allAmount={} remainingAllAmount={}", tradeOrderDO.getPayTradeNo(), transferDO.getAmount(), allAmount, remainingAllAmount);
            if (allAmount.compareTo(remainingAllAmount) > 0 ){
                log.error("{}:{} {} 转账金额错误", tradeOrderDO.getPayTradeNo(), allAmount, remainingAllAmount);
                throw new DException(REMITT_AMOUNT_ERROR, OrderErrorCode.TEMPLATE_AMOUNT_ERROR, allAmount, remainingAllAmount);
            }
        }

        //风控检测
        detectTransfer(transferDO, orderDO, now);

//        commonResultDO.setT(new TransferResultEntity(tradeOrderDO.getTotalPrice(),tradeOrderDO.getId(),now))
        commonResultDO.setT(new TransferResultEntity(tradeOrderDO.getTotalPrice(),tradeOrderDO.getId(),now,new CalculateOrderDO(amountList),tradeOrderDO.getBillId()))
                .setTradeOrderId(tradeOrderDO.getId());
        return commonResultDO;
    }

    private void detectTransfer(TransferDO transferDO, TradeOrderDO orderDO, Date now) {
        try {
            if ("false".equals(LoadPropertyUtil.getProperty("paymozi.detectTransfer", "false"))) {
                return;
            }
            SharedThreadPool.getSharedTpe().submit(() -> {
                Routing routing = new Routing().setLookupId(routingThriftService.getLookupKeyById(String.valueOf(transferDO.getPayTradeNo())));
                DynamicDataSourceContextHolder.setDataSourceType(DatabaseTypeEnum.MASTER, DatabaseEnum.order, routing);

                List<TradeAmountDetailsModel> tradeAmountDetailsModelList = tradeAmountDetailsModelService.selectByTradeOrderId(orderDO.getId(), orderDO.getUserId());
                TradeAmountDetailsMultiple tradeAmountDetails = new TradeAmountDetailsMultiple(tradeAmountDetailsModelList);
                MoziTransferContext moziContext = new MoziTransferContext();
                moziContext.setTradeOrderId(orderDO.getId());
                moziContext.setPayOutUserId(orderDO.getUserId());
                moziContext.setPayInUserId(transferDO.getUserId());
                moziContext.setBusinessType(orderDO.getBusinessType());
                moziContext.setSource(orderDO.getSource());
                moziContext.setThirdAmount(tradeAmountDetails.getThird());
                moziContext.setCouponAmount(tradeAmountDetails.getCoupon());
                moziContext.setBalanceAmount(tradeAmountDetails.getPassenger());
                moziContext.setPassengerSubsidy(tradeAmountDetails.getPassengerSubsidy());
                moziContext.setMajorProductId(orderDO.getMajorProductId());
                moziContext.setAmount(transferDO.getAmount());
                moziContext.setTransferBusinessType(transferDO.getBusinessType());
                moziContext.setPayCreateTime(orderDO.getCreateTime().getTime());
                moziContext.setPaySuccessTime(orderDO.getSuccessTime().getTime());
                moziContext.setTransferTime(now.getTime());

                List<TradeProductDetailsModel> productFees = tradeProductDetailsModelService.selectByOrderId(orderDO.getId(), orderDO.getUserId());
                if (CollectionUtils.isEmpty(productFees)) {
                    moziContext.setThanksFee(BigDecimal.ZERO);
                    moziContext.setRideFee(BigDecimal.ZERO);
                } else {
                    //感谢费
                    moziContext.setThanksFee(productFees.stream().filter(productFee -> productFee.getType().equals("extraFee")).findFirst().map(TradeProductDetailsModel::getPrice).orElse(BigDecimal.ZERO));
                    //行程费
                    moziContext.setRideFee(productFees.stream().filter(productFee -> productFee.getType().equals("ride")).findFirst().map(TradeProductDetailsModel::getPrice).orElse(BigDecimal.ZERO));
                }
                //高付费
                List<TradeOrderModel> tradeOrderModels = tradeOrderRepository.querySuccessByMajorProductId(orderDO.getMajorProductId(), TradeTypeEnum.payment.name(), TBusinessEnum.carpool_highway.name(), orderDO.getUserId());
                if (CollectionUtils.isEmpty(tradeOrderModels)) {
                    moziContext.setHighwayFee(BigDecimal.ZERO);
                } else {
                    moziContext.setHighwayFee(tradeOrderModels.stream().findFirst().map(TradeOrderModel::getTotalPrice).orElse(BigDecimal.ZERO));
                }
                moziService.detectTransfer(moziContext);
            });
        } catch (Exception ex) {
            //ignore ex
            log.warn("风控组织数据出错了", ex);
        }
    }

    private Long getPlantFreezeTime(Long plantFreezeTime, TradeOrderNote note) {
        if (plantFreezeTime <= 0) {
            return 0L;
        }
        if (!paymentPayAfterUseDomainService.isPauFlag(note.getPaymentWay())) {
            return 0L;
        }
        if (paymentPayAfterUseDomainService.isPauCutFlag(note.getPauCutFlag())) {
            return 0L;
        }
        return plantFreezeTime;
    }


    /**
     *
     * 计算金额
     * 发送二清消息
     *
     * @param transferDomainRequest
     * @param tradeOrderDO
     * @param taxiAdvance
     * @param tradeOrderModel
     * @param advanceOrderDO
     * @return 剩余可退总额(也就是剩余可到账总额)
     * @throws Exception
     */
    private BigDecimal liquidationSend(TransferDomainRequest transferDomainRequest, TradeOrderDO tradeOrderDO,
                                       BigDecimal taxiAdvance, TradeOrderModel tradeOrderModel, AdvanceOrderDO advanceOrderDO,
                                       TradeOrderDO paymentOrder) throws Exception {
        LogProxy.log2(() -> "transferDomainRequest={} tradeOrderDO={} taxiAdvance={} tradeOrderModel={} advanceOrderDO={}"
                , () -> new Object[]{JsonMapper.toJson(transferDomainRequest), JsonMapper.toJson(tradeOrderDO), taxiAdvance, JsonMapper.toJson(tradeOrderModel), JsonMapper.toJson(advanceOrderDO)});
        BigDecimal remainingAllAmount = BigDecimal.ZERO;
        //剩余可退金额
        Map<Long,TradeOrderDO> remainingMap = remainingAmount(tradeOrderDO.getBusinessType(),tradeOrderDO.getPayTradeNo(), transferDomainRequest.getPayOrderIdList(), transferDomainRequest.getPayUserId(),transferDomainRequest.getExistFree());
        BigDecimal transferAmount = StringUtils.equalsAny(tradeOrderDO.getBusinessType(), TBusinessEnum.taxi_fee.name(), TBusinessEnum.taxipooling_fee.name())?tradeOrderDO.getTotalPrice().subtract(transferDomainRequest.getServiceFee()):tradeOrderDO.getTotalPrice();
        log.debug("liquidationSend.transferAmount={}", transferAmount);
        // 垫付后到账金额为0
        if (taxiAdvance.compareTo(BigDecimal.ZERO) > 0){
            transferAmount = BigDecimal.ZERO;
        }
        int size = remainingMap.size();
        for (Map.Entry<Long,TradeOrderDO> entry : remainingMap.entrySet()){
            BigDecimal remainingAmount = entry.getValue().getTotalPrice();
            remainingAllAmount = remainingAllAmount.add(remainingAmount);
            // 当前订单还有到账金额，补偿金到账有可能已经全部退回特殊处理(补偿金最后多于金额公司垫付，全部放到最后一单上)
            size--;
            boolean platCompensateAmountFlag = transferAmount.compareTo(BigDecimal.ZERO) > 0 && size == 0
                    && StringUtils.equals(TBusinessEnum.compensation_fee.name(),tradeOrderModel.getBusinessType());
            if (remainingAmount.compareTo(BigDecimal.ZERO) > 0 || platCompensateAmountFlag ){
                BigDecimal serviceFee = BigDecimal.ZERO;
                // 服务费和放到主订单上
                if (tradeOrderDO.getPayTradeNo().equals(entry.getKey())){
                    serviceFee = transferDomainRequest.getFinalServiceFee();
                }
                BigDecimal driverFee = transferAmount.compareTo(remainingAmount) > 0 ? remainingAmount : transferAmount;
                transferAmount = transferAmount.subtract(driverFee);
                //发送到账二清消息
                TransferWithOrderLiquidationMessage message = new TransferWithOrderLiquidationMessage();
                if (platCompensateAmountFlag){
                    message.setPlatCompensateAmount(transferAmount);
                }
                message.setDriverFee(driverFee)
                        .setTaxiadvanceAmount(taxiAdvance)
                        .setServiceFee(serviceFee)
                        .setPayData(entry.getValue())
                        .setTransferData(tradeOrderDO)
                        .setMajorOrderId(tradeOrderDO.getPayTradeNo())
                        .setAfterTrade(messageContext -> {
                            if (advanceOrderDO != null) {
                                messageContext.setCarpoolPreSettleAmount(AmountConvertUtil.yuan2penny(advanceOrderDO.getAdvanceAmount()));
                            }
                            if (TBusinessEnum.t_scan.name().equals(paymentOrder.getBusinessType())) {
                                //扫码的乘客id记录在拓展字段中
                                TradeOrderNote tradeOrderNote = TradeOrderNote.create(paymentOrder.getNote());
                                ScanExtraNote scanExtraNote = JsonMapper.json2Bean(tradeOrderNote.getExtra(), ScanExtraNote.class);
                                messageContext.setPayOutUserId(String.valueOf(scanExtraNote.getPassengerId()));
                            }
                        })
                        .send();
            }
            log.debug("liquidationSend,entry.getValue().getId():{}, tradeOrderDO.getPayTradeNo():{}", entry.getValue().getId(), tradeOrderDO.getPayTradeNo());
            if (entry.getValue().getId().equals(tradeOrderDO.getPayTradeNo()) && transferDomainRequest.getAutoCheatComplainFlag() != null && transferDomainRequest.getAutoCheatComplainFlag()) {
                //作弊单场景，营销金额不给退款
                ConficateLiquidationMessage conficateLiquidationMessage = new ConficateLiquidationMessage();
                TradeOrderDO tradeOrderDOTmp = new TradeOrderDO();
                BeanUtils.copyProperties(entry.getValue(), tradeOrderDOTmp);
                log.debug("liquidationSend:tradeOrderDOTmp:{}", JsonMapper.toJson(tradeOrderDOTmp));
                tradeOrderDOTmp.setTradeType("refund");
                tradeOrderDOTmp.setBusinessType("carpool_fee");
                conficateLiquidationMessage.setTradeOrderDO(tradeOrderDOTmp)
                        .setAfterTrade(message->{
                            message.setBalance(0);
                            message.setMarketBalance(AmountConvertUtil.yuan2penny(transferDomainRequest.getCouponRecover()
                                    .add(transferDomainRequest.getBonusRecover())
                                    .add(BigDecimalUtil.zeroIfNull(transferDomainRequest.getPassengerSubsidyRecover()))
                            ));
                        })
                        .send();
            }
        }
        return remainingAllAmount;
    }

    /**
     * 发送thea+ 消息
     */
    private void theaSend(TransferDomainRequest transferDomainRequest, TransferDO transferDO, BigDecimal taxiAdvance,
                          List<TradeAmountDetailsModel> amountList, TradeOrderModel tradeOrderModel,
                          TradeOrderDO paymentOrder) {
        CommonTradeOrderTheaMessage commonTradeOrderTheaMessage = new CommonTradeOrderTheaMessage();
        commonTradeOrderTheaMessage.setTradeOrder(tradeOrderModel)
                .setAmountDetails(amountList)
                .setAfterTrade(theaMessageContext -> {
                    Map<String, Object> financeInfo = theaMessageContext.getFinanceInfo();
                    if (CollectionUtils.isEmpty(financeInfo)){financeInfo = new HashMap<>();}
                    if (StringUtils.equalsAny(tradeOrderModel.getBusinessType(), TBusinessEnum.taxi_fee.name(), TBusinessEnum.taxipooling_fee.name())){
                        BigDecimal tradeAmount = tradeOrderModel.getTotalPrice().subtract(transferDomainRequest.getServiceFee());
                        if (taxiAdvance.compareTo(BigDecimal.ZERO) > 0){
                            financeInfo.put(TheaExtraInfoKeyEnum.company_advance.name(), taxiAdvance);
                            tradeAmount = BigDecimal.ZERO;
                        }
                        theaMessageContext.setWalletPay(tradeAmount)
                                .setOrderPrice(tradeAmount);

                    }
                    // 车费到账添加车费信息
                    if (transferDomainRequest.getSumReceivable().compareTo(BigDecimal.ZERO) > 0){
                        financeInfo.put(TheaExtraInfoKeyEnum.service_fee.name(),transferDomainRequest.getServiceFee());
                        financeInfo.put(TheaExtraInfoKeyEnum.sum_receivable.name(),transferDomainRequest.getSumReceivable());
                        financeInfo.put(TheaExtraInfoKeyEnum.final_servicefee.name(),transferDomainRequest.getFinalServiceFee());
                        financeInfo.put(TheaExtraInfoKeyEnum.final_receivable.name(),transferDomainRequest.getFinalReceivable());
                    }
                    // 补偿金到账添加公司成本
                    if (StringUtils.equals(TBusinessEnum.compensation_fee.name(),tradeOrderModel.getBusinessType())){
                        financeInfo.put(TheaExtraInfoKeyEnum.company_accept.name(),transferDO.getAmount().subtract(transferDomainRequest.getFrozenAmount()));
                    }
                    if (!CollectionUtils.isEmpty(transferDomainRequest.getPayOrderIdList())) {
                        financeInfo.put(TheaExtraInfoKeyEnum.transfer_list.name(),transferDomainRequest.getPayOrderIdList());
                    }
                    if (transferDomainRequest.getAutoCheatComplainFlag() != null && transferDomainRequest.getAutoCheatComplainFlag()) {
                        financeInfo.put(TheaExtraInfoKeyEnum.recover_bonus.name(), transferDomainRequest.getBonusRecover());
                        financeInfo.put(TheaExtraInfoKeyEnum.recover_coupon.name(), transferDomainRequest.getCouponRecover());
                        financeInfo.put("passenger_subsidy",transferDomainRequest.getPassengerSubsidyRecover());
                    }
                    financeInfo.put(TheaExtraInfoKeyEnum.latest_ride_id.name(),transferDomainRequest.getTradeOrderDO().getMajorProductId());
                    theaMessageContext.setFinanceInfo(financeInfo);
                })
                .send();
    }


    public boolean checkTransfer(String productId,String businessType,Long userId){
        List<TradeOrderDO> tradeOrderDos = tradeOrderRepository.queryTradeOrderList(productId, TradeTypeEnum.transfer.name(), businessType, userId);
        return !CollectionUtils.isEmpty(tradeOrderDos);
    }

    private void accountDeal(TransferDO transferDO){
        //顺风金账户
        if (transferDO.getBonus() != null && transferDO.getBonus().compareTo(BigDecimal.ZERO) > 0){
            InnerAccountChangeItem innerAccountChangeItem = new InnerAccountChangeItem();
            innerAccountChangeItem.setTradeType(TradeOrderTypeEnum.transfer.name());
            innerAccountChangeItem.setUserId(transferDO.getUserId());
            innerAccountChangeItem.setBusinessType(transferDO.getBusinessType());
            innerAccountChangeItem.setOrderId(transferDO.getOrderId());
            innerAccountChangeItem.setAmount(transferDO.getBonus());
            innerAccountChangeItem.setComment(transferDO.getComment());
            innerAccountChangeItem.setSysopId(transferDO.getSysopId());
            innerAccountChangeItem.setAccountType(TOrderAccountTypeEnum.bonus.name());
            AccountChangeUtil.accountIncome(innerAccountChangeItem);
        }

        if (transferDO.getAmount().compareTo(BigDecimal.ZERO) > 0){
            InnerAccountChangeItem innerAccountChangeItem = new InnerAccountChangeItem();
            innerAccountChangeItem.setAccountType(transferDO.getAccountType());
            innerAccountChangeItem.setBusinessType(transferDO.getBusinessType());
            innerAccountChangeItem.setTradeType(TradeOrderTypeEnum.transfer.name());
            innerAccountChangeItem.setUserId(transferDO.getUserId());
            innerAccountChangeItem.setCompanyId(transferDO.getCompanyId());
            innerAccountChangeItem.setOrderId(transferDO.getOrderId());
            innerAccountChangeItem.setPayTradeNO(transferDO.getPayTradeNo() == null || transferDO.getPayTradeNo() <= 0 ? transferDO.getOrderId():transferDO.getPayTradeNo());
            innerAccountChangeItem.setAmount(transferDO.getAmount());
            innerAccountChangeItem.setRemarks(transferDO.getRemarks());
            innerAccountChangeItem.setDetails(transferDO.getDetails());
            innerAccountChangeItem.setComment(transferDO.getComment());
            innerAccountChangeItem.setSysopId(transferDO.getSysopId());
            innerAccountChangeItem.setTag(transferDO.getTransferTag());
            innerAccountChangeItem.setPlantFreezeTime(transferDO.getPlantFreezeTime());
            innerAccountChangeItem.setOrderSource(transferDO.getOrderSource());
            innerAccountChangeItem.setFreezeReason(transferDO.getFreezeReason());
            innerAccountChangeItem.setCommissionFee(transferDO.getServiceFee());
            AccountChangeUtil.accountIncome(innerAccountChangeItem);
        }
    }

    /**
     * 获取当前订单到账相关信息
     *  剩余可到账金额 = 支付-退款-免单
     */
    private Map<Long,TradeOrderDO> remainingAmount(String businessType,Long majorOrderId, List<Long> orderIdList,Long userId,Boolean existFree) {
        LogProxy.log2(() -> "remainingAmount. businessType={} majorOrderId={} orderIdList={} userId={} existFree={}",
                () -> new Object[]{businessType, majorOrderId, JsonMapper.toJson(orderIdList), userId, existFree});
        //查询用户支付金额
        List<TradeOrderDO> payTradeList = queryPayTradeOrderResult(orderIdList, userId);
        LogProxy.log2(() -> "remainingAmount.queryPayTradeOrderResult={}", () -> new Object[]{JsonMapper.toJson(payTradeList)});
        // 获取免单金额
        Map<Long, BigDecimal> freeMap = queryFreeResultMap(businessType, majorOrderId, orderIdList, existFree);
        LogProxy.log2(() -> "remainingAmount.queryFreeResultMap={}", () -> new Object[]{JsonMapper.toJson(freeMap)});
        //查询退款结果
        Map<Long, BigDecimal> refundMap = queryRefundResult(orderIdList, userId);
        LogProxy.log2(() -> "remainingAmount.queryRefundResult={}", () -> new Object[]{JsonMapper.toJson(refundMap)});

        // 获取未退款的券包金额
        BigDecimal couponPackPrice = calculateOrderDomainService.getCouponPackPrice(majorOrderId,userId);
        LogProxy.log2(() -> "remainingAmount.getCouponPackPrice majorOrderId={} userId={} couponPackPrice={}", () -> new Object[]{majorOrderId, userId, couponPackPrice});

        // 限制校验，支付金额 - 已经退款金额 = 剩余金额
        Map<Long, TradeOrderDO> payMap = new LinkedHashMap<>();
        for (TradeOrderDO tradeOrderDO : payTradeList) {
            if (!StringUtils.equals(TradeOrderStatusEnum.success.name(), tradeOrderDO.getStatus())) {
                log.error("{}获取剩余金额失败 {}:{} 订单未支付", this.getClass(), tradeOrderDO.getId(), tradeOrderDO.getStatus());
                throw new DException(ORDER_NOTEXIT_ERROR);
            }
            //支付金额
            BigDecimal remainAmount = tradeOrderDO.getTotalPrice();
            //已经退款金额
            BigDecimal refundAmount = refundMap.get(tradeOrderDO.getId());
            //免单金额
            BigDecimal freeAmount = freeMap.get(tradeOrderDO.getId());
            //剩余可退金额 = 支付金额 - 已经退款金额 - 免单金额 - 券包金额
            remainAmount = remainAmount.subtract(refundAmount == null ? BigDecimal.ZERO : refundAmount)
                    .subtract(freeAmount == null ? BigDecimal.ZERO : freeAmount)
                    .subtract(tradeOrderDO.getId().equals(majorOrderId) ? couponPackPrice : BigDecimal.ZERO);
            tradeOrderDO.setTotalPrice(remainAmount);
            payMap.put(tradeOrderDO.getId(), tradeOrderDO);
        }
        LogProxy.log2(() -> "remainingAmount.remainingAmountResult={}", () -> new Object[]{JsonMapper.toJson(payMap)});
        return payMap;
    }

    /**
     * 查询每笔订单的退款金额
     * @param orderIdList
     * @param userId
     * @return
     */
    private Map<Long, BigDecimal> queryRefundResult(List<Long> orderIdList, Long userId) {
        List<TradeOrderDO> refundTradeList = tradeOrderRepository.queryBypayTradeNoList(orderIdList, Lists.newArrayList(TradeTypeEnum.refund.name()), userId);
        //退款数据结构转换成map
        Map<Long, BigDecimal> refundMap = new HashMap<>();
        for (TradeOrderDO tradeOrderDO : refundTradeList) {
            BigDecimal bigDecimal = refundMap.get(tradeOrderDO.getPayTradeNo());
            if (bigDecimal == null) {
                bigDecimal = BigDecimal.ZERO;
            }
            refundMap.put(tradeOrderDO.getPayTradeNo(), bigDecimal.add(tradeOrderDO.getTotalPrice()));
        }
        return refundMap;
    }

    /**
     * 查询用户支付
     * @param orderIdList
     * @param userId
     * @return
     */
    private List<TradeOrderDO> queryPayTradeOrderResult(List<Long> orderIdList, Long userId) {
        //获取支付交易单
        List<TradeOrderDO> payTradeList = tradeOrderRepository.queryByIds(orderIdList, userId);
        if (CollectionUtils.isEmpty(payTradeList) || payTradeList.size() < orderIdList.size()){
            log.error("{}获取剩余金额失败 {}订单不存在,",this.getClass(), orderIdList);
            throw new DException(NOT_FUND_PAY_ORDER_ERROR);
        }
        return payTradeList;
    }

    /**
     * 查询免单金额
     * @param businessType
     * @param majorOrderId
     * @param orderIdList
     * @param existFree
     * @return
     */
    private Map<Long, BigDecimal> queryFreeResultMap(String businessType, Long majorOrderId, List<Long> orderIdList, Boolean existFree) {
        Map<Long, BigDecimal> freeMap = new HashMap<>();
        if (!StringUtils.equalsAny(businessType, TBusinessEnum.carpool_highway_fee.name(), TBusinessEnum.taxi_fee.name(), TBusinessEnum.taxipooling_fee.name())) {
            List<RemittanceOrderModelDO> remittanceOrderModelDOs = remittanceOrderRepository.queryByBussinessTypeAndPayTradeNo(majorOrderId, CARPOOL_ORDER_FREE_BUSINESS_TYPE);
            for (RemittanceOrderModelDO remittanceOrderModelDO : remittanceOrderModelDOs) {
                freeMap.put(remittanceOrderModelDO.getPayTradeNo(), remittanceOrderModelDO.getSumFee());
            }
            //免单互斥，免单和到账存在并发，业务方没有办法判断到账和免单实际已经发生
            //TODO 这是什么意思？？？ 校验参数中传递过来的免单 和 实际是否免单 的一致
            if (!existFree.equals(!CollectionUtils.isEmpty(freeMap))) {
                log.error("{}实际免单和传参不一致 {},existFree={} freeMap={}", majorOrderId, JsonMapper.toJson(orderIdList), existFree, JsonMapper.toJson(freeMap));
                throw new DException(FREE_ERR);
            }
        }
        return freeMap;
    }


    private void doubleWrite(TradeOrderDO tradeOrderDO ){
        if (!StringUtils.equalsAny(tradeOrderDO.getBusinessType(),TBusinessEnum.carpool_fee.name(),TBusinessEnum.taxi_fee.name(),TBusinessEnum.taxipooling_fee.name())){
            return;
        }
        String dwcParam = null;
        if (tradeOrderDO.getBusinessType().equals(TBusinessEnum.carpool_fee.name())){
            CarpoolTransferParam carpoolTransferParam = new CarpoolTransferParam();
            carpoolTransferParam.setOrderId(tradeOrderDO.getPayTradeNo());
            dwcParam = JsonMapper.toJson(carpoolTransferParam);
        }else {
            TaxiTransferParam taxiTransferParam = new TaxiTransferParam();
            taxiTransferParam.setTaxiOrderId(tradeOrderDO.getPayTradeNo());
            taxiTransferParam.setTaxiRideId(Long.parseLong(tradeOrderDO.getMajorProductId()));
            dwcParam = JsonMapper.toJson(taxiTransferParam);
        }


        DoubleWriteCheckParam doubleWriteCheckParam = new DoubleWriteCheckParam();
        doubleWriteCheckParam.setBusinessType(tradeOrderDO.getBusinessType());
        doubleWriteCheckParam.setSource(tradeOrderDO.getSource());
        doubleWriteCheckParam.setTradeType(tradeOrderDO.getTradeType());
        doubleWriteCheckParam.setDwcParam(dwcParam);
        doubleWriteCheckParam.setCheckFlag(false);
        doubleWriteCheckParam.setRouteId(tradeOrderDO.getMajorProductId() + "");

        RocketMqSendUtils.doubleWrite(doubleWriteCheckParam);
    }

    private void doubleWriteV2(TransferDO transferDO,TradeOrderDO tradeOrderDO ){
        RechargeParam rechargeParam = new RechargeParam();
        rechargeParam.setProductType(transferDO.getAccountType());
        rechargeParam.setUserId(transferDO.getUserId().intValue());
        rechargeParam.setHisId(tradeOrderDO.getId());
        rechargeParam.setSourceCid(tradeOrderDO.getSource());
        rechargeParam.setBusinessOrderId(tradeOrderDO.getMajorProductId());
        rechargeParam.setSysOpId(transferDO.getSysopId());
        rechargeParam.setPrice(AmountConvertUtil.yuan2penny(transferDO.getAmount()));
        rechargeParam.setComment(tradeOrderDO.getOrderDesc());
        InnerAccountChangeItem innerAccountChangeItem = new InnerAccountChangeItem();
        innerAccountChangeItem.setAccountType(transferDO.getAccountType());
        innerAccountChangeItem.setBusinessType(transferDO.getBusinessType());
        innerAccountChangeItem.setTradeType(TradeOrderTypeEnum.transfer.name());
        rechargeParam.setSubChangeType(AccountChangeUtil.getSubchangeType(innerAccountChangeItem));
        Map<String,String> extraMap = new HashMap<>();
        extraMap.put("liquidation","1");
        rechargeParam.setExtra3(JsonMapper.toJson(extraMap));


        DoubleWriteCheckParam doubleWriteCheckParam = new DoubleWriteCheckParam();
        doubleWriteCheckParam.setBusinessType("common_transfer");
        doubleWriteCheckParam.setSource("new_pay");
        doubleWriteCheckParam.setTradeType(tradeOrderDO.getTradeType());
        doubleWriteCheckParam.setDwcParam(JsonMapper.toJson(rechargeParam));
        doubleWriteCheckParam.setCheckFlag(false);
        doubleWriteCheckParam.setRouteId(tradeOrderDO.getId() + "");

        RocketMqSendUtils.doubleWrite(doubleWriteCheckParam);
    }

    private void commonBusinessCheckAndProcess(TradeOrderDO tradeOrderDO, Long payUserId, TransferDO transferDO) {
        //高速费垫付 需要查询一下是否有支付记录
        if (TBusinessEnum.c_highway_advance.name().equals(tradeOrderDO.getBusinessType())) {
            TradeOrderModel tradeOrderModels = tradeOrderRepository.queryOneSuccessByMajorProductIdWithLock(tradeOrderDO.getMajorProductId(), TradeTypeEnum.payment.name(), TBusinessEnum.carpool_highway.name(), payUserId);
            if (tradeOrderModels != null) {
                //乘客已支付高速费，不用发起垫付
                log.error("乘客已支付高速费，不用发起垫付");
                throw new DException(TradeErrorCode.PAYMENT_RECEIVED_ERROR);
            }
        }
        //站点拼车奖励回收businessType转换
        if (StringUtils.equals(TBusinessEnum.carpool_regular.name(), tradeOrderDO.getBusinessType())) {
            TradeOrderModel payTradeOrderModel = tradeOrderRepository.queryById(tradeOrderDO.getPayTradeNo(), payUserId);
            if (payTradeOrderModel != null
                    && StringUtils.equals(TBusinessEnum.carpool_station.name(), payTradeOrderModel.getBusinessType())) {
                transferDO.setBusinessType(TBusinessEnum.carpool_station_award_fee.name());
                tradeOrderDO.setBusinessType(TBusinessEnum.carpool_station_award_fee.name());
                log.info("transfer 站点拼车奖励回收businessType转换. carpool_regular->carpool_station_award_fee.payTradeNo:{}", tradeOrderDO.getPayTradeNo());
            }
        }
    }

    public AdvanceOrderDO businessCheckAndProcess(TradeOrderDO tradeOrderDO, TradeOrderDO payOrderDO) {
        //高速费到账 需要查询一下是否有垫付记录，金额是否正确
        AdvanceOrderDO advanceOrderDO = null;
        if (TBusinessEnum.carpool_highway_fee.name().equals(tradeOrderDO.getBusinessType())) {
            TradeOrderModel tradeOrderModels = tradeOrderRepository.queryOneSuccessByMajorProductIdWithLock(tradeOrderDO.getMajorProductId(), TradeTypeEnum.transfer.name(), TBusinessEnum.c_highway_advance.name(), tradeOrderDO.getUserId());
            if (tradeOrderModels == null) {
                return advanceOrderDO;
            }
            Long count = tradeOrderRepository.queryCountByPayTradeNoAndBusinessType(tradeOrderModels.getId(), TradeTypeEnum.confiscate.name(), TBusinessEnum.c_highway_advance.name(), tradeOrderDO.getUserId());
            if (count > 0) {
                //有垫付 但也有撤销记录，等于没有垫付
                return advanceOrderDO;
            }
            if (tradeOrderModels.getTotalPrice().add(tradeOrderDO.getTotalPrice()).compareTo(payOrderDO.getTotalPrice()) > 0) {
                log.error("到账金额超过限制，请重新发起");
                throw new DException(TradeErrorCode.REMITT_AMOUNT_ERROR);
            }
            advanceOrderDO = new AdvanceOrderDO();
            advanceOrderDO.setAdvanceOrderId(tradeOrderModels.getId());
            advanceOrderDO.setAdvanceAmount( tradeOrderModels.getTotalPrice());

        }
        TradeOrderNote note = TradeOrderNote.create(payOrderDO.getNote());
        //挂起校验到账埋点
        boolean oldHoldStatus = note.getHold() != null && note.getHold();
        if (oldHoldStatus) {
            //挂起状态不应该到账，添加埋点
            TradeMetric.focus("hold_after_transfer", "error");
            log.warn("挂起状态，不应该到账. MajorProductId:{}", payOrderDO.getMajorProductId());
        }
        return advanceOrderDO;
    }

    @Transactional
    public void sharding(SharingElementBO sharingElementBO) {
        sharingElementBO.accept(sharingActionVisitor);
        sharingElementBO.accept(billOrderMappingVisitor);
    }
}
