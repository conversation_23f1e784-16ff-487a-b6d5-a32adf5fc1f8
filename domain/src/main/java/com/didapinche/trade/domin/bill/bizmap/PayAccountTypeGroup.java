package com.didapinche.trade.domin.bill.bizmap;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.didapinche.trade.infrastructure.tbl.TradeAmountDetailsModel;
import com.didapinche.trade.infrastructure.util.BigDecimalUtil;
import com.didapinche.trade.thrift.entities.TOrderAmountDetails;
import com.didapinche.trade.thrift.enums.TOrderAccountTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class PayAccountTypeGroup {

    public final static String accountPay = "accountPay";//账户支付,三方账户
    public final static String subsidies = "subsidies";//优惠补贴

    @ApolloJsonValue("${pay.account.type.group:{third:accountPay,passenger:accountPay,passenger_not_withdraw:accountPay,taxi:accountPay,driver:accountPay,c_enterprise_vip:accountPay,t_enterprise:accountPay,t_enterprise_vip:accountPay,offline:accountPay,coupon:subsidies,recover_coupon:subsidies,bonus:subsidies,recover_bonus:subsidies,reduction:subsidies,highway_subsidy:subsidies,recover_highway_subsidy:subsidies,passenger_subsidy:subsidies,recover_passenger_subsidy:subsidies}}")
    private Map<String,String> payAccountTypeMap = new HashMap<>();

    public String getAccountGroup(String accountType){
        return payAccountTypeMap.getOrDefault(accountType,accountPay);
    }

    public Map<String, BigDecimal> splitOrderAmount(List<TradeAmountDetailsModel> orderAmountDetails) {
        if(CollectionUtils.isEmpty(orderAmountDetails)){
            return MapUtils.EMPTY_MAP;
        }
        Map<String, BigDecimal> groupedSumMap = orderAmountDetails.stream()
                .collect(Collectors.groupingBy(
                        detail -> getAccountGroup(detail.getType()),
                        Collectors.reducing(BigDecimal.ZERO, TradeAmountDetailsModel::getAmount, BigDecimal::add)
                ));
        return groupedSumMap;
    }
}
