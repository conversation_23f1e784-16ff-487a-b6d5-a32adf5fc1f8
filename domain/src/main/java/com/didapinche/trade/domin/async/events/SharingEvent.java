package com.didapinche.trade.domin.async.events;

import com.didapinche.payment.async.localmsg.core.config.DTCConfig;
import com.didapinche.payment.async.localmsg.rpc.ActorEvent;
import com.didapinche.trade.domin.async.custom.SharingEventCustom;
import com.didapinche.trade.domin.async.datas.SharingData;

@DTCConfig(value = SharingEventCustom.class)
public class SharingEvent extends ActorEvent<SharingData> {

}
