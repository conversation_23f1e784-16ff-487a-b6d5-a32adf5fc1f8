package com.didapinche.trade.domin.payment;

import com.didapinche.agaue.common.exception.DException;
import com.didapinche.finance.mq.context.enums.TradeTypeEnum;
import com.didapinche.trade.infrastructure.tbl.TradeOrderModel;
import com.didapinche.trade.thrift.enums.TBusinessEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.didapinche.trade.infrastructure.exception.TradeErrorCode.PARAM_ERROR;

@Service
public class QueryPaymentTradeDomainService extends AbstractDomainService{


    /**
     * 根据行程号查询支付流水信息
     * @param majorProductId 行程号
     * @param userId 用户id
     * @return
     */
    public List<TradeOrderModel> queryTradedOrderListByMajorId(String majorProductId, Long userId, String tradeType, String businessType) {
        if (StringUtils.isAnyEmpty(majorProductId, tradeType, businessType) || userId == null || userId == 0) {
            throw new DException(PARAM_ERROR);
        }
//        TradeTypeEnum.payment.name(), TBusinessEnum.carpool.name(),
        return super.queryTradedOrderList(majorProductId, tradeType, businessType, userId);
    }
}
