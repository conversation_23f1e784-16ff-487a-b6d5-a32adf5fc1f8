package com.didapinche.trade.domin.liquidation.message;

import com.didapinche.agaue.tools.convert.AmountConvertUtil;
import com.didapinche.liquidate.common.entity.LiquidationPaymentEntity;
import com.didapinche.server.commons.common.util.DateUtil;
import com.didapinche.trade.domin.bean_new.TradeAmountDetailsMultiple;
import com.didapinche.trade.infrastructure.entities.TradeOrderNote;
import com.didapinche.trade.infrastructure.enums.GoodsTypeEnum;
import com.didapinche.trade.infrastructure.tbl.TradeOrderModel;
import com.didapinche.trade.infrastructure.tbl.TradeProductDetailsModel;
import com.didapinche.trade.infrastructure.util.BigDecimalUtil;
import com.didapinche.trade.thrift.enums.TBusinessEnum;
import com.didapinche.trade.thrift.enums.TTradeProductTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @Date 2022/9/21 9:34
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class PaymentLiquidationMessage extends BaseLiquidationMessage {

    private TradeOrderModel tradeOrderModel;
    private List<TradeProductDetailsModel> tradeProductDetailsModels;
    private TradeAmountDetailsMultiple tradeAmountDetails;

    @Override
    public String businessType() {
        return tradeOrderModel.getBusinessType();
    }

    @Override
    public String tradeType() {
        return tradeOrderModel.getTradeType();
    }

    @Override
    public LiquidationPaymentEntity buildLiquidationMessage() throws Exception {
        LiquidationPaymentEntity messageContext = new LiquidationPaymentEntity();
        messageContext.setSuffixTime(DateUtil.date2String(tradeOrderModel.getSuccessTime()));
        messageContext.setBusinessTime(DateUtil.date2String(tradeOrderModel.getSuccessTime()));
        messageContext.setMoney(AmountConvertUtil.yuan2penny(tradeAmountDetails.getThird()));
        messageContext.setPayChannelAssignMerNo(TradeOrderNote.create(tradeOrderModel.getNote()).getMerchantId());
        messageContext.setPayChannelType(tradeOrderModel.getThirdChannel());
        messageContext.setPayChannelTranSeqNo(tradeOrderModel.getPaymentNo());
        messageContext.setMarketBalance(AmountConvertUtil.yuan2penny(tradeAmountDetails.getCoupon()
                .add(tradeAmountDetails.getBonus())
                .add(tradeAmountDetails.getHighwaySubsidy())
                .add(BigDecimalUtil.zeroIfNull(tradeAmountDetails.getPassengerSubsidy()))
        ));
        BigDecimal goodsPrice = BigDecimal.ZERO;
        if (!TBusinessEnum.mall.name().equals(businessType()) && !TBusinessEnum.carpool_additional.name().equals(businessType())) {
            goodsPrice = tradeProductDetailsModels.stream()
                    .filter(Objects::nonNull)
                    .filter(product -> StringUtils.equalsAny(
                            product.getType(),
                            TTradeProductTypeEnum.couponPackage.name(),
                            TTradeProductTypeEnum.ensureFee.name()
                    ))
                    .map(TradeProductDetailsModel::getPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        } else {
            for (TradeProductDetailsModel tradeProductDetailsModel : tradeProductDetailsModels) {
                if (GoodsTypeEnum.ensureFee.name().equals(tradeProductDetailsModel.getType())) {
                    messageContext.setOrderType("goods_ensure_fee");
                    break;
                }
            }
        }
        messageContext.setGoodsPrice(AmountConvertUtil.yuan2penny(goodsPrice));
        messageContext.setBalance(AmountConvertUtil.yuan2penny(tradeAmountDetails.getPassenger()
                .add(tradeAmountDetails.getPassengerNotWithdraw())
                .add(tradeAmountDetails.getCEnterpriseVip())
                .add(tradeAmountDetails.getTEnterpriseVip())));
        messageContext.setTradeNo(String.valueOf(tradeOrderModel.getId()));
        messageContext.setTradeId(String.valueOf(tradeOrderModel.getId()));
        TradeOrderNote note = TradeOrderNote.create(tradeOrderModel.getNote());
        if (TBusinessEnum.carpool_addition.name().equals(tradeOrderModel.getBusinessType())) {
            //补单需要查到主单orderId
            messageContext.setBusinessNo(String.valueOf(note.getMajorOrderId()));
        } else {
            messageContext.setBusinessNo(String.valueOf(tradeOrderModel.getId()));
        }
        boolean enterpriseFlag = StringUtils.equalsAny(tradeOrderModel.getBusinessType(), TBusinessEnum.c_enterprise_vip.name(), TBusinessEnum.t_enterprise.name(), TBusinessEnum.t_enterprise_vip.name());
        if (enterpriseFlag) {
            //公司id
            messageContext.setPayOutUserId(String.valueOf(tradeOrderModel.getCompanyId()));
        } else {
            messageContext.setPayOutUserId(String.valueOf(tradeOrderModel.getUserId()));
        }
        if (this.getAfterTrade() != null) {
            this.getAfterTrade().accept(messageContext);
        }
        return messageContext;
    }
}
