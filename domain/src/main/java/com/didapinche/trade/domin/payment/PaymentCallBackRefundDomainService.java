package com.didapinche.trade.domin.payment;

import cn.hutool.core.util.NumberUtil;
import com.didapinche.agaue.tools.convert.AmountConvertUtil;
import com.didapinche.finance.mq.context.enums.PauCutFlagEnum;
import com.didapinche.finance.mq.context.enums.TheaExtraInfoKeyEnum;
import com.didapinche.trade.infrastructure.entities.PaySuccNotifyMessageContext;
import com.didapinche.finance.mq.context.message.messagecontext.RefundMessageContext;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.trade.domin.payment.pau.PaymentPayAfterUseDomainService;
import com.didapinche.trade.domin.thea.message.PaymentCallBackFailTheaMessage;
import com.didapinche.trade.infrastructure.entities.TradeOrderNote;
import com.didapinche.trade.infrastructure.enums.PaymentFailReasonEnum;
import com.didapinche.trade.infrastructure.enums.PaymentPauCutFlageEnum;
import com.didapinche.trade.infrastructure.enums.ThirdRefundSourceEnum;
import com.didapinche.trade.infrastructure.enums.order.TradeOrderStatusEnum;
import com.didapinche.trade.infrastructure.metric.TradeMetric;
import com.didapinche.trade.infrastructure.mq.RocketMqSendUtils;
import com.didapinche.trade.infrastructure.tbl.TradeAmountDetailsModel;
import com.didapinche.trade.infrastructure.tbl.TradeOrderModel;
import com.didapinche.trade.infrastructure.util.TradeDateUtil;
import com.didapinche.trade.thrift.enums.AmountReceiptStatusEnum;
import com.didapinche.trade.thrift.enums.TChannelEnum;
import com.didapinche.trade.thrift.enums.TOrderAccountTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2022/8/3
 */
@Service
@Slf4j
public class PaymentCallBackRefundDomainService extends AbstractDomainService {

    @Resource
    private PaymentPayAfterUseDomainService paymentPayAfterUseDomainService;

    @Transactional(rollbackFor = Exception.class)
    public void thirdRefund(Long userId, PaySuccNotifyMessageContext context, String paymentChannel, Date orderTime, Boolean doubleWrite,
                            TradeOrderNote tradeOrderNote, PaymentFailReasonEnum reasonEnum) throws Exception {
        payLogger.info("thirdRefund start. payTradeNo:{},reason:{}", context.getPayTradeNo(), reasonEnum.getReason());
        TradeMetric.focus("repayment", "warn");
        String payTradeNo = context.getPayTradeNo();
        BigDecimal paymentAmount = context.getPaymentAmount();
        boolean isPauOrder = paymentPayAfterUseDomainService.isPauFlag(tradeOrderNote.getPaymentWay());
        //改库
        TradeOrderModel tradeOrderModel = new TradeOrderModel();
        long tradeOrderId = NumberUtil.parseLong(payTradeNo);
        tradeOrderModel.setId(tradeOrderId);
        tradeOrderModel.setUserId(userId);
        tradeOrderModel.setStatus(TradeOrderStatusEnum.fail.name());
        tradeOrderModel.setPaymentNo(context.getOrderNo());
        tradeOrderModel.setThirdTradeNo(context.getThirdpartyOrderNo());
        Date now = new Date();
        if (isPauOrder) {
            tradeOrderNote.setPauCutFlag(PaymentPauCutFlageEnum.finish.name());
            tradeOrderNote.setPauFinishTime(TradeDateUtil.date2String(now));
            //更新账户金额
            TradeAmountDetailsModel tradeAmountDetailsModel = tradeAmountDetailsModelService.selectByTradeOrderIdAndType(tradeOrderId, TOrderAccountTypeEnum.third.name(), userId);
            TradeAmountDetailsModel updateModel = new TradeAmountDetailsModel();
            updateModel.setId(tradeAmountDetailsModel.getId());
            updateModel.setReceiptStatus(AmountReceiptStatusEnum.launched_cut.name());
            updateModel.setCutPayLaunchTime(now);
            tradeAmountDetailsModelService.updateById(updateModel);
        }
        tradeOrderNote.setMerchantId(context.getMerchantId());
        tradeOrderNote.setDiscountAmount(getDisCountAmount(tradeOrderId, context.getPaymentExtra(), tradeOrderModel.getThirdChannel()));
        tradeOrderModel.setNote(tradeOrderNote.setRefundReason(reasonEnum.getReason()).toJson());
        tradeOrderModel.setUpdateTime(now);
        updateTradeById(tradeOrderModel);
        tradeOrderModel = queryById(tradeOrderId, userId);
        sendRefundMq(reasonEnum, paymentAmount, tradeOrderModel);
        //thea消息
        PaymentCallBackFailTheaMessage message = new PaymentCallBackFailTheaMessage();
        message.setTradeOrder(tradeOrderModel)
                .setAfterTrade(messageContext -> {
                    if (isPauOrder) {
                        Map<String, Object> financeInfo = messageContext.getFinanceInfo();
                        if (CollectionUtils.isEmpty(financeInfo)) {
                            financeInfo = new HashMap<>();
                        }
                        financeInfo.put(TheaExtraInfoKeyEnum.pau_cut_flag.name(), PauCutFlagEnum.before.name());
                        messageContext.setFinanceInfo(financeInfo);
                    }
                    messageContext.setPayTime(context.getPayTime())
                            .setOrderPrice(context.getPaymentAmount())
                            .setThirdPay(context.getPaymentAmount());
                })
                .send();
        //双写 目前只有券包重复购买需要双写及发二清 如果后面还有新增 需要新增type进行区分
        if (Boolean.TRUE.equals(doubleWrite)) {
            doubleWriteDomainService.doubleWritePaymentCallback(context, tradeOrderModel, true, false);
        }
    }

    private static void sendRefundMq(PaymentFailReasonEnum reasonEnum, BigDecimal paymentAmount, TradeOrderModel tradeOrderModel) {
        RefundMessageContext messageContext = new RefundMessageContext();
        messageContext.setUserId(tradeOrderModel.getUserId());
        messageContext.setPayTradeNo(String.valueOf(tradeOrderModel.getId()));
        messageContext.setRefundAmount(paymentAmount);
        String outRefundNo = getOutRefundNo(String.valueOf(tradeOrderModel.getId()), null, ThirdRefundSourceEnum.CB.name());
        messageContext.setTradeNo(outRefundNo);
        messageContext.setRefundBusinessType(reasonEnum.getReason());
        messageContext.setChannelType(tradeOrderModel.getThirdChannel());
        messageContext.setOrderNo(tradeOrderModel.getPaymentNo());
        RocketMqSendUtils.paymentRefund(messageContext);
    }

    /**
     * 解析并获取三方折扣金额
     */
    private BigDecimal getDisCountAmount(Long tradeOrderId, String paymentExtra, String thirdChannel) {
        BigDecimal discountAmount = AmountConvertUtil.ZERO;
        try {
            if (TChannelEnum.alipay.name().equals(thirdChannel)) {
                Map<String, Object> paymentExtraMap = JsonMapper.json2Map(paymentExtra);
                if (paymentExtraMap != null && paymentExtraMap.get("otherInfo") != null) {
                    String otherInfo = paymentExtraMap.get("otherInfo").toString();
                    List<HashMap> voucherList = JsonMapper.json2List(otherInfo, HashMap.class);
                    if (voucherList != null) {
                        for (HashMap item : voucherList) {
                            discountAmount = discountAmount.add(AmountConvertUtil.yuan2BigDecimal(item.get("amount").toString()));
                        }
                    }
                }
            }
        } catch (Exception e) {
            payLogger.error("解析并获取三方折扣金额 error. tradeOrderId:{}, paymentExtra:{}, thirdChannel:{}",
                    tradeOrderId, paymentExtra, thirdChannel);
        }
        return discountAmount;
    }

    public static String getOutRefundNo(String outTradeNo, String outRefundNo, String refundSource) {
        String refundPrefix = "R" + (StringUtils.isBlank(refundSource) ? "" : refundSource);
        if (StringUtils.isBlank(outRefundNo)) {
            return (refundPrefix + outTradeNo).replace("-", "");
        } else {
            Matcher mtch = Pattern.compile("[A-z]+").matcher(outTradeNo);
            return (refundPrefix + (mtch.find() ? mtch.group() : "") + outRefundNo);
        }
    }
}
