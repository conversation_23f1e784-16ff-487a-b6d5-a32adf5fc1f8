package com.didapinche.trade.domin.transfer.taxiqr.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 21:00 2022/3/31
 **/
@Data
@Accessors(chain = true)
public class ConfirmProfitSharingRequestDO {

    /**
     * 乘客用户id
     *
     */
    private Long userId;
    /**
     * 主产品id
     *
     */
    private String majorProductId;
    /**
     * 支付订单id
     *
     */
    private Long tradeOrderId;
    /**
     * 司机用户id
     */
    private Long driverUserId;
    /**
     * 司机用户name
     */
    private String driverName;
    /**
     * 转账订单号 转账成功会返回该字段
     */
    private Long transferTradeOrderId;
    /**
     * 是否重试
     */
    private Boolean profitSharingConfirmRetry = Boolean.FALSE;
    private Integer reConsumeTimes;

    /**
     * 账单id
     */
    private Long billId;

}
