package com.didapinche.trade.domin.payment;

import com.didapinche.agaue.common.exception.DException;
import com.didapinche.agaue.tools.convert.AmountConvertUtil;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.server.commons.common.util.LoadPropertyUtil;
import com.didapinche.server.commons.common.util.RedisClusterUtil;
import com.didapinche.server.commons.mapper.didapinche.util.BaseConfigMapperUtil;
import com.didapinche.trade.domin.DO.check.CheckAccountDO;
import com.didapinche.trade.domin.bean_new.AccountInfoReq;
import com.didapinche.trade.domin.bean_new.AccountInfoRes;
import com.didapinche.trade.domin.bean_new.TradeAmountDetailsMultiple;
import com.didapinche.trade.infrastructure.apollo.BonusAccountUseAbleApolloService;
import com.didapinche.trade.infrastructure.apollo.UserAccountUseAbleApolloService;
import com.didapinche.trade.infrastructure.constants.RedisKeyConstants;
import com.didapinche.trade.infrastructure.entities.account.UserPayAccount;
import com.didapinche.trade.infrastructure.entities.enterprise.EnterpriseAccount;
import com.didapinche.trade.infrastructure.exception.TradeErrorCode;
import com.didapinche.trade.infrastructure.thrift.AccountReadThriftSupportService;
import com.didapinche.trade.infrastructure.thrift.EnterpriseAccReadWriteThriftSupportService;
import com.didapinche.trade.infrastructure.thrift.UserPermissionThriftSupportService;
import com.didapinche.trade.thrift.entities.PaymentRequest;
import com.didapinche.trade.thrift.entities.TOrderAmountDetails;
import com.didapinche.trade.thrift.enums.TOrderAccountTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/8
 */
@Service
@Slf4j
public class UserAccountDomainService extends AbstractDomainService {

    /**
     * 乘客账户
     */
    private static final int BALANCE_IDENTIFYING = 0;

    @Resource
    private AccountReadThriftSupportService accountReadThriftSupportService;
    @Resource
    private UserAccountUseAbleApolloService userAccountUseAbleApolloService;
    @Resource
    private BonusAccountUseAbleApolloService bonusAccountUseAbleApolloService;
    @Resource
    private UserPermissionThriftSupportService userPermissionThriftSupportService;
    @Resource
    private EnterpriseAccReadWriteThriftSupportService enterpriseAccReadWriteThriftSupportService;

    /**
     * 获取账户余额
     *
     * @param req -
     * @return -
     */
    public AccountInfoRes getAccountInfo(AccountInfoReq req) {
        AccountInfoRes accountInfoRes = new AccountInfoRes();
        accountInfoRes.setBalanceIdentifying(BALANCE_IDENTIFYING);
        if (userAccountUseAbleApolloService.match(req.getBusinessType(), req.getSource())) {
            UserPayAccount userPayAccount = accountReadThriftSupportService.getUserPayAccount(req.getUserId());
            if (userPayAccount.getRequestSuccess()) {
                accountInfoRes.setAccountBalance(userPayAccount.getUserBalance().add(userPayAccount.getDriverBalance()));
                accountInfoRes.setPassengerBalance(userPayAccount.getUserBalance());
                accountInfoRes.setPassengerNotWithdrawBalance(userPayAccount.getDriverBalance());
                accountInfoRes.setUseAbleAccountBalance(true);
            }
        }
        if (bonusAccountUseAbleApolloService.match(req.getBusinessType(), req.getSource())) {
            accountInfoRes.setBonusBalance(accountReadThriftSupportService.getUserBonusByUserId(req.getUserId()));
            Integer times = RedisClusterUtil.get(RedisKeyConstants.BONUS_USE_TIMES_KEY, req.getUserId(), Integer.class);
            if (times != null && times >= LoadPropertyUtil.getProperty("bonus.use.max", Integer.class, 10)) {
                accountInfoRes.setBonusUseMoreTen(1);
            }
        }
        return accountInfoRes;
    }

    /**
     * 账户使用检查
     *
     * @param checkAccountDO -
     */
    public void checkAccount(CheckAccountDO checkAccountDO) {
        String majorProductId = checkAccountDO.getMajorProductId();
        TradeAmountDetailsMultiple amountDetails = checkAccountDO.getAmountDetails();
        if (amountDetails.getPassenger().compareTo(BigDecimal.ZERO) > 0) {
            //是否支持余额支付
            if (!userAccountUseAbleApolloService.match(checkAccountDO.getBusinessType(), checkAccountDO.getSource())) {
                payLogger.error("checkPayment 不支持余额支付. userId:{}, majorProductId:{}", checkAccountDO.getUserId(), majorProductId);
                throw new DException(TradeErrorCode.NOT_SUPPORT_ACCOUNT_PAYMENT_ERROR);
            }
            //余额是否冻结
            if (userPermissionThriftSupportService.getUserAccountIsBanned(checkAccountDO.getUserId())) {
                payLogger.error("checkPayment 用户余额已被封禁. userId:{}, majorProductId:{}", checkAccountDO.getUserId(), majorProductId);
                throw new DException(TradeErrorCode.USER_ACCOUNT_BAN_ERROR);
            }
            //余额是否足够
            UserPayAccount userPayAccount = accountReadThriftSupportService.getUserPayAccountThrowException(checkAccountDO.getUserId());
            BigDecimal balance = AmountConvertUtil.toDecimalPlaces(userPayAccount.getUserBalance().add(userPayAccount.getDriverBalance()));
            if (amountDetails.getPassenger().compareTo(balance) > 0) {
                payLogger.error("checkPayment 用户余额不足. userId:{}, majorProductId:{}", checkAccountDO.getUserId(), majorProductId);
                throw new DException(TradeErrorCode.USER_ACCOUNT_NOT_ENOUGH_ERROR);
            }
        }
        if (amountDetails.getBonus().compareTo(BigDecimal.ZERO) > 0) {
            if ("true".equals(LoadPropertyUtil.getProperty("bonus.off", "true"))) {
                //顺风金下线
                payLogger.error("checkPayment 顺风金下线 本次行程顺风金不可用. userId:{}, majorProductId:{}", checkAccountDO.getUserId(), majorProductId);
                throw new DException(TradeErrorCode.BONUS_NOT_USER_ERROR);
            }
            // 行程是否可用顺风金
            if (!checkAccountDO.getBonusUseAble()) {
                payLogger.error("checkPayment 本次行程顺风金不可用. userId:{}, majorProductId:{}", checkAccountDO.getUserId(), majorProductId);
                throw new DException(TradeErrorCode.BONUS_NOT_USER_ERROR);
            }
            //是否支持顺风金支付
            if (!bonusAccountUseAbleApolloService.match(checkAccountDO.getBusinessType(), checkAccountDO.getSource())) {
                payLogger.error("checkPayment 不支持顺风金支付. userId:{}, majorProductId:{}", checkAccountDO.getUserId(), majorProductId);
                throw new DException(TradeErrorCode.NOT_SUPPORT_BONUS_ACCOUNT_PAYMENT_ERROR);
            }
            //lock顺风金是否足够
            BigDecimal bonusAmount = amountDetails.getBonus();
//            BigDecimal bonusBalance = accountWriteThriftSupportService.lockAndGetBonus(checkAccountDO.getUserId(), majorProductId);
            BigDecimal bonusBalance = accountReadThriftSupportService.getUserBonusByUserId(checkAccountDO.getUserId());
            if (bonusAmount.compareTo(bonusBalance) > 0) {
                payLogger.error("checkPayment 用户顺风金余额不足. userId:{}, majorProductId:{}", checkAccountDO.getUserId(), majorProductId);
                throw new DException(TradeErrorCode.BONUS_ACCOUNT_NOT_ENOUGH_ERROR);
            }
            //顺风金使用门槛，高于本金额可以使用
            BigDecimal ruleMoney = AmountConvertUtil.penny2yuan(BaseConfigMapperUtil.getBaseConfig("threshold_bonus_cent"));
            if (bonusBalance.compareTo(ruleMoney) < 0) {
                payLogger.info("checkPayment 顺风金低于门槛价格. majorProductId:{}, bonusAmount:{}, ruleMoney:{}",
                        majorProductId, bonusAmount, ruleMoney);
                throw new DException(TradeErrorCode.BONUS_ACCOUNT_NOT_ENOUGH_ERROR);
            }
            // 顺风金使用上限金额
            BigDecimal bonusUseMax = AmountConvertUtil.penny2yuan(BaseConfigMapperUtil.getBaseConfig("max_bonus_cent_lynx"));
            if (bonusAmount.compareTo(bonusUseMax) > 0) {
                payLogger.info("checkPayment 顺风金大于使用上限. majorProductId:{}, bonusAmount:{}, bonusUseMax:{}",
                        majorProductId, bonusAmount, bonusUseMax);
                throw new DException(TradeErrorCode.BONUS_MORE_MAX_PRICE_ERROR.getCode(),
                        String.format(TradeErrorCode.BONUS_MORE_MAX_PRICE_ERROR.getMsg(), bonusUseMax));
            }
            // 使用次数校验
            Integer max = LoadPropertyUtil.getProperty("bonus.use.max", Integer.class, 10);
            Integer times = RedisClusterUtil.get(RedisKeyConstants.BONUS_USE_TIMES_KEY, checkAccountDO.getUserId(), Integer.class);
            if (times != null && times >= max) {
                payLogger.info("checkPayment 顺风金使用次数已达上限. majorProductId:{}, bonusCent:{}, 使用次数:{}, 次数上限:{}",
                        majorProductId, bonusAmount, times, max);
                throw new DException(TradeErrorCode.BONUS_MORE_USER_ERROR.getCode(),
                        String.format(TradeErrorCode.BONUS_MORE_USER_ERROR.getMsg(), max));
            }
            // 打车金不能抵扣感谢费\打车金+优惠券不能抵扣感谢费
            if (amountDetails.getPassenger().add(amountDetails.getThird()).compareTo(checkAccountDO.getThankPrice()) < 0
                    && bonusAmount.add(amountDetails.getCoupon()).compareTo(checkAccountDO.getSinglePrice()) > 0) {
                payLogger.info("checkPayment 感谢费不能用顺风金(或 顺风车金+优惠券)支付. majorProductId:{}, thankPrice:{}, suggestPrice:{}, bonusAmount:{}",
                        majorProductId, checkAccountDO.getThankPrice(), checkAccountDO.getSinglePrice(), bonusAmount);
                throw new DException(TradeErrorCode.BONUS_NOT_DEDUCT_THANK_PRICE_ERROR);
            }
        }
        if (amountDetails.getTEnterprise().compareTo(BigDecimal.ZERO) > 0) {
            EnterpriseAccount account = enterpriseAccReadWriteThriftSupportService.lockAccount(majorProductId, checkAccountDO.getCompanyId());
            BigDecimal enterpriseBalance = AmountConvertUtil.penny2yuan(account.getBalance()).add(AmountConvertUtil.penny2yuan(account.getRechargeAwardBalance()));
            if (amountDetails.getTEnterprise().compareTo(enterpriseBalance) > 0) {
                payLogger.error("checkPayment 企业余额不足. userId:{}, majorProductId:{}", checkAccountDO.getUserId(), majorProductId);
                throw new DException(TradeErrorCode.ENTERPRISE_ACCOUNT_NOT_ENOUGH_ERROR);
            }
        }
    }

    public BigDecimal getEnterpriseBalance(String majorProductId, Integer companyId, BigDecimal tEnterprise) {
        EnterpriseAccount account = enterpriseAccReadWriteThriftSupportService.lockAccount(majorProductId, companyId);
        BigDecimal enterpriseBalance = AmountConvertUtil.penny2yuan(account.getBalance()).add(AmountConvertUtil.penny2yuan(account.getRechargeAwardBalance()));
        if (tEnterprise.compareTo(enterpriseBalance) > 0) {
            payLogger.error("checkPayment 企业余额不足.  majorProductId:{}",  majorProductId);
            throw new DException(TradeErrorCode.ENTERPRISE_ACCOUNT_NOT_ENOUGH_ERROR);
        }
        return enterpriseBalance;
    }

    /**
     * 检查余额并计算余额支付需要解析换成优先抵扣不可提现余额
     *
     * @param request -
     */
    public void checkAndCalculateAccountAmount(PaymentRequest request, BigDecimal passengerAmount, BigDecimal passengerNotAmount) {
        //余额为0不需要
        if (BigDecimal.ZERO.compareTo(passengerAmount.add(passengerNotAmount)) == 0) {
            return;
        }
        //存在不可提现金额则退出该逻辑
        if (passengerNotAmount.compareTo(BigDecimal.ZERO) > 0) {
            return;
        }
        UserPayAccount userPayAccount = accountReadThriftSupportService.getUserPayAccountThrowException(request.getUserId());
        payLogger.info("乘客账号余额 majorProductId:{},userPayAccount:{},passengerAmount:{} ",
                request.getMajorProductId(), JsonMapper.toJson(userPayAccount), passengerAmount);
        // 计算余额
        if ((userPayAccount.getUserBalance().add(userPayAccount.getDriverBalance())).compareTo(passengerAmount) < 0) {
            throw new DException(TradeErrorCode.USER_ACCOUNT_NOT_ENOUGH_ERROR);
        }
        //可提现余额
        BigDecimal cash;
        //不可提现余额
        BigDecimal noCash;
        if (userPayAccount.getDriverBalance().compareTo(passengerAmount) >= 0) {
            cash = BigDecimal.ZERO;
            noCash = passengerAmount;
        } else {
            noCash = userPayAccount.getDriverBalance();
            cash = passengerAmount.subtract(userPayAccount.getDriverBalance());
        }
        if (passengerAmount.compareTo(cash) == 0) {
            //相等 无需更新
            return;
        }
        payLogger.info("doPayment 使用不可提现余额. userId:{}, majorProductId:{}, passengerAmount:{}, cash:{}, noCash:{}",
                request.getUserId(), request.getMajorProductId(), passengerAmount, cash, noCash);
        //过滤乘客账户支付抵扣金额 重新塞入
        List<TOrderAmountDetails> newOrderAmountDetails = request.getOrderAmountDetails().stream()
                .filter(Objects::nonNull)
                .filter(orderAmountDetail -> !StringUtils.equals(orderAmountDetail.getType(), TOrderAccountTypeEnum.passenger.name()))
                .collect(Collectors.toList());
        if (cash.compareTo(BigDecimal.ZERO) > 0) {
            TOrderAmountDetails passengerDetails = new TOrderAmountDetails()
                    .setAmount(cash.toString())
                    .setType(TOrderAccountTypeEnum.passenger.name());
            newOrderAmountDetails.add(passengerDetails);
        }
        if (noCash.compareTo(BigDecimal.ZERO) > 0) {
            TOrderAmountDetails passengerNotWithdrawDetails = new TOrderAmountDetails()
                    .setAmount(noCash.toString())
                    .setType(TOrderAccountTypeEnum.passenger_not_withdraw.name());
            newOrderAmountDetails.add(passengerNotWithdrawDetails);
        }
        request.setOrderAmountDetails(newOrderAmountDetails);
    }
}
