package com.didapinche.trade.domin.bill.action.billtag.bo;

import cn.hutool.json.JSONUtil;
import com.didapinche.agaue.tools.convert.AmountConvertUtil;
import com.didapinche.trade.domin.bill.action.mapping.BillOrderMappingVisitor;
import com.didapinche.trade.infrastructure.tbl.TradeBillModel;
import com.didapinche.trade.infrastructure.util.BigDecimalUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Objects;

import static com.didapinche.trade.infrastructure.util.BigDecimalUtil.defValue;

/**
 * 免单标签
 */
@Slf4j
@Getter
public class BillOrderFreeTagBO extends BillOrderTagBO{



    String currFreeTag;//免单标识
    BigDecimal currFreeAmount;//总的免单金额

    Long driverId;//收款人ID

    Long freeTagId;

    public BillOrderFreeTagBO(TradeBillModel billModel) {
        super(billModel);
    }



    @Override
    public TradeBillModel updateTradeBillModel() {
        TradeBillModel tradeBillModel=new TradeBillModel();
        tradeBillModel.setId(this.getId());
        tradeBillModel.setVersion(this.getVersion());
        if(Objects.isNull(this.getPayeeId())){
            tradeBillModel.setPayeeId(driverId);
            this.setPayeeId(driverId);
        }
        //免单总金额
        tradeBillModel.setFreeAmount(defValue(this.currFreeAmount));
        //免单标识
        tradeBillModel.setFreeTag(this.currFreeTag);
        //到账金额减少
        tradeBillModel.setReceiptsAmount(AmountConvertUtil.toDecimalPlaces(super.getReceiptsAmount()).subtract(this.currFreeAmount));
        //收款人退回的钱
        tradeBillModel.setPayeeReturnAmount(BigDecimalUtil.add(defValue(this.currFreeAmount),super.getPayeeReturnAmount()));
        //更新付款人实际付的钱和退款金额
        tradeBillModel.setPaymentAmount(getPaymentAmount().subtract(defValue(defValue(this.currFreeAmount))));
        tradeBillModel.setPayerRefundAmount(BigDecimalUtil.add(getPayerRefundAmount(),defValue(this.currFreeAmount)));
        log.debug("即将更新免单数据:{}", JSONUtil.toJsonStr(tradeBillModel));
        return tradeBillModel;
    }

    @Override
    public void accept(BillOrderMappingVisitor billActionVisitor) {
        billActionVisitor.visit(this);
    }

    @Override
    public void setTag(Object[] tags) {
        if(Objects.isNull(tags)){
            return;
        }
        for (int i = 0; i < tags.length; i++) {
            switch (i){
                case 0:this.currFreeTag =(String)tags[i];break;
                case 1:this.currFreeAmount =(BigDecimal) tags[i];break;
                case 2:this.driverId=(Long) tags[i];break;
                case 3:this.freeTagId=(Long) tags[i];break;
                case 4:this.businessType=(String) tags[i];break;
            }
        }
    }
}
