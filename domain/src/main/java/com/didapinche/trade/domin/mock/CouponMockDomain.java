package com.didapinche.trade.domin.mock;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Set;

@Component
public class CouponMockDomain implements MockDomain {




    @ApolloJsonValue("${trade.mock.couponId:[]}")
    Set<String> mockCouponIdSet ;
    public boolean mockCoupon(Integer couponSetID) {
        if(couponSetID==null){
            return false;
        }
        return mockCouponIdSet.contains(couponSetID.toString());
    }
}
