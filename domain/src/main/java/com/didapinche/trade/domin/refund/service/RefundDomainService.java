package com.didapinche.trade.domin.refund.service;

import cn.hutool.core.lang.Pair;
import com.didapinche.agaue.common.exception.DException;
import com.didapinche.agaue.tools.convert.AmountConvertUtil;
import com.didapinche.commons.tx.TransactionExtHelper;
import com.didapinche.coupon.thrift.CouponResult;
import com.didapinche.doublewritecheck.entity.enums.PauActionEnums;
import com.didapinche.finance.mq.context.enums.PauCutFlagEnum;
import com.didapinche.finance.mq.context.enums.TheaExtraInfoKeyEnum;
import com.didapinche.finance.mq.context.enums.TradeTypeEnum;
import com.didapinche.finance.mq.context.message.messagecontext.RefundMessageContext;
import com.didapinche.finance.mq.context.message.messagecontext.TheaMessageContext;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.server.commons.common.util.DateUtil;
import com.didapinche.server.commons.common.util.LoadPropertyUtil;
import com.didapinche.server.commons.common.util.RedisClusterUtil;
import com.didapinche.trade.domin.DO.RemittanceOrderModelDO;
import com.didapinche.trade.domin.DO.TradeAmountDetailsDO;
import com.didapinche.trade.domin.DO.TradeCouponDetailsDO;
import com.didapinche.trade.domin.DO.TradeOrderDO;
import com.didapinche.trade.domin.doublewrite.DoubleWriteDomainService;
import com.didapinche.trade.domin.liquidation.message.RefundLiquidationMessage;
import com.didapinche.trade.domin.liquidation.message.RefundPauHighwayLiquidationMessage;
import com.didapinche.trade.domin.payment.pau.PaymentPayAfterUseDomainService;
import com.didapinche.trade.domin.refund.DO.CalculateOrderDO;
import com.didapinche.trade.domin.refund.DO.RefundResultDO;
import com.didapinche.trade.domin.refund.request.CheckRefundDomainRequest;
import com.didapinche.trade.domin.refund.request.RefundDomainRequest;
import com.didapinche.trade.domin.repository.RemittanceOrderRepository;
import com.didapinche.trade.domin.repository.TradeAmountDetailsRepository;
import com.didapinche.trade.domin.repository.TradeCouponDetailsRepository;
import com.didapinche.trade.domin.repository.TradeOrderRepository;
import com.didapinche.trade.domin.repository.TradeProductDetailsRepository;
import com.didapinche.trade.domin.thea.message.CommonTradeOrderTheaMessage;
import com.didapinche.trade.domin.thea.message.PauCutOrRefundTheaMessage;
import com.didapinche.trade.domin.thea.message.PauHighwayFeeRefundTheaMessage;
import com.didapinche.trade.infrastructure.constants.RedisKeyConstants;
import com.didapinche.trade.infrastructure.entities.TradeOrderNote;
import com.didapinche.trade.infrastructure.enums.PauBusinessTypeEnum;
import com.didapinche.trade.infrastructure.enums.PaymentPauCutFlageEnum;
import com.didapinche.trade.infrastructure.enums.RefundSceneTypeEnum;
import com.didapinche.trade.infrastructure.enums.order.TradeOrderStatusEnum;
import com.didapinche.trade.infrastructure.mq.RocketMqSendUtils;
import com.didapinche.trade.infrastructure.mq.bean.CouponMQEntity;
import com.didapinche.trade.infrastructure.support.TradeOrderIdFactory;
import com.didapinche.trade.infrastructure.tbl.TradeAmountDetailsModel;
import com.didapinche.trade.infrastructure.tbl.TradeOrderModel;
import com.didapinche.trade.infrastructure.tbl.TradeProductDetailsModel;
import com.didapinche.trade.infrastructure.thrift.ActivityGoodsOrderThriftSupportService;
import com.didapinche.trade.infrastructure.thrift.CouponThriftClient;
import com.didapinche.trade.infrastructure.thrift.JiFenThriftSupportService;
import com.didapinche.trade.infrastructure.thrift.PayThriftClient;
import com.didapinche.trade.infrastructure.util.AccountChangeUtil;
import com.didapinche.trade.infrastructure.util.BigDecimalUtil;
import com.didapinche.trade.infrastructure.util.TradeDateUtil;
import com.didapinche.trade.infrastructure.util.accountchangefactory.bean.InnerAccountChangeItem;
import com.didapinche.trade.thrift.enums.TBusinessEnum;
import com.didapinche.trade.thrift.enums.TChannelEnum;
import com.didapinche.trade.thrift.enums.TOrderAccountTypeEnum;
import com.didapinche.trade.thrift.enums.TPaymentWayEnum;
import com.didapinche.trade.thrift.enums.TTradeProductTypeEnum;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiPredicate;
import java.util.function.Consumer;

import static com.didapinche.trade.infrastructure.constants.TradeConstants.CARPOOL_ORDER_FREE_BUSINESS_TYPE;
import static com.didapinche.trade.infrastructure.exception.TradeErrorCode.NOT_FUND_PAY_ORDER_ERROR;
import static com.didapinche.trade.infrastructure.exception.TradeErrorCode.ORDER_CANT_ERROR;

/**
 * <AUTHOR>
 * @Date 2022/8/4 11:44
 * @Version 1.0
 */
@Service
@Slf4j
public class RefundDomainService {

    @Autowired
    private TradeOrderRepository tradeOrderRepository;
    @Autowired
    private TradeAmountDetailsRepository tradeAmountDetailsRepository;
    @Autowired
    private TradeCouponDetailsRepository tradeCouponDetailsRepository;
    @Autowired
    private TradeOrderIdFactory tradeOrderIdFactory;
    @Resource
    protected CouponThriftClient couponThriftClient;
    @Resource
    private TradeProductDetailsRepository tradeProductDetailsRepository;
    @Resource
    private RemittanceOrderRepository remittanceOrderRepository;
    @Resource
    private PaymentPayAfterUseDomainService paymentPayAfterUseDomainService;
    @Resource
    protected PayThriftClient payThriftClient;
    @Resource
    private JiFenThriftSupportService jiFenThriftSupportService;
    @Resource
    private ActivityGoodsOrderThriftSupportService activityGoodsOrderThriftSupportService;
    @Resource
    protected DoubleWriteDomainService doubleWriteDomainService;

    private static final Logger liquidationLogger = LoggerFactory.getLogger("LiquidationLogger");

    private final Map<String, BiPredicate<TradeOrderDO,CheckRefundDomainRequest>> refundCheck;

    public RefundDomainService(){
        refundCheck = new HashMap<>();
        //免单不能退   外输类不允许退款
        refundCheck.put(TBusinessEnum.carpool_offline.name(),(tradeOrderDO,checkRefundDomainRequest)->{
            List<RemittanceOrderModelDO> remittanceOrderModelDOS = remittanceOrderRepository.queryByBussinessTypeAndPayTradeNo(tradeOrderDO.getId(), CARPOOL_ORDER_FREE_BUSINESS_TYPE);
            if (!CollectionUtils.isEmpty(remittanceOrderModelDOS)){
                log.error("当前订单存在免单 orderId:{}",tradeOrderDO.getId());
                return true;
            }
            try {
                if ("true".equals(LoadPropertyUtil.getProperty("carpool_offline.carpool_vip.refund.check", "true"))) {
                    if (TBusinessEnum.c_enterprise_vip.name().equals(tradeOrderDO.getBusinessType())) {
                        log.error("外输订单不支持退款 orderId:{}", tradeOrderDO.getId());
                        return true;
                    }
                }
            }catch (Exception ex){
                //ignore ex
                log.error("carpool_offline.carpool_vip.refund.check error",ex);
            }
            return false;
        });
        //三化订单不持支退款
        refundCheck.put(TBusinessEnum.taxi_offline.name(),(tradeOrderDO,checkRefundDomainRequest)->{
            if (TBusinessEnum.taxiqr.name().equals(tradeOrderDO.getBusinessType())){
                log.error("三化订单支不持退款 orderId:{}",tradeOrderDO.getId());
                return true;
            }
            return false;
        });
        // 免单不能退
        refundCheck.put(TBusinessEnum.carpool_cancel.name(),(tradeOrderDO,checkRefundDomainRequest)->{
            List<RemittanceOrderModelDO> remittanceOrderModelDOS = remittanceOrderRepository.queryByBussinessTypeAndPayTradeNo(tradeOrderDO.getId(), CARPOOL_ORDER_FREE_BUSINESS_TYPE);
            if (!CollectionUtils.isEmpty(remittanceOrderModelDOS)){
                log.error("当前订单存在免单 {}",tradeOrderDO.getId());
                return true;
            }
            return false;
        });
        // 券包退款金额校验
        refundCheck.put(TBusinessEnum.mall.name(),(tradeOrderDO,checkRefundDomainRequest)->{
            BigDecimal couponPackageAmount = tradeProductDetailsRepository.couponPackageAmount(checkRefundDomainRequest.getOrderId(), checkRefundDomainRequest.getUserId());
            BigDecimal ensureFeePackageAmount = tradeProductDetailsRepository.ensureFeePackageAmount(checkRefundDomainRequest.getOrderId(), checkRefundDomainRequest.getUserId());
            if (couponPackageAmount.compareTo(checkRefundDomainRequest.getRefundAmount()) < 0 && ensureFeePackageAmount.compareTo(checkRefundDomainRequest.getRefundAmount()) < 0){
                log.error("券包或随单购退款金额错误 {}:{}",checkRefundDomainRequest.getOrderId(),checkRefundDomainRequest.getRefundAmount().toString());
                return true;
            }
            return false;
        });
        // 感谢费退款
        refundCheck.put(TBusinessEnum.carpool_additional.name(),(tradeOrderDO,checkRefundDomainRequest)->{

            BigDecimal extraFeeAmount = tradeProductDetailsRepository.queryAllAdditionalAmount(checkRefundDomainRequest.getOrderId(), checkRefundDomainRequest.getUserId());
            if (extraFeeAmount.compareTo(checkRefundDomainRequest.getRefundAmount()) < 0){
                log.error("感谢费退款金额错误 {}:{}",checkRefundDomainRequest.getOrderId(),checkRefundDomainRequest.getRefundAmount().toString());
                return true;
            }
            return false;
        });

        //高速费退款校验
        refundCheck.put(TBusinessEnum.carpool_highway.name(), (tradeOrderDO, checkRefundDomainRequest) -> {
            //增加对应费用项目的金额校验
            BigDecimal applyRefundAmount = checkRefundDomainRequest.getRefundAmount();
            BigDecimal singleTypeAmount = tradeProductDetailsRepository.getSingleAmount(tradeOrderDO.getId(), tradeOrderDO.getUserId(), TTradeProductTypeEnum.hignwayFee.name());
            if (applyRefundAmount.compareTo(singleTypeAmount) > 0) {
                log.error("发起高速费退款金额错误 {}:{}", tradeOrderDO.getId(), checkRefundDomainRequest.getRefundAmount().toString());
                return true;
            }
            return false;
        });

    }


    public List<RefundResultDO> lockAndCheckRefund(CheckRefundDomainRequest checkRefundDomainRequest){
        // 根据主订单加锁
        TradeOrderDO lock = tradeOrderRepository.queryWithLock(checkRefundDomainRequest.getOrderId(), checkRefundDomainRequest.getUserId());
        if (lock == null){
            throw new DException(NOT_FUND_PAY_ORDER_ERROR);
        }
        // 不同业务做校验
        BiPredicate<TradeOrderDO,CheckRefundDomainRequest> tradeOrderDoConsumer = refundCheck.get(checkRefundDomainRequest.getBusinessType());
        if (tradeOrderDoConsumer != null){
            if (tradeOrderDoConsumer.test(lock,checkRefundDomainRequest)){
                throw new DException(ORDER_CANT_ERROR);
            }
        }
        //查询是否已经退款，同一个行程可能会有多个重复支付用order_id 做去重
        if (StringUtils.equalsAny(checkRefundDomainRequest.getBusinessType(), TBusinessEnum.carpool_repeatpay.name())){
            return queryRefundInfoListByid(checkRefundDomainRequest.getOrderId(), checkRefundDomainRequest.getBusinessType(), checkRefundDomainRequest.getUserId());
        }
        return queryRefundInfoListByPid(checkRefundDomainRequest.getProductId(), checkRefundDomainRequest.getBusinessType(), checkRefundDomainRequest.getUserId());
    }

    /**
     * 查询退款明细
     * @param payTradeNo 支付id
     * @param businessType   业务类型
     */
    private List<RefundResultDO> queryRefundInfoListByid(Long payTradeNo, String businessType, Long userId) {
        List<TradeOrderDO> refundTradeOrderList = tradeOrderRepository.queryBypayTradeNoAndBusinessType(payTradeNo, TradeTypeEnum.refund.name(), businessType,userId);
        if (CollectionUtils.isEmpty(refundTradeOrderList)){
            return new ArrayList<>();
        }
        List<RefundResultDO> tradeRefundResults = new ArrayList<>();
        for (TradeOrderDO tradeOrderDO : refundTradeOrderList){
            RefundResultDO refundResultDO = new RefundResultDO();
            refundResultDO.setPayOrderId(tradeOrderDO.getPayTradeNo());
            refundResultDO.setRefundOrderId(tradeOrderDO.getId());
            refundResultDO.setTotalPrice(BigDecimal.ZERO);
            List<TradeAmountDetailsDO> tradeAmountDetailsModelList = tradeAmountDetailsRepository.queryAmountDetailsByOrderId(tradeOrderDO.getId(),userId);
            for (TradeAmountDetailsDO tradeAmountDetailsDO : tradeAmountDetailsModelList) {
                refundResultDO.setTotalPrice(refundResultDO.getTotalPrice().add(tradeAmountDetailsDO.getAmount()));
                if (TOrderAccountTypeEnum.third.name().equals(tradeAmountDetailsDO.getType())) {
                    refundResultDO.setThird(tradeAmountDetailsDO.getAmount());
                    refundResultDO.setThirdChannel(tradeOrderDO.getThirdChannel());
                    continue;
                }
                if (TOrderAccountTypeEnum.passenger.name().equals(tradeAmountDetailsDO.getType())) {
                    refundResultDO.setPassenger(tradeAmountDetailsDO.getAmount());
                    continue;
                }
                if (TOrderAccountTypeEnum.bonus.name().equals(tradeAmountDetailsDO.getType())) {
                    refundResultDO.setBonus(tradeAmountDetailsDO.getAmount());
                    continue;
                }
                if (TOrderAccountTypeEnum.coupon.name().equals(tradeAmountDetailsDO.getType())) {
                    refundResultDO.setCoupon(tradeAmountDetailsDO.getAmount());
                    refundResultDO.setCouponId(tradeCouponDetailsRepository.queryCouponId(tradeOrderDO.getId(),userId));
                    continue;
                }
                if (TOrderAccountTypeEnum.passenger_not_withdraw.name().equals(tradeAmountDetailsDO.getType())) {
                    refundResultDO.setPassengerNotWithdraw(tradeAmountDetailsDO.getAmount());
                    continue;
                }
                if (TOrderAccountTypeEnum.c_enterprise_vip.name().equals(tradeAmountDetailsDO.getType())) {
                    refundResultDO.setCEnterpriseVip(tradeAmountDetailsDO.getAmount());
                    continue;
                }
                if (TOrderAccountTypeEnum.t_enterprise_vip.name().equals(tradeAmountDetailsDO.getType())) {
                    refundResultDO.setTEnterpriseVip(tradeAmountDetailsDO.getAmount());
                    continue;
                }
                if (TOrderAccountTypeEnum.t_enterprise.name().equals(tradeAmountDetailsDO.getType())) {
                    refundResultDO.setTEnterprise(tradeAmountDetailsDO.getAmount());
                    continue;
                }
                if (TOrderAccountTypeEnum.recover_coupon.name().equals(tradeAmountDetailsDO.getType())) {
                    refundResultDO.setCouponRecover(tradeAmountDetailsDO.getAmount());
                    continue;
                }
                if (TOrderAccountTypeEnum.recover_bonus.name().equals(tradeAmountDetailsDO.getType())) {
                    refundResultDO.setBonusRecover(tradeAmountDetailsDO.getAmount());
                }
            }
            tradeRefundResults.add(refundResultDO);
        }
        return tradeRefundResults;
    }

    /**
     * 查询退款明细
     * @param productId 业务订单号
     * @param businessType   业务类型
     */
    private List<RefundResultDO> queryRefundInfoListByPid(String productId, String businessType, Long userId) {
        List<TradeOrderDO> refundTradeOrderList = tradeOrderRepository.queryTradeOrderList(productId, TradeTypeEnum.refund.name(), businessType,userId);
        if (CollectionUtils.isEmpty(refundTradeOrderList)){
            return new ArrayList<>();
        }
        List<RefundResultDO> tradeRefundResults = new ArrayList<>();
        for (TradeOrderDO tradeOrderDO : refundTradeOrderList){
            RefundResultDO refundResultDO = new RefundResultDO();
            refundResultDO.setPayOrderId(tradeOrderDO.getPayTradeNo());
            refundResultDO.setRefundOrderId(tradeOrderDO.getId());
            refundResultDO.setTotalPrice(BigDecimal.ZERO);
            List<TradeAmountDetailsDO> tradeAmountDetailsModelList = tradeAmountDetailsRepository.queryAmountDetailsByOrderId(tradeOrderDO.getId(),userId);
            for (TradeAmountDetailsDO tradeAmountDetailsDO : tradeAmountDetailsModelList) {
                refundResultDO.setTotalPrice(refundResultDO.getTotalPrice().add(tradeAmountDetailsDO.getAmount()));
                if (TOrderAccountTypeEnum.third.name().equals(tradeAmountDetailsDO.getType())) {
                    refundResultDO.setThird(tradeAmountDetailsDO.getAmount());
                    refundResultDO.setThirdChannel(tradeOrderDO.getThirdChannel());
                    continue;
                }
                if (TOrderAccountTypeEnum.passenger.name().equals(tradeAmountDetailsDO.getType())) {
                    refundResultDO.setPassenger(tradeAmountDetailsDO.getAmount());
                    continue;
                }
                if (TOrderAccountTypeEnum.bonus.name().equals(tradeAmountDetailsDO.getType())) {
                    refundResultDO.setBonus(tradeAmountDetailsDO.getAmount());
                    continue;
                }
                if (TOrderAccountTypeEnum.coupon.name().equals(tradeAmountDetailsDO.getType())) {
                    refundResultDO.setCoupon(tradeAmountDetailsDO.getAmount());
                    refundResultDO.setCouponId(tradeCouponDetailsRepository.queryCouponId(tradeOrderDO.getId(),userId));
                    continue;
                }
                if (TOrderAccountTypeEnum.passenger_not_withdraw.name().equals(tradeAmountDetailsDO.getType())) {
                    refundResultDO.setPassengerNotWithdraw(tradeAmountDetailsDO.getAmount());
                    continue;
                }
                if (TOrderAccountTypeEnum.c_enterprise_vip.name().equals(tradeAmountDetailsDO.getType())) {
                    refundResultDO.setCEnterpriseVip(tradeAmountDetailsDO.getAmount());
                    continue;
                }
                if (TOrderAccountTypeEnum.t_enterprise_vip.name().equals(tradeAmountDetailsDO.getType())) {
                    refundResultDO.setTEnterpriseVip(tradeAmountDetailsDO.getAmount());
                    continue;
                }
                if (TOrderAccountTypeEnum.t_enterprise.name().equals(tradeAmountDetailsDO.getType())) {
                    refundResultDO.setTEnterprise(tradeAmountDetailsDO.getAmount());
                    continue;
                }
                if (TOrderAccountTypeEnum.recover_coupon.name().equals(tradeAmountDetailsDO.getType())) {
                    refundResultDO.setCouponRecover(tradeAmountDetailsDO.getAmount());
                    continue;
                }
                if (TOrderAccountTypeEnum.recover_bonus.name().equals(tradeAmountDetailsDO.getType())) {
                    refundResultDO.setBonusRecover(tradeAmountDetailsDO.getAmount());
                }
                if (TOrderAccountTypeEnum.recover_highway_subsidy.name().equals(tradeAmountDetailsDO.getType())) {
                    refundResultDO.setHighwaySubsidyRecover(tradeAmountDetailsDO.getAmount());
                }
                if (TOrderAccountTypeEnum.highway_subsidy.name().equals(tradeAmountDetailsDO.getType())) {
                    refundResultDO.setHighwaySubsidy(tradeAmountDetailsDO.getAmount());
                }
            }
            tradeRefundResults.add(refundResultDO);
        }
        return tradeRefundResults;
    }


    /**
     *  退款
     */
    public RefundResultDO refund(RefundDomainRequest refundDomainRequest) throws Exception {
        RefundResultDO refundResult = new RefundResultDO();
        // 总退款金额
        BigDecimal totalPrice = BigDecimal.ZERO;
        TradeOrderDO tradeOrderDO = refundDomainRequest.getTradeOrderDO();
        CalculateOrderDO calculateOrderDO = refundDomainRequest.getCalculateOrderDO();
        tradeOrderDO.setId(tradeOrderIdFactory.genUniqueId(tradeOrderDO.getPayTradeNo()+""));
        TradeOrderModel oldTrade = calculateOrderDO.getTradeOrderModel();
        TradeOrderNote tradeOrderNote = TradeOrderNote.create(calculateOrderDO.getTradeOrderModel().getNote());
        boolean pauFlag = paymentPayAfterUseDomainService.isPauFlag(tradeOrderNote.getPaymentWay());
        if (pauFlag) {
            //先享后付单
            return pauRefund(refundDomainRequest);
        }



        Consumer<TheaMessageContext> afterTrade = theaMessageContext -> {};
        // 退三方
        if (calculateOrderDO.getThird().compareTo(BigDecimal.ZERO) > 0){
            tradeOrderDO.setThirdChannel(calculateOrderDO.getTradeOrderModel().getThirdChannel());
            tradeOrderDO.setSubType(tradeOrderDO.getThirdChannel());//普通的支付方式
            refundResult.setThirdChannel(tradeOrderDO.getThirdChannel());
            totalPrice = totalPrice.add(calculateOrderDO.getThird());
            RefundMessageContext messageContext = new RefundMessageContext();
            messageContext.setUserId(tradeOrderDO.getUserId());
            messageContext.setPayTradeNo(String.valueOf(tradeOrderDO.getPayTradeNo()));
            messageContext.setRefundAmount(calculateOrderDO.getThird());
            messageContext.setTradeNo(String.valueOf(tradeOrderDO.getId()));
            messageContext.setRefundBusinessType(tradeOrderDO.getBusinessType());
            messageContext.setChannelType(tradeOrderDO.getThirdChannel());
            messageContext.setOrderNo(refundDomainRequest.getPaymentNo());
            RocketMqSendUtils.paymentRefund(messageContext);
            String payChannel = oldTrade.getThirdChannel();
            if (TChannelEnum.tonglian.name().equals(payChannel)) {
                if ("weixin".equals(tradeOrderNote.getPaymentWay())) {
                    payChannel = payChannel + "_weixin";
                }
            }
            String finalPayChannel = payChannel;
            afterTrade = afterTrade.andThen( theaMessageContext -> {
                String merchantInfo = tradeOrderNote.getMerchantId() == null ? null : tradeOrderNote.getMerchantId();
                theaMessageContext.setPayTime(tradeOrderDO.getSuccessTime())
                        .setPayType("default")
                        .setMerchantInfo(merchantInfo)
                        .setThirdPayTradeNo(oldTrade.getThirdTradeNo())
                        .setPayChannleStr(finalPayChannel);
            });
        }

        // 退余额
        if (calculateOrderDO.getPassenger().add(calculateOrderDO.getPassengerNotWithdraw()).compareTo(BigDecimal.ZERO) > 0){
            totalPrice = totalPrice.add(calculateOrderDO.getPassengerNotWithdraw()).add(calculateOrderDO.getPassenger());
            InnerAccountChangeItem innerAccountChangeItem = new InnerAccountChangeItem();
            innerAccountChangeItem.setAccountType(TOrderAccountTypeEnum.passenger.name());
            innerAccountChangeItem.setBusinessType(tradeOrderDO.getBusinessType());
            innerAccountChangeItem.setTradeType(tradeOrderDO.getTradeType());
            innerAccountChangeItem.setUserId(tradeOrderDO.getUserId());
            innerAccountChangeItem.setOrderId(tradeOrderDO.getId());
            innerAccountChangeItem.setPayTradeNO(tradeOrderDO.getPayTradeNo());
            innerAccountChangeItem.setAmount(calculateOrderDO.getPassenger());
            innerAccountChangeItem.setPassengerNotWithdraw(calculateOrderDO.getPassengerNotWithdraw());
            innerAccountChangeItem.setSysopId(refundDomainRequest.getSysOpId());
            AccountChangeUtil.accountIncome(innerAccountChangeItem);
        }

        // 退顺风金
        if (calculateOrderDO.getBonus().compareTo(BigDecimal.ZERO) > 0){
            totalPrice = totalPrice.add(calculateOrderDO.getBonus());
            InnerAccountChangeItem innerAccountChangeItem = new InnerAccountChangeItem();
            innerAccountChangeItem.setAccountType(TOrderAccountTypeEnum.bonus.name());
            innerAccountChangeItem.setTradeType(tradeOrderDO.getTradeType());
            innerAccountChangeItem.setBusinessType(tradeOrderDO.getBusinessType());
            innerAccountChangeItem.setUserId(tradeOrderDO.getUserId());
            innerAccountChangeItem.setOrderId(tradeOrderDO.getId());
            innerAccountChangeItem.setAmount(calculateOrderDO.getBonus());
            innerAccountChangeItem.setSysopId(refundDomainRequest.getSysOpId());
            innerAccountChangeItem.setPayTradeNO(tradeOrderDO.getPayTradeNo());
            AccountChangeUtil.accountIncome(innerAccountChangeItem);
            // 更新打车金使用次数
            Integer times = RedisClusterUtil.get(RedisKeyConstants.BONUS_USE_TIMES_KEY, tradeOrderDO.getUserId(), Integer.class);
            if (times == null || times < 0) {
                times = 0;
            }

            Integer origin = times;
            RedisClusterUtil.setToday(RedisKeyConstants.BONUS_USE_TIMES_KEY, tradeOrderDO.getUserId(), times > 0 ? times - 1 : 0, false);
            TransactionExtHelper.addRolledbackCallback(() -> RedisClusterUtil.setToday(RedisKeyConstants.BONUS_USE_TIMES_KEY,tradeOrderDO.getUserId() , origin, false));
        }

        // 退部分优惠券标识
        boolean couponDiffRefundFlag = false;
        if (calculateOrderDO.getCoupon().compareTo(BigDecimal.ZERO) > 0){
            totalPrice = totalPrice.add(calculateOrderDO.getCoupon());
            TradeCouponDetailsDO tradeCouponDetailsDO = tradeCouponDetailsRepository.queryCoupon(tradeOrderDO.getPayTradeNo(),tradeOrderDO.getUserId());
            Preconditions.checkArgument(tradeCouponDetailsDO != null,"refund tradeCouponDetailsDO is null");
            afterTrade = afterTrade.andThen(theaMessageContext -> {
                // 添加优惠券
                Map<String, Object> financeInfo = theaMessageContext.getFinanceInfo();
                if (CollectionUtils.isEmpty(financeInfo)) {
                    financeInfo = new HashMap<>();
                    theaMessageContext.setFinanceInfo(financeInfo);
                }
                financeInfo.put(TheaExtraInfoKeyEnum.coupon_id.name(), tradeCouponDetailsDO.getCouponId());
            });
            if (Boolean.TRUE.equals(refundDomainRequest.getCouponFlag())){
                CouponResult couponResult = couponThriftClient.couponResult(tradeCouponDetailsDO.getCouponId(),tradeOrderDO.getUserId(), tradeOrderDO.getPayTradeNo());
                if (couponResult.getData() != null && tradeOrderDO.getPayTradeNo().toString().equals(StringUtils.defaultIfEmpty(couponResult.getData().getOrder_id(),"").replace("-",""))) {
                    CouponMQEntity mqEntity = new CouponMQEntity();
                    mqEntity.setUserId(tradeOrderDO.getUserId());
                    mqEntity.setTradeOrderId(tradeOrderDO.getPayTradeNo());
                    mqEntity.setCouponId(tradeCouponDetailsDO.getCouponId());
                    mqEntity.setStatus("refund");
                    mqEntity.setOrderId(tradeOrderDO.getId());
                    RocketMqSendUtils.refundCoupon(mqEntity);
                    refundResult.setCouponId(tradeCouponDetailsDO.getCouponId());
                    // 插入数据库
                    tradeCouponDetailsDO.setTradeOrderId(tradeOrderDO.getId());
                    tradeCouponDetailsDO.setAmount(calculateOrderDO.getCoupon());
                    tradeCouponDetailsRepository.save(tradeCouponDetailsDO);
                }
            }
            if (Boolean.TRUE.equals(refundDomainRequest.getNotifyCoupon()) && !refundDomainRequest.getCouponFlag()){
                //部分退优惠券需要通知活动组
                log.info("部分退优惠券通知活动组. paymentId:{},amount:{}", oldTrade.getId(), refundDomainRequest.getCouponRealAmount());
                couponThriftClient.againUseCouponAmount(oldTrade.getId(), tradeCouponDetailsDO.getCouponId(), refundDomainRequest.getCouponRealAmount(),
                        oldTrade.getMajorProductId(), getProductType(oldTrade.getBusinessType()), oldTrade.getUserId());
                //更新退款记录优惠券金额amount的实际核销结果和状态
                couponDiffRefundFlag = true;
            }
        }

        //补贴是平台的，不存在退还给乘客

        //退顺风车api企业账户 trade记录 账户中心不记录
        if (calculateOrderDO.getCEnterpriseVip().compareTo(BigDecimal.ZERO) > 0){
            totalPrice = totalPrice.add(calculateOrderDO.getCEnterpriseVip());
            afterTrade = afterTrade.andThen(theaMessageContext -> {
                BigDecimal insuranceFee = tradeOrderNote.getEnterpriseDetails() == null ? null : AmountConvertUtil.yuan2BigDecimal(tradeOrderNote.getEnterpriseDetails().getOrderServiceFee());
                Map<String, Object> extraInfoMap = new HashMap<>();
                extraInfoMap.put("insurance_fee", insuranceFee);
                theaMessageContext.setExtraInfo(extraInfoMap);
            });
        }

        // 退出租车api企业账户
        if (calculateOrderDO.getTEnterpriseVip().compareTo(BigDecimal.ZERO) > 0){
            totalPrice = totalPrice.add(calculateOrderDO.getTEnterpriseVip());
            InnerAccountChangeItem innerAccountChangeItem = new InnerAccountChangeItem();
            innerAccountChangeItem.setBusinessType(tradeOrderDO.getBusinessType());
            innerAccountChangeItem.setRuleId(0);
            innerAccountChangeItem.setBalanceExtra1change(0);
            innerAccountChangeItem.setBalanceExtra2change(0);
            innerAccountChangeItem.setAwardBalance(BigDecimal.ZERO);
            innerAccountChangeItem.setUserId(tradeOrderDO.getUserId());
            innerAccountChangeItem.setCompanyId(tradeOrderDO.getCompanyId());
            innerAccountChangeItem.setOrderId(tradeOrderDO.getId());
            innerAccountChangeItem.setAmount(calculateOrderDO.getTEnterpriseVip());
            innerAccountChangeItem.setAccountType(TOrderAccountTypeEnum.t_enterprise_vip.name());
            innerAccountChangeItem.setSysopId(refundDomainRequest.getSysOpId());
            innerAccountChangeItem.setComment("出租车企业付订单退款");
            innerAccountChangeItem.setPayTradeNO(tradeOrderDO.getPayTradeNo());
            TradeOrderNote tradeOrderNote1 = JsonMapper.json2Bean(tradeOrderDO.getNote(), TradeOrderNote.class);
            if (StringUtils.isNotBlank(tradeOrderNote1.getRideId())) {
                innerAccountChangeItem.setRideId(StringUtils.isNumeric(tradeOrderNote1.getRideId()) ? Long.parseLong(tradeOrderNote1.getRideId()) : 0);
            } else {
                innerAccountChangeItem.setRideId(StringUtils.isNumeric(tradeOrderDO.getMajorProductId()) ? Long.parseLong(tradeOrderDO.getMajorProductId()) : 0);
            }
            AccountChangeUtil.accountIncome(innerAccountChangeItem);
            afterTrade = afterTrade.andThen(theaMessageContext -> {
                Map<String, Object> extraInfoMap = new HashMap<>();
                extraInfoMap.put("insurance_fee", refundDomainRequest.getServiceFee());
                theaMessageContext.setExtraInfo(extraInfoMap);
            });
        }
        // 退出租车企业账户
        if (calculateOrderDO.getTEnterprise().compareTo(BigDecimal.ZERO) > 0){
            totalPrice = totalPrice.add(calculateOrderDO.getTEnterprise());
            InnerAccountChangeItem innerAccountChangeItem = new InnerAccountChangeItem();
            innerAccountChangeItem.setRuleId(0);
            innerAccountChangeItem.setBalanceExtra1change(0);
            innerAccountChangeItem.setBalanceExtra2change(0);
            innerAccountChangeItem.setAwardBalance(BigDecimal.ZERO);
            innerAccountChangeItem.setBusinessType(tradeOrderDO.getBusinessType());
            innerAccountChangeItem.setUserId(tradeOrderDO.getUserId());
            innerAccountChangeItem.setCompanyId(tradeOrderDO.getCompanyId());
            innerAccountChangeItem.setOrderId(tradeOrderDO.getId());
            innerAccountChangeItem.setAmount(calculateOrderDO.getTEnterprise());
            innerAccountChangeItem.setComment("出租车企业账户退款");
            innerAccountChangeItem.setPayTradeNO(tradeOrderDO.getPayTradeNo());
            TradeOrderNote tradeOrderNote1 = JsonMapper.json2Bean(tradeOrderDO.getNote(), TradeOrderNote.class);
            if (StringUtils.isNotBlank(tradeOrderNote1.getRideId())) {
                innerAccountChangeItem.setRideId(StringUtils.isNumeric(tradeOrderNote1.getRideId()) ? Long.parseLong(tradeOrderNote1.getRideId()) : 0);
            } else {
                innerAccountChangeItem.setRideId(StringUtils.isNumeric(tradeOrderDO.getMajorProductId()) ? Long.parseLong(tradeOrderDO.getMajorProductId()) : 0);
            }
            innerAccountChangeItem.setAccountType(TOrderAccountTypeEnum.t_enterprise.name());
            AccountChangeUtil.accountIncome(innerAccountChangeItem);
        }

        if (calculateOrderDO.getBonusRecover().compareTo(BigDecimal.ZERO) > 0){
            totalPrice = totalPrice.add(calculateOrderDO.getBonusRecover());
        }

        if (calculateOrderDO.getCouponRecover().compareTo(BigDecimal.ZERO) > 0){
            totalPrice = totalPrice.add(calculateOrderDO.getCouponRecover());
        }

        if (calculateOrderDO.getHighwaySubsidyRecover().compareTo(BigDecimal.ZERO) > 0) {
            totalPrice = totalPrice.add(calculateOrderDO.getHighwaySubsidyRecover());
        }

        if (calculateOrderDO.getHighwaySubsidy().compareTo(BigDecimal.ZERO) > 0) {
            totalPrice = totalPrice.add(calculateOrderDO.getHighwaySubsidy());
        }

        //补贴
        if(BigDecimalUtil.gtZero(calculateOrderDO.getPassengerSubsidy())){
            totalPrice = totalPrice.add(calculateOrderDO.getPassengerSubsidy());
        }

        if (totalPrice.compareTo(BigDecimal.ZERO) >= 0){
            //入库
            tradeOrderDO.setTotalPrice(totalPrice);
            tradeOrderDO.setStatus(TradeOrderStatusEnum.success.name());
            TradeOrderModel tradeOrderModel = tradeOrderRepository.save(tradeOrderDO);
            List<TradeAmountDetailsModel> amountDetails = tradeAmountDetailsRepository.save(calculateOrderDO, tradeOrderDO.getId(), tradeOrderModel, false, couponDiffRefundFlag);
            if (totalPrice.compareTo(BigDecimal.ZERO) > 0) {
                TradeProductDetailsModel ensureFeePackageProductDetail = null;
                if (TBusinessEnum.mall.name().equals(tradeOrderDO.getBusinessType())) {
                    ensureFeePackageProductDetail = tradeProductDetailsRepository.getEnsureFeePackageProductDetail(tradeOrderDO.getPayTradeNo(), tradeOrderDO.getUserId());
                }
                //thea+
                CommonTradeOrderTheaMessage theaMessage = new CommonTradeOrderTheaMessage();
                Consumer<TheaMessageContext> finalAfterTrade = afterTrade;
                theaMessage.setTradeOrder(tradeOrderModel)
                        .setAmountDetails(amountDetails)
                        .setProductDetail(ensureFeePackageProductDetail) //查看是否为随单购，发送不同的thea消息
                        .setAfterTrade(theaMessageContext -> {
                            finalAfterTrade.accept(theaMessageContext);
                            // 添加到帐状态
                            Map<String, Object> financeInfo = theaMessageContext.getFinanceInfo();
                            if (CollectionUtils.isEmpty(financeInfo)) {
                                financeInfo = new HashMap<>();
                            }
                            financeInfo.put(TheaExtraInfoKeyEnum.transfer_status.name(),String.valueOf(refundDomainRequest.getTransferStatus()));
                            theaMessageContext.setFinanceInfo(financeInfo);
                        }).send();
                //二清
                RefundLiquidationMessage liquidationMessage = new RefundLiquidationMessage();
                liquidationMessage.setRefundOrder(tradeOrderDO)
                        .setCalculateOrderDO(calculateOrderDO)
                        .setProductDetail(ensureFeePackageProductDetail)
                        .setTransferFlag(refundDomainRequest.getTransferStatus())
                        .setDriverId(refundDomainRequest.getDriverId())
                        .setServiceFee(refundDomainRequest.getServiceFee())
                        .setMajorOrderId(refundDomainRequest.getMajorOrderId())
                        .setAfterTrade(messageContext -> {
                            if (refundDomainRequest.getAdvanceOrderDO() != null) {
                                messageContext.setCarpoolPreSettleAmount(AmountConvertUtil.yuan2penny(refundDomainRequest.getAdvanceOrderDO().getAdvanceAmount()));
                            }
                            liquidationLogger.info("发送二清消息.body={}", JsonMapper.toJson(messageContext));
                        })
                        .send();
            }
        }
        // 组装返回值
        refundResult.setTEnterpriseVip(calculateOrderDO.getTEnterpriseVip());
        refundResult.setTEnterprise(calculateOrderDO.getTEnterprise());
        refundResult.setCEnterpriseVip(calculateOrderDO.getCEnterpriseVip());
        refundResult.setBonusRecover(calculateOrderDO.getBonusRecover());
        refundResult.setCouponRecover(calculateOrderDO.getCouponRecover());
        refundResult.setTotalPrice(totalPrice);
        refundResult.setPayOrderId(tradeOrderDO.getPayTradeNo());
        refundResult.setRefundOrderId(tradeOrderDO.getId());
        refundResult.setCoupon(calculateOrderDO.getCoupon());
        refundResult.setThird(calculateOrderDO.getThird());
        refundResult.setPassenger(calculateOrderDO.getPassenger());
        refundResult.setPassengerNotWithdraw(calculateOrderDO.getPassengerNotWithdraw());
        refundResult.setBonus(calculateOrderDO.getBonus());
        refundResult.setSuccessTime(tradeOrderDO.getSuccessTime());
        //补贴
        refundResult.setPassengerSubsidy(calculateOrderDO.getPassengerSubsidy());
        return refundResult;
    }

    /**
     * 区分carpool/taxi 用于免密区分和优惠券核销区分
     *
     * @param businessType 业务
     * @return -
     */
    public String getProductType(String businessType) {
        if (StringUtils.equalsAny(businessType,
                TBusinessEnum.taxi.name(),
                TBusinessEnum.taxiqr.name(),
                TBusinessEnum.t_substitute_pay.name(),
                TBusinessEnum.t_enterprise.name())) {
            return TBusinessEnum.taxi.name();
        }
        return TBusinessEnum.carpool.name();
    }

    /*
     * 随单券包退款分情况：
     *  先享后付
     *      在扣款中且无补偿金-> 调用paythrift关单 发二清
     *      在扣款中且有补偿金->存数据库，等扣完完成后发起退款 发二清
     *      扣款完成->正常操作，发起退款 发二清
     *
     */
    private RefundResultDO pauRefund(RefundDomainRequest refundDomainRequest) throws Exception {
        log.info("pauRefund start. request:{}", JsonMapper.toJson(refundDomainRequest));
        RefundResultDO refundResult = new RefundResultDO();
        // 总退款金额
        BigDecimal totalPrice = BigDecimal.ZERO;
        TradeOrderDO tradeOrderDO = refundDomainRequest.getTradeOrderDO();
        CalculateOrderDO calculateOrderDO = refundDomainRequest.getCalculateOrderDO();
        tradeOrderDO.setId(tradeOrderIdFactory.genUniqueId(tradeOrderDO.getPayTradeNo()+""));
        TradeOrderModel paymentTradeOrder = calculateOrderDO.getTradeOrderModel();
        TradeOrderNote tradeOrderNote = TradeOrderNote.create(paymentTradeOrder.getNote());
        Long paymentTradeOrderId = paymentTradeOrder.getId();
        Long userId = paymentTradeOrder.getUserId();
        boolean pauCutFlag = paymentPayAfterUseDomainService.isPauCutFlag(tradeOrderNote.getPauCutFlag());
        log.info("pauRefund  数据库cut状态:{}", tradeOrderNote.getPauCutFlag());
        //取消订单重新下单特殊处理
        boolean pauFinishOrder = false;
        TradeProductDetailsModel goodsModel = tradeProductDetailsRepository.getCouponPackageProductDetail(paymentTradeOrderId, userId);
        BigDecimal goodsAmount = goodsModel == null ? BigDecimal.ZERO : goodsModel.getPrice();
        if (StringUtils.equals(tradeOrderDO.getBusinessType(), TBusinessEnum.carpool_cancel.name())) {
            pauFinishOrder = true;
            tradeOrderNote.setCompensationAmount(refundDomainRequest.getDeductFee().toString());
        }
        Date now = new Date();
        //部分业务需在这里计算扣款金额，然后扣款或者终止扣款
        pauFinishOrder = isPauFinishOrder(paymentTradeOrderId, userId, calculateOrderDO.getThird(), pauCutFlag,
                pauFinishOrder, tradeOrderNote, paymentTradeOrder, goodsAmount, tradeOrderDO.getBusinessType(),now);
        //note字段更新
        paymentTradeOrder.setNote(tradeOrderNote.toJson());
        tradeOrderRepository.updateById(paymentTradeOrder);

        //没扣款成功且不关单 退款改为进行中，等扣款成功后再进行退款 目前这类业务只有 随单券包退款 补偿金退款 免单
        if (!refundDomainRequest.getRefundStatusSuccess() && !pauCutFlag && !pauFinishOrder) {
            log.info("退款单状态更新为退款中");
            //如果是权益包购买，需要将状态设置为已成功，因为在后面取消订单的情况不再增加权益包扣款金额了
            TradeProductDetailsModel ensureFeeModel = tradeProductDetailsRepository.getEnsureFeePackageProductDetail(paymentTradeOrderId, userId);
            if (ensureFeeModel != null && ensureFeeModel.getProductId().equals(refundDomainRequest.getTradeOrderDO().getMajorProductId())) {
                tradeOrderDO.setStatus(TradeOrderStatusEnum.success.name());
            } else {
                tradeOrderDO.setStatus(TradeOrderStatusEnum.processing.name());
            }
        }

        Consumer<TheaMessageContext> afterTrade = null;
        // 退三方
        if (calculateOrderDO.getThird().compareTo(BigDecimal.ZERO) > 0){
            tradeOrderDO.setThirdChannel(calculateOrderDO.getTradeOrderModel().getThirdChannel());
            tradeOrderDO.setSubType(tradeOrderNote.getPaymentWay());//子类型
            refundResult.setThirdChannel(tradeOrderDO.getThirdChannel());
            totalPrice = totalPrice.add(calculateOrderDO.getThird());
            if (pauCutFlag) {
                RefundMessageContext messageContext = new RefundMessageContext();
                messageContext.setUserId(tradeOrderDO.getUserId());
                messageContext.setPayTradeNo(String.valueOf(tradeOrderDO.getPayTradeNo()));
                messageContext.setRefundAmount(calculateOrderDO.getThird());
                messageContext.setTradeNo(String.valueOf(tradeOrderDO.getId()));
                messageContext.setRefundBusinessType(tradeOrderDO.getBusinessType());
                messageContext.setChannelType(tradeOrderDO.getThirdChannel());
                messageContext.setOrderNo(refundDomainRequest.getPaymentNo());
                RocketMqSendUtils.paymentRefund(messageContext);
            }

            afterTrade = theaMessageContext -> {
                String merchantInfo = tradeOrderNote.getMerchantId() == null ? null : tradeOrderNote.getMerchantId();
                theaMessageContext.setPayTime(tradeOrderDO.getSuccessTime())
                        .setPayType("default")
                        .setMerchantInfo(merchantInfo)
                        .setThirdPayTradeNo(paymentTradeOrder.getThirdTradeNo())
                        .setPayChannleStr(paymentTradeOrder.getThirdChannel());
            };
        }

        // 退余额
        if (calculateOrderDO.getPassenger().add(calculateOrderDO.getPassengerNotWithdraw()).compareTo(BigDecimal.ZERO) > 0){
            totalPrice = totalPrice.add(calculateOrderDO.getPassengerNotWithdraw()).add(calculateOrderDO.getPassenger());
            InnerAccountChangeItem innerAccountChangeItem = new InnerAccountChangeItem();
            innerAccountChangeItem.setAccountType(TOrderAccountTypeEnum.passenger.name());
            innerAccountChangeItem.setBusinessType(tradeOrderDO.getBusinessType());
            innerAccountChangeItem.setTradeType(tradeOrderDO.getTradeType());
            innerAccountChangeItem.setUserId(tradeOrderDO.getUserId());
            innerAccountChangeItem.setOrderId(tradeOrderDO.getId());
            innerAccountChangeItem.setPayTradeNO(tradeOrderDO.getPayTradeNo());
            innerAccountChangeItem.setAmount(calculateOrderDO.getPassenger());
            innerAccountChangeItem.setPassengerNotWithdraw(calculateOrderDO.getPassengerNotWithdraw());
            innerAccountChangeItem.setSysopId(refundDomainRequest.getSysOpId());
            AccountChangeUtil.accountIncome(innerAccountChangeItem);
        }

        // 退顺风金
        if (calculateOrderDO.getBonus().compareTo(BigDecimal.ZERO) > 0){
            totalPrice = totalPrice.add(calculateOrderDO.getBonus());
            InnerAccountChangeItem innerAccountChangeItem = new InnerAccountChangeItem();
            innerAccountChangeItem.setAccountType(TOrderAccountTypeEnum.bonus.name());
            innerAccountChangeItem.setTradeType(tradeOrderDO.getTradeType());
            innerAccountChangeItem.setBusinessType(tradeOrderDO.getBusinessType());
            innerAccountChangeItem.setUserId(tradeOrderDO.getUserId());
            innerAccountChangeItem.setOrderId(tradeOrderDO.getId());
            innerAccountChangeItem.setAmount(calculateOrderDO.getBonus());
            innerAccountChangeItem.setSysopId(refundDomainRequest.getSysOpId());
            innerAccountChangeItem.setPayTradeNO(tradeOrderDO.getPayTradeNo());
            AccountChangeUtil.accountIncome(innerAccountChangeItem);
            // 更新打车金使用次数
            Integer times = RedisClusterUtil.get(RedisKeyConstants.BONUS_USE_TIMES_KEY, tradeOrderDO.getUserId(), Integer.class);
            if (times == null || times < 0) {
                times = 0;
            }

            Integer origin = times;
            RedisClusterUtil.setToday(RedisKeyConstants.BONUS_USE_TIMES_KEY, tradeOrderDO.getUserId(), times > 0 ? times - 1 : 0, false);
            TransactionExtHelper.addRolledbackCallback(() -> RedisClusterUtil.setToday(RedisKeyConstants.BONUS_USE_TIMES_KEY,tradeOrderDO.getUserId() , origin, false));
        }

        // 退优惠券
        // 退部分优惠券标识
        boolean couponDiffRefundFlag = false;
        Long couponId = null;
        if (calculateOrderDO.getCoupon().compareTo(BigDecimal.ZERO) > 0){
            totalPrice = totalPrice.add(calculateOrderDO.getCoupon());
            TradeCouponDetailsDO tradeCouponDetailsDO = tradeCouponDetailsRepository.queryCoupon(tradeOrderDO.getPayTradeNo(),tradeOrderDO.getUserId());
            Preconditions.checkArgument(tradeCouponDetailsDO != null,"refund tradeCouponDetailsDO is null");
            couponId = tradeCouponDetailsDO.getCouponId();
            if (Boolean.TRUE.equals(refundDomainRequest.getCouponFlag())){
                CouponResult couponResult = couponThriftClient.couponResult(tradeCouponDetailsDO.getCouponId(),tradeOrderDO.getUserId(), tradeOrderDO.getPayTradeNo());
                if (couponResult.getData() != null && tradeOrderDO.getPayTradeNo().toString().equals(StringUtils.defaultIfEmpty(couponResult.getData().getOrder_id(),"").replace("-",""))) {
                    CouponMQEntity mqEntity = new CouponMQEntity();
                    mqEntity.setUserId(tradeOrderDO.getUserId());
                    mqEntity.setTradeOrderId(tradeOrderDO.getPayTradeNo());
                    mqEntity.setCouponId(tradeCouponDetailsDO.getCouponId());
                    mqEntity.setStatus("refund");
                    mqEntity.setOrderId(tradeOrderDO.getId());
                    RocketMqSendUtils.refundCoupon(mqEntity);
                    refundResult.setCouponId(tradeCouponDetailsDO.getCouponId());
                    // 插入数据库
                    tradeCouponDetailsDO.setTradeOrderId(tradeOrderDO.getId());
                    tradeCouponDetailsDO.setAmount(calculateOrderDO.getCoupon());
                    tradeCouponDetailsRepository.save(tradeCouponDetailsDO);
                }
            }
            if (refundDomainRequest.getNotifyCoupon() && !refundDomainRequest.getCouponFlag()){
                //部分退优惠券需要通知活动组
                log.info("部分退优惠券通知活动组. paymentId:{},amount:{}", paymentTradeOrder.getId(), refundDomainRequest.getCouponRealAmount());
                couponThriftClient.againUseCouponAmount(paymentTradeOrder.getId(), tradeCouponDetailsDO.getCouponId(), refundDomainRequest.getCouponRealAmount(),
                        paymentTradeOrder.getMajorProductId(), getProductType(paymentTradeOrder.getBusinessType()), paymentTradeOrder.getUserId());
                couponDiffRefundFlag = true;
            }
        }

        if (calculateOrderDO.getBonusRecover().compareTo(BigDecimal.ZERO) > 0){
            totalPrice = totalPrice.add(calculateOrderDO.getBonusRecover());
        }

        if (calculateOrderDO.getCouponRecover().compareTo(BigDecimal.ZERO) > 0){
            totalPrice = totalPrice.add(calculateOrderDO.getCouponRecover());
        }

        //补贴
        if(BigDecimalUtil.gtZero(calculateOrderDO.getPassengerSubsidy())){
            totalPrice = totalPrice.add(calculateOrderDO.getPassengerSubsidy());
        }

        if (totalPrice.compareTo(BigDecimal.ZERO) >= 0){
            //入库
            tradeOrderDO.setTotalPrice(totalPrice);
            if (StringUtils.isBlank(tradeOrderDO.getStatus())) {
                log.info("退款单状态更新为完成");
                tradeOrderDO.setStatus(TradeOrderStatusEnum.success.name());
            }
            TradeOrderModel tradeOrderModel = tradeOrderRepository.save(tradeOrderDO);
            //三方的amount核销状态需要单独处理，1.退款触发终止扣款的，直接更改为成功 2.合拼返款和退差价直接改为成功
            boolean thirdSuccessFlag = pauFinishOrder || StringUtils.equalsAny(tradeOrderDO.getBusinessType(),
                                                                            TBusinessEnum.carpool_multi.name(),
                                                                            TBusinessEnum.carpool_deduction.name());
            List<TradeAmountDetailsModel> amountDetails = tradeAmountDetailsRepository.save(calculateOrderDO, tradeOrderDO.getId(), tradeOrderModel, thirdSuccessFlag, couponDiffRefundFlag);
            if (totalPrice.compareTo(BigDecimal.ZERO) > 0) {
                //二清thea
                sendRefundTheaLiq(refundDomainRequest, afterTrade, couponId, tradeOrderModel, amountDetails, pauCutFlag, tradeOrderDO, calculateOrderDO, paymentTradeOrderId, userId);
            }
        }
        // 组装返回值
        refundResult.setTEnterpriseVip(calculateOrderDO.getTEnterpriseVip());
        refundResult.setTEnterprise(calculateOrderDO.getTEnterprise());
        refundResult.setCEnterpriseVip(calculateOrderDO.getCEnterpriseVip());
        refundResult.setBonusRecover(calculateOrderDO.getBonusRecover());
        refundResult.setCouponRecover(calculateOrderDO.getCouponRecover());
        refundResult.setTotalPrice(totalPrice);
        refundResult.setPayOrderId(tradeOrderDO.getPayTradeNo());
        refundResult.setRefundOrderId(tradeOrderDO.getId());
        refundResult.setCoupon(calculateOrderDO.getCoupon());
        refundResult.setThird(calculateOrderDO.getThird());
        refundResult.setPassenger(calculateOrderDO.getPassenger());
        refundResult.setPassengerNotWithdraw(calculateOrderDO.getPassengerNotWithdraw());
        refundResult.setBonus(calculateOrderDO.getBonus());
        refundResult.setSuccessTime(tradeOrderDO.getSuccessTime());
        //补贴回收金额
        refundResult.setPassengerSubsidy(calculateOrderDO.getPassengerSubsidy());
        return refundResult;

    }

    private void sendRefundTheaLiq(RefundDomainRequest refundDomainRequest, Consumer<TheaMessageContext> afterTrade, Long couponId,
                                   TradeOrderModel tradeOrderModel, List<TradeAmountDetailsModel> amountDetails,
                                   boolean pauCutFlag, TradeOrderDO tradeOrderDO, CalculateOrderDO calculateOrderDO,
                                   Long paymentTradeOrderId, Long userId) throws Exception {
        //cms全额退高速费退款单独发thea二清
        BigDecimal highwayAmount = BigDecimal.ZERO;
        if(RefundSceneTypeEnum.cmsAllRefundIncludeHighway.name().equals(refundDomainRequest.getScene())) {
            highwayAmount = tradeProductDetailsRepository.getHighwayAmount(paymentTradeOrderId, userId);
            if (highwayAmount.compareTo(BigDecimal.ZERO) > 0) {
                //查询高速费是否到账
                boolean highwayTransferStatus = false;
                if (refundDomainRequest.getDriverId() > 0) {
                    Long count = tradeOrderRepository.queryCountByPayTradeNoAndBusinessType(paymentTradeOrderId, TradeTypeEnum.transfer.name(), TBusinessEnum.carpool_highway_fee.name(), refundDomainRequest.getDriverId());
                    if (count > 0) {
                        highwayTransferStatus = true;
                    }
                }
                TradeOrderModel paymentTradeOrder = calculateOrderDO.getTradeOrderModel();
                TradeOrderNote tradeOrderNote = TradeOrderNote.create(paymentTradeOrder.getNote());
                PauHighwayFeeRefundTheaMessage pauHighwayFeeRefundTheaMessage = new PauHighwayFeeRefundTheaMessage();
                pauHighwayFeeRefundTheaMessage.setTradeOrder(tradeOrderModel)
                        .setHighwayAmount(highwayAmount)
                        .setTransferFlag(highwayTransferStatus)
                        .setPauCutFlag(pauCutFlag)
                        .setAfterTrade(theaMessageContext -> {
                            String merchantInfo = tradeOrderNote.getMerchantId() == null ? null : tradeOrderNote.getMerchantId();
                            theaMessageContext.setPayTime(tradeOrderDO.getSuccessTime())
                                    .setPayType("default")
                                    .setMerchantInfo(merchantInfo)
                                    .setThirdPayTradeNo(paymentTradeOrder.getThirdTradeNo())
                                    .setPayChannleStr(paymentTradeOrder.getThirdChannel());
                        })
                        .send();
                RefundPauHighwayLiquidationMessage liquidationMessage = new RefundPauHighwayLiquidationMessage();
                liquidationMessage.setRefundOrder(tradeOrderDO)
                        .setPaymentTradeOrderModel(calculateOrderDO.getTradeOrderModel())
                        .setTransferFlag(highwayTransferStatus)
                        .setHighwayAmount(highwayAmount)
                        .setDriverId(refundDomainRequest.getDriverId())
                        .setMajorOrderId(refundDomainRequest.getMajorOrderId())
                        .send();
            }
        }
        //thea+
        TradeProductDetailsModel ensureFeePackageProductDetail = null;
        if (TBusinessEnum.mall.name().equals(tradeOrderDO.getBusinessType())) {
            ensureFeePackageProductDetail = tradeProductDetailsRepository.getEnsureFeePackageProductDetail(paymentTradeOrderId, userId);
        }
        CommonTradeOrderTheaMessage theaMessage = new CommonTradeOrderTheaMessage();
        final Consumer<TheaMessageContext> finalAfterTrade = afterTrade;
        final Long finalCoupon = couponId;
        BigDecimal finalHighwayAmount = highwayAmount;
        theaMessage.setTradeOrder(tradeOrderModel)
                .setAmountDetails(amountDetails)
                .setProductDetail(ensureFeePackageProductDetail)
                .setAfterTrade(theaMessageContext -> {
                    if (finalAfterTrade != null){
                        finalAfterTrade.accept(theaMessageContext);
                    }
                    if (finalHighwayAmount.compareTo(BigDecimal.ZERO) > 0) {
                        //总金额再减去高速费的金额
                        theaMessageContext.setOrderPrice(theaMessageContext.getOrderPrice().subtract(finalHighwayAmount));
                        if (theaMessageContext.getThirdPay() != null) {
                            //修改三方付金额
                            theaMessageContext.setThirdPay(theaMessageContext.getThirdPay().subtract(finalHighwayAmount));
                        }
                    }
                    // 添加到帐状态
                    Map<String, Object> financeInfo = theaMessageContext.getFinanceInfo();
                    if (CollectionUtils.isEmpty(financeInfo)) {
                        financeInfo = new HashMap<>();
                    }
                    if (finalCoupon != null){
                        financeInfo.put(TheaExtraInfoKeyEnum.coupon_id.name(), finalCoupon);
                    }
                    financeInfo.put(TheaExtraInfoKeyEnum.transfer_status.name(),String.valueOf(refundDomainRequest.getTransferStatus()));
                    //若出现不用扣款情况，thea发扣款前还是扣款成功
                    financeInfo.put(TheaExtraInfoKeyEnum.pau_cut_flag.name(), pauCutFlag ? PauCutFlagEnum.success.name() : PauCutFlagEnum.before.name());
                    theaMessageContext.setFinanceInfo(financeInfo);
                }).send();
        //二清
        RefundLiquidationMessage liquidationMessage = new RefundLiquidationMessage();
        liquidationMessage.setRefundOrder(tradeOrderDO)
                .setCalculateOrderDO(calculateOrderDO)
                .setProductDetail(ensureFeePackageProductDetail)
                .setTransferFlag(refundDomainRequest.getTransferStatus())
                .setDriverId(refundDomainRequest.getDriverId())
                .setServiceFee(refundDomainRequest.getServiceFee())
                .setMajorOrderId(refundDomainRequest.getMajorOrderId())
                .setAfterTrade(messageContext -> {
                     if (finalHighwayAmount.compareTo(BigDecimal.ZERO) > 0) {
                         //总金额再减去高速费的金额
                         messageContext.setMoney(messageContext.getMoney() - AmountConvertUtil.yuan2penny(finalHighwayAmount));
                     }
                }).send();

    }

    /**
     * 部分业务需在这里计算扣款金额，然后扣款或者终止扣款
     */
    private boolean isPauFinishOrder(Long paymentTradeOrderId, Long userId, BigDecimal calculateThirdAmount,
                                     boolean pauCutFlag, boolean pauFinishOrder, TradeOrderNote tradeOrderNote,
                                     TradeOrderModel paymentTradeOrder, BigDecimal goodsAmount, String refundBusinessType, Date now) {
        //扣款成功不需要做下面处理
        if (pauCutFlag) {
            return pauFinishOrder;
        }
        //非这类退款也不需要做下面处理
        if (!StringUtils.equalsAny(refundBusinessType,
                TBusinessEnum.carpool_compensation.name(),
                TBusinessEnum.carpool_repeatpay.name(),
                TBusinessEnum.carpool_cancel.name(),
                TBusinessEnum.mall.name(),
                TBusinessEnum.carpool_station_cancel.name(),
                TBusinessEnum.carpool_station_repeatpay.name())) {
            return pauFinishOrder;
        }
        //新计算扣款金额逻辑
        //未扣款成功时，查询支付订单的三方支付金额 - 退款记录的三方支付金额总和 - 要退款的三方金额 = 0 代表需要终止三方扣款，不再使用业务标识判断
        Pair<BigDecimal, BigDecimal> thirdAmounts = getThirdAmount(paymentTradeOrderId, userId);
        BigDecimal paymentThirdAmount = thirdAmounts.getKey();
        BigDecimal refundThirdAmount = thirdAmounts.getValue();
        BigDecimal cutAmount = paymentThirdAmount.subtract(refundThirdAmount).subtract(calculateThirdAmount);
        log.info("pauRefund 计算扣款金额. 支付三方金额:{},已退款三方金额:{},本次退款三方金额:{},要扣款金额:{}",
                paymentThirdAmount, refundThirdAmount, calculateThirdAmount, cutAmount);
        if (cutAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("pauRefund 先用后付三方金额已全部退完，关闭订单");
            pauFinishOrder = true;
            if (!StringUtils.equals(tradeOrderNote.getPauCutFlag(), PaymentPauCutFlageEnum.finish.name())) {
                tradeOrderNote.setPauCutFlag(PaymentPauCutFlageEnum.finish.name());
                finishPauOrder(userId, paymentTradeOrderId, paymentTradeOrder.getThirdChannel(), paymentTradeOrder.getPaymentNo());
                tradeOrderNote.setPauFinishTime(TradeDateUtil.date2String(now));
                //更新amount状态和金额
                tradeAmountDetailsRepository.updatePauThirdAmountCancel(paymentTradeOrderId, userId, now, new Date());
            }
            //退款中的记录要改成退款成功
            updateRefundProcessingOrder(paymentTradeOrderId, userId, now);
        } else if (!TBusinessEnum.mall.name().equals(refundBusinessType)) {
            //只有创建状态才可发起扣款，其他状态不操作
            if(StringUtils.equals(tradeOrderNote.getPauCutFlag(), PaymentPauCutFlageEnum.create.name())) {
                log.info("pauRefund 发起扣款");
                //扣款金额>0 需要扣款
                tradeOrderNote.setPauCutFlag(PaymentPauCutFlageEnum.processing.name());
                //存储扣款金额
                tradeOrderNote.setPauCutAmount(String.valueOf(cutAmount));
                String pauCutComment;
                //防止出现补偿金和券包金额一致，记录错误情况
                if (goodsAmount.compareTo(BigDecimal.ZERO) > 0 && cutAmount.compareTo(goodsAmount) == 0) {
                    pauCutComment = "扣券包";
                } else if (goodsAmount.compareTo(BigDecimal.ZERO) <= 0 && cutAmount.compareTo(BigDecimal.ZERO) > 0){
                    pauCutComment = "扣补偿金";
                } else {
                    pauCutComment = "扣券包+补偿金";
                }
                tradeOrderNote.setPauCutComment(pauCutComment);
                tradeOrderNote.setPauStartCutTime(TradeDateUtil.date2String(now));
                RocketMqSendUtils.pauCutSendMq(paymentTradeOrderId, userId, paymentTradeOrder.getMajorProductId(), cutAmount, tradeOrderNote.getPaymentWay(),
                        paymentTradeOrder.getBusinessType(), paymentTradeOrder.getSource());

                tradeAmountDetailsRepository.updateThirdAmountLaunchTime(paymentTradeOrderId,  userId,now,BigDecimalUtil.defValue(tradeOrderNote.getPauCutAmount()));
                //开始扣款双写
                doubleWriteDomainService.doubleWritePauAction(paymentTradeOrderId, paymentTradeOrder.getMajorProductId(), PauActionEnums.startCut.name(), cutAmount, pauCutComment);
            }
        }
        return pauFinishOrder;
    }

    /**
     * 更新退款中订单的状态
     */
    private void updateRefundProcessingOrder(Long payTradeNo, Long userId, Date now) {
        TradeOrderModel queryCondition = new TradeOrderModel();
        queryCondition.setPayTradeNo(payTradeNo);
        queryCondition.setUserId(userId);
        queryCondition.setTradeType(TradeTypeEnum.refund.name());
        queryCondition.setStatus(TradeOrderStatusEnum.processing.name());
        List<TradeOrderModel> tradeOrderModels = tradeOrderRepository.selectTradedOrderListByQueryConditionAndPayTradeNo(queryCondition);
        if (CollectionUtils.isEmpty(tradeOrderModels)) {
            return;
        }
        TradeOrderModel updateOrder = new TradeOrderModel();
        for (TradeOrderModel refundOrder : tradeOrderModels) {
            log.info("updateRefundProcessingOrder refundId:{},bus:{}", refundOrder.getId(), refundOrder.getBusinessType());
            updateOrder.setId(refundOrder.getId());
            updateOrder.setUserId(refundOrder.getUserId());
            updateOrder.setStatus(TradeOrderStatusEnum.success.name());
            updateOrder.setSuccessTime(now);
            updateOrder.setUpdateTime(now);
            tradeOrderRepository.updateById(updateOrder);
            //更新amount
            tradeAmountDetailsRepository.updatePauThirdAmountCancel(refundOrder.getId(), refundOrder.getUserId(), now, new Date());
        }
    }


    /**
     * 查询支付订单的三方支付金额 - 退款记录的三方支付金额总和
     * @param paymentTradeOrderId 支付订单号
     * @param userId 用户id
     * @return 支付订单的三方支付金额 退款记录的三方支付金额总和
     */
    private Pair<BigDecimal, BigDecimal> getThirdAmount(Long paymentTradeOrderId, Long userId) {
        TradeAmountDetailsDO thirdAmountDetailsDO = tradeAmountDetailsRepository.queryAmountDetailsByTradeOrderIdAndType(paymentTradeOrderId, TOrderAccountTypeEnum.third.name(), userId);
        BigDecimal paymentThirdAmount = thirdAmountDetailsDO != null ? thirdAmountDetailsDO.getAmount() : BigDecimal.ZERO;
        List<TradeOrderModel> refundTradeOrderModels = tradeOrderRepository.selectTradedOrderListByPayTradeNoAndUserIdAndTradeType(paymentTradeOrderId, TradeTypeEnum.refund.name(), userId);
        BigDecimal refundThirdAmount = BigDecimal.ZERO;
        if (CollectionUtils.isEmpty(refundTradeOrderModels)) {
            return Pair.of(paymentThirdAmount, refundThirdAmount);
        }
        TradeAmountDetailsDO refundThirdAmountDetailsDO;
        for(TradeOrderModel tradeOrderModel : refundTradeOrderModels) {
            refundThirdAmountDetailsDO  = tradeAmountDetailsRepository.queryAmountDetailsByTradeOrderIdAndType(tradeOrderModel.getId(), TOrderAccountTypeEnum.third.name(), userId);
            if (refundThirdAmountDetailsDO != null && refundThirdAmountDetailsDO.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                refundThirdAmount = refundThirdAmount.add(refundThirdAmountDetailsDO.getAmount());
            }
        }
        return Pair.of(paymentThirdAmount, refundThirdAmount);
    }

    private void finishPauOrder(Long userId, Long paymentTradeOrderId, String thirdChannel, String paymentNo) {
        log.info("关闭先乘后付订单");
        if (StringUtils.isBlank(paymentNo)) {
            log.info("关闭先乘后付订单 paymentNo 为空");
            return;
        }
        //关单
        if (TChannelEnum.weixin.name().equals(thirdChannel)) {
            payThriftClient.cancelZhifufenOrder(userId, paymentTradeOrderId, "取消支付分订单" + paymentTradeOrderId);
        }else if (TChannelEnum.douyinpay.name().equals(thirdChannel)) {
            payThriftClient.cancelDouyinpayPauOrder(userId, paymentTradeOrderId, "取消支付分订单" + paymentTradeOrderId);
        }  else {
            payThriftClient.zhiMaFinishOrderHandle(userId, paymentTradeOrderId, "");
        }

    }

    /**
     * 扣款完成后继续处理之前发起的各种退款行为
     */
    public void pauContinueThirdRefund(TradeOrderModel paymentTradeOrder, TradeOrderModel refundOrder) throws ParseException {
        if (refundOrder == null || !TradeOrderStatusEnum.processing.name().equals(refundOrder.getStatus())) {
            return;
        }
        List<TradeAmountDetailsDO> tradeAmountDetailsDOS = tradeAmountDetailsRepository.queryAmountDetailsByOrderId(refundOrder.getId(), refundOrder.getUserId());
        if (CollectionUtils.isEmpty(tradeAmountDetailsDOS)) {
            log.info("pauContinueThirdRefund 无退款金额信息.");
            return;
        }
        TradeAmountDetailsDO tradeAmountDetailsDO = tradeAmountDetailsDOS.stream().filter(bo -> bo.getType().equals(TOrderAccountTypeEnum.third.name())).findFirst().orElse(null);
        if (tradeAmountDetailsDO == null || tradeAmountDetailsDO.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            log.info("pauContinueThirdRefund 无三方退款金额信息.");
            return;
        }
        BigDecimal amount = tradeAmountDetailsDO.getAmount();
        log.info("pauContinueThirdRefund start refund. amount:{},type:{}.", amount, refundOrder.getBusinessType());
        RefundMessageContext messageContext = new RefundMessageContext();
        messageContext.setUserId(refundOrder.getUserId());
        messageContext.setPayTradeNo(String.valueOf(refundOrder.getPayTradeNo()));
        messageContext.setRefundAmount(amount);
        messageContext.setTradeNo(String.valueOf(refundOrder.getId()));
        messageContext.setRefundBusinessType(refundOrder.getBusinessType());
        messageContext.setChannelType(refundOrder.getThirdChannel());
        messageContext.setOrderNo(paymentTradeOrder.getPaymentNo());
        RocketMqSendUtils.paymentRefund(messageContext);
        //更新库
        refundOrder.setStatus(TradeOrderStatusEnum.success.name());
        tradeOrderRepository.updateById(refundOrder);
        //thea
        theaPauRefund(paymentTradeOrder, refundOrder, amount);
        log.info("pauContinueThirdRefund success refund. amount:{},type:{}.", amount, refundOrder.getBusinessType());
    }

    public void theaPauRefund(TradeOrderModel paymentTradeOrder, TradeOrderModel refundOrder, BigDecimal thirdAmount) throws ParseException {
        PauCutOrRefundTheaMessage theaMessage = new PauCutOrRefundTheaMessage();
        String finalPayChannel = paymentTradeOrder.getThirdChannel();
        TradeOrderNote tradeOrderNote = TradeOrderNote.create(paymentTradeOrder.getNote());
        Date date = DateUtil.string2Date(tradeOrderNote.getPauCutTime());
        theaMessage.setTradeOrder(refundOrder)
                .setPauBusinessType(paymentTradeOrder.getBusinessType().equals(TBusinessEnum.carpool.name()) ? PauBusinessTypeEnum.carpool_pau.name() : PauBusinessTypeEnum.mall_pau.name())
                .setAfterTrade(theaMessageContext -> {
                    theaMessageContext.setOutTradeNo(String.valueOf(paymentTradeOrder.getId()));
                    theaMessageContext.setTradeNo("" + refundOrder.getId());
                    theaMessageContext.setOrderPrice(thirdAmount);
                    if (thirdAmount.compareTo(BigDecimal.ZERO) > 0) {
                        theaMessageContext.setThirdPay(thirdAmount);
                        theaMessageContext.setPayTime(date);
                        theaMessageContext.setPayChannleStr(finalPayChannel);
                        theaMessageContext.setThirdPayTradeNo(paymentTradeOrder.getThirdTradeNo());
                        if (isWeiXinPau(tradeOrderNote.getPaymentWay())) {
                            theaMessageContext.setPayType(TPaymentWayEnum.weixin_pau.name());
                            theaMessageContext.setMerchantInfo("1602592506");
                        }
                        if (isAlipayPau(tradeOrderNote.getPaymentWay())) {
                            theaMessageContext.setPayType("zhima");
                            theaMessageContext.setMerchantInfo("<EMAIL>");
                        }
                        if (isDouyinpayPau(tradeOrderNote.getPaymentWay())) {
                            theaMessageContext.setPayType(TPaymentWayEnum.douyinpay_pau.name());
                            theaMessageContext.setMerchantInfo(tradeOrderNote.getMerchantId());
                        }
                    }
                    log.info("发送退款thea+消息.body={}", JsonMapper.toJson(theaMessageContext));
                }).send();
    }

    public boolean isWeiXinPau(String paymentWay) {
        return TPaymentWayEnum.weixin_pau.name().equals(paymentWay);
    }

    public boolean isAlipayPau(String paymentWay) {
        return StringUtils.equalsAny(paymentWay, TPaymentWayEnum.zhima_pau.name(), TPaymentWayEnum.zhima_pau_sign.name());
    }

    public boolean isDouyinpayPau(String paymentWay) {
        return StringUtils.equalsAny(paymentWay, TPaymentWayEnum.douyinpay_pau.name(), TPaymentWayEnum.douyinpay_pau_sign.name());
    }
}
