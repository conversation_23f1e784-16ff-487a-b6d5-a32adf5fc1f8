package com.didapinche.trade.domin.transfer.taxiqr.bean;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.didapinche.agaue.common.exception.DidaCode;
import com.didapinche.server.commons.common.json.JsonMapper;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 11:41 2022/2/15
 **/
@Data
public class BaseResponse {
    /**
     * 三方返回的全部信息
     * 如果需要可自行解析
     */
    private Map<String, String> respBody;
    /**
     * 大前提是请求必须成功，如果请求失败（包括超时、参数错误等）侧为false
     * 1.支付查询是查询支付行为是否成功，如果支付查询返回状态不是成功的状态，也为false
     * 2.统一下单如果返回失败的状态也为false
     */
    private boolean success;

    private DidaCode didaCode;

    public void setRespBody(Object value) {
        this.respBody = JSON.parseObject(JsonMapper.toJson(value), new TypeReference<Map<String, String>>() {
        });
    }

    public BaseResponse fail() {
        this.success = Boolean.FALSE;
        return this;
    }

    public void ok() {
        this.success = Boolean.TRUE;
    }

}
