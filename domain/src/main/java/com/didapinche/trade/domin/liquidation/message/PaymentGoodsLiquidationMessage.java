package com.didapinche.trade.domin.liquidation.message;

import com.didapinche.agaue.tools.convert.AmountConvertUtil;
import com.didapinche.liquidate.common.entity.LiquidationPaymentEntity;
import com.didapinche.liquidate.common.enums.business.LiquidationRemarkPreEnums;
import com.didapinche.liquidate.common.enums.pingan.LiquidationAccountTypeEnums;
import com.didapinche.server.commons.common.util.DateUtil;
import com.didapinche.trade.domin.DO.goods.GoodsInfoDO;
import com.didapinche.trade.domin.bean_new.TradeAmountDetailsMultiple;
import com.didapinche.trade.infrastructure.entities.TradeOrderNote;
import com.didapinche.trade.infrastructure.tbl.TradeOrderModel;
import com.didapinche.trade.infrastructure.tbl.TradeProductDetailsModel;
import com.didapinche.trade.thrift.enums.TBusinessEnum;
import com.didapinche.trade.thrift.enums.TTradeProductTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
 * <AUTHOR>
 * @Date 2022/9/21 9:34
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class PaymentGoodsLiquidationMessage extends BaseLiquidationMessage {

    private TradeOrderModel tradeOrderModel;
    private List<TradeProductDetailsModel> tradeProductDetailsModels;
    private TradeAmountDetailsMultiple tradeAmountDetails;
    private GoodsInfoDO goodsInfo;

    @Override
    public String businessType() {
        return TBusinessEnum.mall.name();
    }

    @Override
    public String tradeType() {
        return tradeOrderModel.getTradeType();
    }

    @Override
    public LiquidationPaymentEntity buildLiquidationMessage() throws Exception {
        LiquidationPaymentEntity messageContext = new LiquidationPaymentEntity();
        messageContext.setSuffixTime(DateUtil.date2String(tradeOrderModel.getSuccessTime()));
        messageContext.setBusinessTime(DateUtil.date2String(tradeOrderModel.getSuccessTime()));
        messageContext.setPayChannelAssignMerNo(TradeOrderNote.create(tradeOrderModel.getNote()).getMerchantId());
        messageContext.setPayChannelType(tradeOrderModel.getThirdChannel());
        messageContext.setPayChannelTranSeqNo(tradeOrderModel.getPaymentNo());
        messageContext.setMoney(AmountConvertUtil.yuan2penny(goodsInfo.getGoodsPrice()));
        messageContext.setPayOutUserId(String.valueOf(tradeOrderModel.getUserId()));
        messageContext.setPayOutTypeEnum(LiquidationAccountTypeEnums.passenger.name());
        messageContext.setPayInTypeEnum(LiquidationAccountTypeEnums.function.name());
        // orderID
        messageContext.setTradeNo(String.valueOf(tradeOrderModel.getId()));
        messageContext.setTradeId(String.valueOf(tradeOrderModel.getId()));
        //写死
        messageContext.setPayInUserId("999999");
        //写死
        messageContext.setRemarkPreType(LiquidationRemarkPreEnums.goods_pay.name());
        //写死
        if (TTradeProductTypeEnum.ensureFee.name().equals(goodsInfo.getGoodsType())) {
            messageContext.setOrderType("goods_ensure_fee");
        } else {
            messageContext.setOrderType("goods_order");
        }
        TradeOrderNote note = TradeOrderNote.create(tradeOrderModel.getNote());
        if (TBusinessEnum.carpool_addition.name().equals(tradeOrderModel.getBusinessType())) {
            //补单需要查到主单orderId
            messageContext.setBusinessNo(String.valueOf(note.getMajorOrderId()));
        } else {
            messageContext.setBusinessNo(String.valueOf(tradeOrderModel.getId()));
        }
        if (this.getAfterTrade() != null) {
            this.getAfterTrade().accept(messageContext);
        }
        return messageContext;
    }
}
