package com.didapinche.trade.domin.bean_new;

import com.didapinche.agaue.tools.convert.AmountConvertUtil;
import com.didapinche.trade.infrastructure.tbl.TradeAmountDetailsModel;
import com.didapinche.trade.thrift.entities.TOrderAmountDetails;
import com.didapinche.trade.thrift.enums.TOrderAccountTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TradeAmountDetailsMultiple {

    /**
     * 优惠券账户
     */
    private BigDecimal coupon;
    /**
     * 三方支付账户
     */
    private BigDecimal third;
    /**
     * 可提现余额账户
     */
    private BigDecimal passenger;
    /**
     * 不可提现余额账户
     */
    private BigDecimal passengerNotWithdraw;
    /**
     * 顺风金账户
     */
    private BigDecimal bonus;
    /**
     * 顺风车vip企业账户
     */
    private BigDecimal cEnterpriseVip;
    /**
     * 出租车企业账户
     */
    private BigDecimal tEnterprise;
    /**
     * 出租车vip企业账户
     */
    private BigDecimal tEnterpriseVip;
    /**
     * 线下支付
     */
    private BigDecimal offline;

    /**
     * 高速费补贴金额
     */
    private BigDecimal highwaySubsidy;

    /**
     * 乘客补贴
     */
    private BigDecimal passengerSubsidy;

    public TradeAmountDetailsMultiple(){
        this.coupon = AmountConvertUtil.ZERO;
        this.third = AmountConvertUtil.ZERO;
        this.passenger = AmountConvertUtil.ZERO;
        this.passengerNotWithdraw = AmountConvertUtil.ZERO;
        this.bonus = AmountConvertUtil.ZERO;
        this.cEnterpriseVip = AmountConvertUtil.ZERO;
        this.tEnterprise = AmountConvertUtil.ZERO;
        this.tEnterpriseVip = AmountConvertUtil.ZERO;
        this.offline = AmountConvertUtil.ZERO;
        this.highwaySubsidy = AmountConvertUtil.ZERO;
        this.passengerSubsidy = AmountConvertUtil.ZERO;
    }
    public TradeAmountDetailsMultiple(List<TradeAmountDetailsModel> tradeAmountDetailsModelList){
        this();
        for (TradeAmountDetailsModel tradeAmountDetailsModel : tradeAmountDetailsModelList) {
            if (TOrderAccountTypeEnum.third.name().equals(tradeAmountDetailsModel.getType())) {
                third = tradeAmountDetailsModel.getAmount();
                continue;
            }
            if (TOrderAccountTypeEnum.passenger.name().equals(tradeAmountDetailsModel.getType())) {
                passenger = tradeAmountDetailsModel.getAmount();
                continue;
            }
            if (TOrderAccountTypeEnum.bonus.name().equals(tradeAmountDetailsModel.getType())) {
                bonus = tradeAmountDetailsModel.getAmount();
                continue;
            }
            if (TOrderAccountTypeEnum.coupon.name().equals(tradeAmountDetailsModel.getType())) {
                coupon = tradeAmountDetailsModel.getAmount();
                continue;
            }
            if (TOrderAccountTypeEnum.passenger_not_withdraw.name().equals(tradeAmountDetailsModel.getType())) {
                passengerNotWithdraw = tradeAmountDetailsModel.getAmount();
                continue;
            }
            if (TOrderAccountTypeEnum.c_enterprise_vip.name().equals(tradeAmountDetailsModel.getType())) {
                cEnterpriseVip = tradeAmountDetailsModel.getAmount();
                continue;
            }
            if (TOrderAccountTypeEnum.t_enterprise.name().equals(tradeAmountDetailsModel.getType())) {
                tEnterprise = tradeAmountDetailsModel.getAmount();
                continue;
            }
            if (TOrderAccountTypeEnum.t_enterprise_vip.name().equals(tradeAmountDetailsModel.getType())) {
                tEnterpriseVip = tradeAmountDetailsModel.getAmount();
                continue;
            }
            if (TOrderAccountTypeEnum.offline.name().equals(tradeAmountDetailsModel.getType())) {
                offline = tradeAmountDetailsModel.getAmount();
            }
            if (TOrderAccountTypeEnum.highway_subsidy.name().equals(tradeAmountDetailsModel.getType())) {
                highwaySubsidy = tradeAmountDetailsModel.getAmount();
            }

            if (TOrderAccountTypeEnum.passenger_subsidy.name().equals(tradeAmountDetailsModel.getType())) {
                passengerSubsidy = tradeAmountDetailsModel.getAmount();
            }
        }
    }

    /**
     * 转换金额
     *
     * @param orderAmountDetails -
     * @return -
     */
    public static TradeAmountDetailsMultiple tradeAmountDetailsMultipleByThriftDTO(List<TOrderAmountDetails> orderAmountDetails) {
        TradeAmountDetailsMultiple amountDetails = new TradeAmountDetailsMultiple();
        for (TOrderAmountDetails orderAmountDetail : orderAmountDetails) {
            if (TOrderAccountTypeEnum.passenger.name().equals(orderAmountDetail.getType())) {
                amountDetails.setPassenger(AmountConvertUtil.yuan2BigDecimal(orderAmountDetail.getAmount()));
                continue;
            }
            if (TOrderAccountTypeEnum.passenger_not_withdraw.name().equals(orderAmountDetail.getType())) {
                amountDetails.setPassengerNotWithdraw(AmountConvertUtil.yuan2BigDecimal(orderAmountDetail.getAmount()));
                continue;
            }
            if (TOrderAccountTypeEnum.third.name().equals(orderAmountDetail.getType())) {
                amountDetails.setThird(AmountConvertUtil.yuan2BigDecimal(orderAmountDetail.getAmount()));
                continue;
            }
            if (TOrderAccountTypeEnum.bonus.name().equals(orderAmountDetail.getType())) {
                amountDetails.setBonus(AmountConvertUtil.yuan2BigDecimal(orderAmountDetail.getAmount()));
                continue;
            }
            if (TOrderAccountTypeEnum.coupon.name().equals(orderAmountDetail.getType())) {
                amountDetails.setCoupon(AmountConvertUtil.yuan2BigDecimal(orderAmountDetail.getAmount()));
                continue;
            }
            if (TOrderAccountTypeEnum.c_enterprise_vip.name().equals(orderAmountDetail.getType())) {
                amountDetails.setCEnterpriseVip(AmountConvertUtil.yuan2BigDecimal(orderAmountDetail.getAmount()));
                continue;
            }
            if (TOrderAccountTypeEnum.t_enterprise_vip.name().equals(orderAmountDetail.getType())) {
                amountDetails.setTEnterpriseVip(AmountConvertUtil.yuan2BigDecimal(orderAmountDetail.getAmount()));
                continue;
            }
            if (TOrderAccountTypeEnum.t_enterprise.name().equals(orderAmountDetail.getType())) {
                amountDetails.setTEnterprise(AmountConvertUtil.yuan2BigDecimal(orderAmountDetail.getAmount()));
                continue;
            }
            if (TOrderAccountTypeEnum.offline.name().equals(orderAmountDetail.getType())) {
                amountDetails.setOffline(AmountConvertUtil.yuan2BigDecimal(orderAmountDetail.getAmount()));
            }
            if (TOrderAccountTypeEnum.highway_subsidy.name().equals(orderAmountDetail.getType())) {
                amountDetails.setHighwaySubsidy(AmountConvertUtil.yuan2BigDecimal(orderAmountDetail.getAmount()));
            }

            if (TOrderAccountTypeEnum.passenger_subsidy.name().equals(orderAmountDetail.getType())) {
                amountDetails.setPassengerSubsidy(AmountConvertUtil.yuan2BigDecimal(orderAmountDetail.getAmount()));
            }
        }
        return amountDetails;
    }
}
