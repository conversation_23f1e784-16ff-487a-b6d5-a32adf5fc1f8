package com.didapinche.trade.domin.DO.calculate;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CalculatePaymentInfoResult {
    private BigDecimal needPayAmount;
    private BigDecimal balanceAmount;
    private BigDecimal bonusAmount;
    private BigDecimal couponAmount;
    private BigDecimal passengerSubsidyAmount;
    private BigDecimal thirdAmount;
}
