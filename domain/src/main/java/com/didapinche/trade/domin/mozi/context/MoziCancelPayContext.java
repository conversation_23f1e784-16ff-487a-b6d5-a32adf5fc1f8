package com.didapinche.trade.domin.mozi.context;

import lombok.Data;

@Data
public class MoziCancelPayContext {

    /**
     * 订单id
     */
    private Long tradeOrderId;
    /**
     * 乘客uid
     */
    private Long payOutUserId;
    /**
     * 行程id
     */
    private String majorProductId;
    /**
     * 业务类型
     *
     * @see com.didapinche.trade.thrift.enums.TBusinessEnum
     */
    public String businessType;
    /**
     * 取消时间
     */
    private Long cancelTime;

}
