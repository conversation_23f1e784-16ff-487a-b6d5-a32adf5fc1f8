package com.didapinche.trade.domin.bill.action.confiscate;

import com.didapinche.trade.domin.bill.action.confiscate.bo.ConfiscateElementBO;
import com.didapinche.trade.domin.bill.action.confiscate.bo.StockConfiscateElementBO;
import com.didapinche.trade.infrastructure.service.impl.TradeBillModelService;
import com.didapinche.trade.infrastructure.tbl.TradeBillModel;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 没收
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ConfiscateActionVisitor implements ConfiscateVisitor {

    @Resource
    TradeBillModelService tradeBillModelService;

    @Override
    public void visit(ConfiscateElementBO bo) {
        TradeBillModel tradeBillModel = bo.updateTradeBillModel();
        boolean b = tradeBillModelService.updateTradeBillModelVersion(tradeBillModel);
        Preconditions.checkArgument(b,"update trade bill model version failed");
    }

    @Override
    public void visit(StockConfiscateElementBO bo) {
        //空实现
    }

}

