package com.didapinche.trade.domin.refund.service.refundconfiguration;

import com.didapinche.trade.domin.refund.DO.CalculateOrderDO;
import com.didapinche.trade.domin.refund.service.refundfactory.AbstractRefund;
import com.didapinche.trade.domin.refund.service.refundfactory.RefundFactory;
import com.didapinche.trade.domin.repository.TradeProductDetailsRepository;
import com.didapinche.trade.infrastructure.enums.RefundTypeEnum;
import com.didapinche.trade.thrift.enums.TTradeProductTypeEnum;
import com.google.common.base.Preconditions;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * 车站线下退款配置
 */
@Component("carpool_station_offline_true")
public class CmsCarpoolStationRefundAllConfiguration extends AbstractRefundConfiguration {

    @Override
    public AbstractRefund refund() {
        AbstractRefund refundService = RefundFactory.getRefundService(RefundTypeEnum.ACCOUNT_ORDER_DEDUCT);
        Preconditions.checkArgument(refundService != null,"carpool_station_offline_true refundService is empty");
        refundService.setAmountLinkedList(ALL_ACCOUNT_LIST);
        return refundService;
    }

    @Override
    public Map<Long,CalculateOrderDO> guaranteed(Long userId, Long majorOrderId, List<Long> orderIdList) {
        return new HashMap<>();
    }
}
