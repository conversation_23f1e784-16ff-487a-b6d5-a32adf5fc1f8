package com.didapinche.trade.domin.thea.message;

import com.didapinche.finance.mq.context.enums.StatusEnum;
import com.didapinche.finance.mq.context.enums.TheaExtraInfoKeyEnum;
import com.didapinche.finance.mq.context.enums.TradeTypeEnum;
import com.didapinche.finance.mq.context.message.messagecontext.TheaMessageContext;
import com.didapinche.finance.mq.context.utils.TheaFinanceInfoUtil;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.trade.domin.DO.goods.GoodsInfoDO;
import com.didapinche.trade.infrastructure.entities.TradeOrderNote;
import com.didapinche.trade.infrastructure.enums.GoodsTypeEnum;
import com.didapinche.trade.infrastructure.tbl.TradeAmountDetailsModel;
import com.didapinche.trade.infrastructure.tbl.TradeOrderModel;
import com.didapinche.trade.infrastructure.tbl.TradeProductDetailsModel;
import com.didapinche.trade.thrift.enums.TBusinessEnum;
import com.didapinche.trade.thrift.enums.TOrderAccountTypeEnum;
import com.didapinche.trade.thrift.enums.TTradeProductTypeEnum;
import com.google.common.base.Preconditions;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/9/24 13:44
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper=false)
public class CommonTradeOrderTheaMessage extends BaseTheaMessage {

    private TradeOrderModel tradeOrder;
    private List<TradeAmountDetailsModel> amountDetails;
    private TradeProductDetailsModel productDetail;
    private GoodsInfoDO goodsEnsureFeeInfo; //随单购
    private List<TradeProductDetailsModel> payProductDetails;

    @Override
    public String businessType() {
        if (productDetail != null && GoodsTypeEnum.ensureFee.name().equals(productDetail.getType())) {
            return "ensure_fee";
        } else if (goodsEnsureFeeInfo != null
                && GoodsTypeEnum.ensureFee.name().equals(goodsEnsureFeeInfo.getGoodsType())
                && StringUtils.equalsAny(tradeOrder.getBusinessType(), TBusinessEnum.carpool_additional.name(), TBusinessEnum.mall.name())){
            return "ensure_fee";
        } else if (payProductDetails != null && payProductDetails.get(0) != null && TTradeProductTypeEnum.compensation.name().equals(payProductDetails.get(0).getType())) {
            return "carpool_compensation";
        } else {
            return tradeOrder.getBusinessType();
        }
    }

    @Override
    public String tradeType() {
        return tradeOrder.getTradeType();
    }

    @Override
    public TheaMessageContext buildTheaMessage() {
        Preconditions.checkArgument(tradeOrder != null,"tradeOrder is null");
        Preconditions.checkArgument(amountDetails != null,"amountDetails is null");
        TheaMessageContext messageContext = new TheaMessageContext();
        TheaFinanceInfoUtil financeInfoUtil = new TheaFinanceInfoUtil();
        TradeOrderNote tradeOrderNote = JsonMapper.json2Bean(tradeOrder.getNote(), TradeOrderNote.class);
        if (tradeOrderNote != null) {
            if (StringUtils.isNotBlank(tradeOrderNote.getStartCityId())) {
                financeInfoUtil.putString("start_city_id", String.valueOf(tradeOrderNote.getStartCityId()));
            }
            if (StringUtils.isNotBlank(tradeOrderNote.getEndCityId())) {
                financeInfoUtil.putString("end_city_id", String.valueOf(tradeOrderNote.getEndCityId()));
            }
        }
        for (TradeAmountDetailsModel tradeAmountDetailsModel : amountDetails){
            if (TOrderAccountTypeEnum.third.name().equals(tradeAmountDetailsModel.getType())) {
                messageContext.setThirdPay(tradeAmountDetailsModel.getAmount());
                continue;
            }
            if (StringUtils.equalsAny(tradeAmountDetailsModel.getType(),TOrderAccountTypeEnum.passenger.name(),TOrderAccountTypeEnum.driver.name(),TOrderAccountTypeEnum.taxi.name())) {
                messageContext.setWalletPay(tradeAmountDetailsModel.getAmount());
                continue;
            }
            if (TOrderAccountTypeEnum.bonus.name().equals(tradeAmountDetailsModel.getType())) {
                financeInfoUtil.putBigDecimal(TheaExtraInfoKeyEnum.bonus.name(),tradeAmountDetailsModel.getAmount());
                continue;
            }
            if (TOrderAccountTypeEnum.coupon.name().equals(tradeAmountDetailsModel.getType())) {
                messageContext.setCouponPay(tradeAmountDetailsModel.getAmount());
                continue;
            }
            if (TOrderAccountTypeEnum.passenger_not_withdraw.name().equals(tradeAmountDetailsModel.getType())) {
                financeInfoUtil.putBigDecimal(TheaExtraInfoKeyEnum.non_cash.name(),tradeAmountDetailsModel.getAmount());
                continue;
            }
            if (TOrderAccountTypeEnum.c_enterprise_vip.name().equals(tradeAmountDetailsModel.getType())) {
                financeInfoUtil.putBigDecimal("enterprise_credit_account", tradeAmountDetailsModel.getAmount());
                financeInfoUtil.putString("enterprise_id", String.valueOf(tradeOrder.getCompanyId()));
                continue;
            }
            if (TOrderAccountTypeEnum.t_enterprise_vip.name().equals(tradeAmountDetailsModel.getType())) {
                financeInfoUtil.putBigDecimal("enterprise_credit_account", tradeAmountDetailsModel.getAmount());
                financeInfoUtil.putString("enterprise_id", String.valueOf(tradeOrder.getCompanyId()));
                continue;
            }
            if (TOrderAccountTypeEnum.t_enterprise.name().equals(tradeAmountDetailsModel.getType())) {
                financeInfoUtil.putBigDecimal("enterprise_recharge_award", tradeAmountDetailsModel.getAmount());
                continue;
            }
            if (TOrderAccountTypeEnum.recover_bonus.name().equals(tradeAmountDetailsModel.getType())){
                financeInfoUtil.putBigDecimal("recover_bonus", tradeAmountDetailsModel.getAmount());
                continue;
            }
            if (TOrderAccountTypeEnum.recover_coupon.name().equals(tradeAmountDetailsModel.getType())){
                financeInfoUtil.putBigDecimal("recover_coupon", tradeAmountDetailsModel.getAmount());
            }
            if (TOrderAccountTypeEnum.highway_subsidy.name().equals(tradeAmountDetailsModel.getType())){
                financeInfoUtil.putBigDecimal("sys_highway", tradeAmountDetailsModel.getAmount());
            }
            if (TOrderAccountTypeEnum.recover_highway_subsidy.name().equals(tradeAmountDetailsModel.getType())){
                financeInfoUtil.putBigDecimal("recover_sys_highway", tradeAmountDetailsModel.getAmount());
            }
            //乘客补贴
            if (TOrderAccountTypeEnum.passenger_subsidy.name().equals(tradeAmountDetailsModel.getType())) {
                financeInfoUtil.putBigDecimal(TheaExtraInfoKeyEnum.passenger_subsidy.name(), tradeAmountDetailsModel.getAmount());
            }
        }
        if (StringUtils.equalsAny(tradeOrder.getTradeType(),TradeTypeEnum.transfer.name(),TradeTypeEnum.refund.name())){
            messageContext.setToUserId(tradeOrder.getUserId().intValue());
        }else {
            messageContext.setFromUserId(tradeOrder.getUserId().intValue());
        }
        messageContext.setOrderPrice(tradeOrder.getTotalPrice())
                .setStatus(StatusEnum.finish.getStatus())
                .setSource(this.tradeOrder.getSource())
                .setFinanceInfo(financeInfoUtil.getExtraInfo())
                .setTradeNo(tradeOrder.getId().toString());
        if (tradeOrder.getPayTradeNo() != null && tradeOrder.getPayTradeNo() > 0) {
            messageContext.setOutTradeNo(String.valueOf(tradeOrder.getPayTradeNo()));
        } else {
            messageContext.setOutTradeNo(StringUtils.isBlank(tradeOrder.getMajorProductId()) ? "" : tradeOrder.getMajorProductId());
        }
        financeInfoUtil.putString("request_id",StringUtils.isBlank(tradeOrder.getMajorProductId()) ? "" : tradeOrder.getMajorProductId());
        TradeOrderNote note = TradeOrderNote.create(tradeOrder.getNote());
        if (note != null && StringUtils.isNotBlank(note.getRideId())) {
            financeInfoUtil.putString("ride_id", note.getRideId());
        }
        if (this.getAfterTrade() != null){
            this.getAfterTrade().accept(messageContext);
        }
        return messageContext;
    }
}
