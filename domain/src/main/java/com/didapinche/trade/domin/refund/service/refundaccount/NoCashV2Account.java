package com.didapinche.trade.domin.refund.service.refundaccount;

import com.didapinche.trade.domin.refund.DO.CalculateOrderDO;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2022/7/19 10:37
 * @Version 1.0
 */
public class NoCashV2Account extends AbstractAccount {

    @Override
    public BigDecimal accountRemaining(CalculateOrderDO amountDetails) {
        return amountDetails.getPassengerNotWithdraw();
    }

    @Override
    public void addAmount(CalculateOrderDO amountDetails, BigDecimal amount) {
        amountDetails.setPassenger(amountDetails.getPassenger().add(amount));
    }
}
