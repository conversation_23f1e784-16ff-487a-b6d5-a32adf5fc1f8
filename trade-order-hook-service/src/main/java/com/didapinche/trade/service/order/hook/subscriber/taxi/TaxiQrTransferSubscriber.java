package com.didapinche.trade.service.order.hook.subscriber.taxi;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.didapinche.agaue.common.exception.DException;
import com.didapinche.commons.rocketmq.AbstractRocketMQSubscriber;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.trade.infrastructure.enums.RocketMQGroupEnum;
import com.didapinche.trade.infrastructure.enums.RocketMQTopicEnum;
import com.didapinche.trade.infrastructure.enums.order.TradeOrderStatusEnum;
import com.didapinche.trade.infrastructure.exception.TradeErrorCode;
import com.didapinche.trade.infrastructure.mq.bean.callback.PaymentSuccessCallBackEntity;
import com.didapinche.trade.infrastructure.mq.bean.callback.PaymentSuccessCallBackPaymentExtraEntity;
import com.didapinche.trade.thrift.TradeThriftService;
import com.didapinche.trade.thrift.entities.TTaxiQrTransferRequest;
import com.didapinche.trade.thrift.entities.TTaxiQrTransferResponse;
import com.didapinche.trade.thrift.enums.TBusinessEnum;
import com.didapinche.trade.thrift.enums.TOrderSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class TaxiQrTransferSubscriber extends AbstractRocketMQSubscriber<PaymentSuccessCallBackEntity> {

    @Resource
    private TradeThriftService.Iface tradeThriftService;

    @ApolloJsonValue("${taxiqr.not.transfer.order.list:[]}")
    Set<String> orderList;

    @Override
    public void init() {
        setGroup(RocketMQGroupEnum.C_BDC_PAYMENT_TAXIQR_DRIVER_TRANSFER.name());
        setTopic(RocketMQTopicEnum.t_bdc_payment_trade_success.name());
        setTags(TBusinessEnum.taxiqr.name());
        setRetryTimesAware(Boolean.TRUE);
    }

    @Override
    public void execute(PaymentSuccessCallBackEntity entity, int reConsumeTimes) {
        log.info("智慧码分账 start. entity:{}, reConsumeTimes:{}", JsonMapper.toJson(entity), reConsumeTimes);
        if (entity.getUserId() <= 0L
                || StringUtils.isAnyBlank(entity.getTradeOrderId(), entity.getMajorProductId())) {
            log.error("智慧码分账 参数错误. params:{}", JsonMapper.toJson(entity));
            return;
        }
        PaymentSuccessCallBackPaymentExtraEntity paymentExtra = entity.getPaymentExtra();
        if (paymentExtra == null) {
            log.info("智慧码分账 结束.paymentExtra==null orderId:{}", entity.getTradeOrderId());
            return;
        }
        if (orderList != null && orderList.contains(entity.getTradeOrderId())) {
            log.info("智慧码分账 结束.命中不处理配置 orderId:{}", entity.getTradeOrderId());
            return;
        }
        TTaxiQrTransferRequest request = new TTaxiQrTransferRequest()
                .setUserId(entity.getUserId())
                .setMajorProductId(entity.getMajorProductId())
                .setTradeOrderId(Long.parseLong(entity.getTradeOrderId()))
                .setThirdChannel(entity.getThirdChannel())
                .setBusinessType(TBusinessEnum.taxi_fee.name())
                .setSource(TOrderSourceEnum.payment.name())
                .setProfitSharingRetry(paymentExtra.getProfitSharingRetry());
        try {
            TTaxiQrTransferResponse response = tradeThriftService.doTaxiQrTransfer(request);
            if (response.getCode() == 140011 || response.getCode() == 140012 || response.getCode() == 140025 || response.getCode() == 140044) {
                //订单校验数据异常，不需要重试
                log.error("智慧码分账 返回校验异常code无需重试. orderId:{},response:{}", entity.getTradeOrderId(), JsonMapper.toJson(response));
                return;
            }
            if (response.getCode() != 0 || response.getData() == null || StringUtils.equals(response.getData().getStatus(), TradeOrderStatusEnum.processing.name())) {
                log.info("智慧码分账失败或者分账中.orderId:{},response:{}", entity.getTradeOrderId(), JsonMapper.toJson(response));
                throw new DException(response.getCode(), response.getMessage());
            }
            log.info("智慧码分账成功. orderId:{},response:{}", entity.getTradeOrderId(), JsonMapper.toJson(response));
        } catch (Exception e) {
            log.error("智慧码分账 调用error. orderId:{}", entity.getTradeOrderId(), e);
            throw new DException(TradeErrorCode.SYSTEM_104_ERROR);
        }

    }
}
