package com.didapinche.trade.service.order.hook.subscriber.coupon;

import com.alibaba.fastjson.JSON;
import com.didapinche.commons.rocketmq.AbstractRocketMQSubscriber;
import com.didapinche.server.commons.common.util.LoadPropertyUtil;
import com.didapinche.server.commons.common.util.RedisClusterUtil;
import com.didapinche.trade.infrastructure.enums.RocketMQGroupEnum;
import com.didapinche.trade.infrastructure.enums.RocketMQTopicEnum;
import com.didapinche.trade.infrastructure.mq.bean.SavedMoneyEntity;
import com.didapinche.trade.infrastructure.util.TradeDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.didapinche.trade.infrastructure.constants.RedisKeyConstants.SAVED_MONEY_SINCE_YEAR_BEGIN;

/**
 * 消费大数据生产的消息
 * 统计用户从当年1月1日到T+1总计使用的优惠券（出租车+顺风车） + 顺风金
 * 存储介质：Redis 过期时间：到次年1月1日 + 1天
 * 发送的金额为总量，直接替换即可，不需要累加
 */
@Component
@Slf4j
public class SavedMoneyStatisticsSubscriber extends AbstractRocketMQSubscriber<SavedMoneyEntity> {

    @Override
    public void init() {
        setGroup(RocketMQGroupEnum.C_BDC_PAYMENT_SAVED_MONEY.name());
        setTopic(RocketMQTopicEnum.t_bdc_payment_saved_money_statistics.name());
    }

    @Override
    public void execute(SavedMoneyEntity entity) {
        if (entity == null || StringUtils.isAnyBlank(entity.getUserId(), entity.getTime(), entity.getTotalAmount())) {
            log.error("invalid entity: {}", JSON.toJSONString(entity));
            return;
        }
        if (!LoadPropertyUtil.getProperty("savedMoney.consume.switch", Boolean.class, true)) {
            return;
        }
        String userId = entity.getUserId();
        String sTime = entity.getTime(); // 统计时间 yyyy-MM-dd
        Date current = new Date();
        log.info("start consuming, time: {}, userId: {}", sTime, userId);
        long nextYearFirstDay = TradeDateUtil.getNextYearFirstDay(sTime);
        // 次年1月1日+1天+随机10分钟之内过期
        int expire = (int) ((nextYearFirstDay - current.getTime()) / 1000) + (int) (Math.random() * 600 + 1);
        Map<String, Object> value = new HashMap<>();
        value.put("time", entity.getTime());
        value.put("amount", entity.getTotalAmount());
        RedisClusterUtil.setAndExpire(SAVED_MONEY_SINCE_YEAR_BEGIN, userId, value, expire, false);
    }


}
