package com.didapinche.trade.service.order.hook.subscriber.coupon;

import com.didapinche.commons.rocketmq.AbstractRocketMQSubscriber;
import com.didapinche.coupon.thrift.CouponResult;
import com.didapinche.coupon.thrift.CouponService;
import com.didapinche.coupon.thrift.TUpdateCouponStatusParams;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.server.commons.common.metrics.InterfaceMonitor;
import com.didapinche.server.commons.common.rocketmq.RocketMQUtil;
import com.didapinche.server.commons.common.util.LoadPropertyUtil;
import com.didapinche.thrift.common.util.DateUtil;
import com.didapinche.trade.application.service.support.UpdateTradeAmountStatusService;
import com.didapinche.trade.domin.async.datas.UpdateTradeAmountStatusData;
import com.didapinche.trade.infrastructure.constants.RedisKeyConstants;
import com.didapinche.trade.infrastructure.enums.RocketMQGroupEnum;
import com.didapinche.trade.infrastructure.enums.RocketMQTopicEnum;
import com.didapinche.trade.infrastructure.mq.MqConsistencyUtil;
import com.didapinche.trade.infrastructure.mq.bean.CouponMQEntity;
import com.didapinche.trade.thrift.enums.AmountReceiptStatusEnum;
import com.didapinche.trade.thrift.enums.TOrderAccountTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@DependsOn({"ZEUS-couponService"})
@Slf4j
public class CouponUpdateSubscriber extends AbstractRocketMQSubscriber<CouponMQEntity> {

    @Resource
    private CouponService.Iface couponService;
    @Resource
    private UpdateTradeAmountStatusService updateTradeAmountStatusService;


    @Override
    public void init() {
        setGroup(RocketMQGroupEnum.C_SERVER_ALLPAYMENT_COUPON.name());
        setTopic(RocketMQTopicEnum.t_server_coupon.name());
    }

    @Override
    public void execute(CouponMQEntity entity) {
        if (entity == null || entity.getCouponId() == 0 || StringUtils.isBlank(entity.getStatus())) {
            log.error("CouponUpdateSubscriber参数有误.entity {}", JsonMapper.toJson(entity));
            return;
        }
        log.info("CouponUpdateSubscriber修改优惠券开始.param:{}", JsonMapper.toJson(entity));
        long orderId = entity.getTradeOrderId();
        String orderType = entity.getTradeOrderType();
        Long userId = entity.getUserId();

        TUpdateCouponStatusParams couponStatusParams = new TUpdateCouponStatusParams();
        couponStatusParams.setCouponId(String.valueOf(entity.getCouponId()));
        couponStatusParams.setType(entity.getStatus());
        couponStatusParams.setCouponCredit(String.valueOf(entity.getCouponCredit()));
        if (StringUtils.isNotBlank(entity.getRideId())) {
            couponStatusParams.setRideId(entity.getRideId());
        }
        if ("carpool".equals(orderType)) {
            couponStatusParams.setOrderId(String.valueOf(orderId));
        } else if ("taxi".equals(orderType)) {
            couponStatusParams.setOrderId("-" + orderId);
        }
        couponStatusParams.setUserId(String.valueOf(userId));

        couponStatusParams.setTradeChannel(entity.getTradeChannel());
        couponStatusParams.setBizNo(entity.getBizNo());
        couponStatusParams.setTotalFee(entity.getTotalFee());
        couponStatusParams.setAlipayTradeNo(entity.getAlipayTradeNo());

        try {
            CouponResult result = couponService.updateCouponStatusByParams(couponStatusParams);
            if (result == null || (result.getCode() != 0 && result.getCode() != 5035 && result.getCode() != 1528 && result.getCode() != 5038 && result.getCode() != 5037)) {
                log.error("updateCouponStatus fail. orderId：{} 接口返回结果:{}",
                        orderId, result == null ? "is null" : JsonMapper.toJson(result));
                InterfaceMonitor.getInstance().addWarn("updateCouponStatusException", InterfaceMonitor.TYPE_CUSTOM);
                throw new Exception("retry updateCouponStatus. coupon_id:{}" + entity.getCouponId());
            }
        } catch (Exception e) {
            log.error("updateCouponStatusByParams error.orderId:{}", orderId, e);
        }

        if ("use".equals(entity.getStatus())) {
            Map<String, String> map = new HashMap<>();
            map.put("coupon_id", entity.getCouponId() + "");
            map.put("order_id", orderId + "");
            map.put("order_type", orderType);
            RocketMQUtil.sendToMQ(orderId + "_" + orderType, map, RocketMQTopicEnum.t_server_wechat_card_write_off.name());
        }
        InterfaceMonitor.getInstance().addTotal("updateCouponStatus", InterfaceMonitor.TYPE_CUSTOM);
        InterfaceMonitor.getInstance().addTotal("updateCouponStatus_" + orderType, entity.getStatus());
        try {
            MqConsistencyUtil.hdel(RedisKeyConstants.COUPON_PAYOUT_CONFORMITY, entity.getOrderCid());
        } catch (Exception e) {
            log.error("删除优惠券扣除一致性核对数据出错 orderId:{}", orderId, e);
        }
        sendUpdateAmountStatus(entity);
        log.info("CouponUpdateSubscriber 修改优惠券结束...... orderId:{{}", orderId);
    }

    private void sendUpdateAmountStatus(CouponMQEntity mqEntity) {
        if (!LoadPropertyUtil.getProperty("trade.update.amount.coupon.enable", Boolean.class, true)) {
            return;
        }
        UpdateTradeAmountStatusData data = new UpdateTradeAmountStatusData()
                .setUserId(mqEntity.getUserId())
                .setPaymentOrderId(mqEntity.getTradeOrderId())
                .setOrderId(mqEntity.getOrderId())
                .setAmountType(TOrderAccountTypeEnum.coupon.name())
                .setReceiptStatus(AmountReceiptStatusEnum.cut_success.name());
        try {
            data.setReceiptTime(DateUtil.date2String2(new Date()));
        } catch (Exception e) {
            //ignore
        }
        updateTradeAmountStatusService.updateStatus(data);
    }
}
