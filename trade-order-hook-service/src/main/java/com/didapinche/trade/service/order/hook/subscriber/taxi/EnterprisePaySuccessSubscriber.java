package com.didapinche.trade.service.order.hook.subscriber.taxi;

import com.didapinche.commons.rocketmq.AbstractRocketMQSubscriber;
import com.didapinche.server.commons.common.json.JsonMapper;
import com.didapinche.thrift.taxiwritepay.TResult;
import com.didapinche.thrift.taxiwritepay.TTaxiEpPayCallbackParams;
import com.didapinche.thrift.taxiwritepay.TaxiwritepayThriftService;
import com.didapinche.trade.infrastructure.enums.RocketMQGroupEnum;
import com.didapinche.trade.infrastructure.enums.RocketMQTopicEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Map;

/**
 * Created by zhuxue on 2018/6/22
 */
@Component
@Slf4j
@DependsOn({"ZEUS-taxiwritepayThriftService"})
public class EnterprisePaySuccessSubscriber extends AbstractRocketMQSubscriber<Map<String, Object>> {

    @Resource
    private TaxiwritepayThriftService.Iface taxiwritepayThriftService;

    @Override
    public void init() {
        setGroup(RocketMQGroupEnum.C_SERVER_ENTERPRISEPAY_SUCCESS.name());
        setTopic(RocketMQTopicEnum.t_server_enterprisepay_success.name());
    }

    @Override
    public void execute(Map<String, Object> enterprisePaySuccessMap) throws Exception {
        if (enterprisePaySuccessMap == null ||
                enterprisePaySuccessMap.get("money") == null ||
                enterprisePaySuccessMap.get("orderCid") == null ||
                enterprisePaySuccessMap.get("payChannelCid") == null ||
                enterprisePaySuccessMap.get("payUid") == null) {
            log.error("EnterprisePaySuccessSubscriber 企业支付成功修改状态失败. enterprisePaySuccessMap:{}", JsonMapper.toJson(enterprisePaySuccessMap));
            return;
        }

        log.info("EnterprisePaySuccessSubscriber 企业支付成功修改状态开始. enterprisePaySuccessMap:{}", JsonMapper.toJson(enterprisePaySuccessMap));
        try {
            TResult result = doTaxiEpPayCallBack(
                    new BigDecimal(enterprisePaySuccessMap.get("money").toString()).setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
                    enterprisePaySuccessMap.get("orderCid").toString(),
                    enterprisePaySuccessMap.get("tradeNo").toString(),
                    enterprisePaySuccessMap.get("payChannelCid").toString(),
                    enterprisePaySuccessMap.get("payUid").toString(),
                    enterprisePaySuccessMap.get("longitude") == null ? "0" : enterprisePaySuccessMap.get("longitude").toString(),
                    enterprisePaySuccessMap.get("latitude") == null ? "0" : enterprisePaySuccessMap.get("latitude").toString(),
                    enterprisePaySuccessMap.get("receiptAmount") == null ? "" : enterprisePaySuccessMap.get("receiptAmount").toString(),
                    enterprisePaySuccessMap.get("voucherDetailList") == null ? "" : enterprisePaySuccessMap.get("voucherDetailList").toString(),
                    enterprisePaySuccessMap.get("fundBillList") == null ? "" : enterprisePaySuccessMap.get("fundBillList").toString(),
                    enterprisePaySuccessMap.get("unionpayTradeNo") == null ? "" : enterprisePaySuccessMap.get("unionpayTradeNo").toString(),
                    true,
                    enterprisePaySuccessMap.get("thirdTradePayTime") == null ? "" : enterprisePaySuccessMap.get("thirdTradePayTime").toString(),
                    enterprisePaySuccessMap.get("orderNo") == null ? null : enterprisePaySuccessMap.get("orderNo").toString());
            if (result == null || result.getCode() != 0) {
                log.error("EnterprisePaySuccessSubscriber 企业支付成功修改状态失败.enterprisePaySuccessMap:{},result:{}", JsonMapper.toJson(enterprisePaySuccessMap), JsonMapper.toJson(result));
                throw new RuntimeException("修改ride,order状态失败,enterprisePaySuccessMap:" + JsonMapper.toJson(enterprisePaySuccessMap));
            }
        } catch (Exception e) {
            log.error("EnterprisePaySuccessSubscriber 企业支付成功修改状态发生异常.enterprisePaySuccessMap:{}", JsonMapper.toJson(enterprisePaySuccessMap), e);
            throw e;
        }
        log.info("EnterprisePaySuccessSubscriber 企业支付成功修改状态结束. enterprisePaySuccessMap:{}", JsonMapper.toJson(enterprisePaySuccessMap));
    }

    public TResult doTaxiEpPayCallBack(String money, String taxiOrderCid, String tradeNo,
                                       String payChannel, String payUid, String longitude,
                                       String latitude, String receiptAmount, String voucherDetailList,
                                       String fundBillList, String unionpayTradeNo, boolean isSuccess,
                                       String thirdTradeTime,
                                       String orderNo) throws Exception {
        TTaxiEpPayCallbackParams tTaxiEpPayCallbackParams = new TTaxiEpPayCallbackParams();
        tTaxiEpPayCallbackParams.setMoney(money);
        tTaxiEpPayCallbackParams.setTaxiOrderCid(taxiOrderCid);
        tTaxiEpPayCallbackParams.setTradeNo(tradeNo);
        tTaxiEpPayCallbackParams.setPayChannel(payChannel);
        tTaxiEpPayCallbackParams.setPayUid(payUid);
        tTaxiEpPayCallbackParams.setLongitude(longitude);
        tTaxiEpPayCallbackParams.setLatitude(latitude);
        tTaxiEpPayCallbackParams.setReceiptAmount(receiptAmount);
        tTaxiEpPayCallbackParams.setVoucherDetailList(voucherDetailList);
        tTaxiEpPayCallbackParams.setFundBillList(fundBillList);
        tTaxiEpPayCallbackParams.setUnionpayTradeNo(unionpayTradeNo);
        tTaxiEpPayCallbackParams.setIsSuccess(isSuccess);
        tTaxiEpPayCallbackParams.setThirdTradePayTime(thirdTradeTime);
        tTaxiEpPayCallbackParams.setOrderNo(orderNo);
        return taxiwritepayThriftService.doTaxiEpPayCallBack(tTaxiEpPayCallbackParams);
    }
}
