# TradeThriftServiceImpl 方法中 productId/majorProductId/pid 使用情况分析文档

## 概述

本文档详细分析了 `TradeThriftServiceImpl.java` 文件中指定方法对 `productId`、`majorProductId` 或 `pid` 属性的使用情况，包括使用位置、业务含义和具体代码实现。

---

## 1. doCommonConfiscate 方法

**方法位置**: `trade-thrift-service/src/main/java/com/didapinche/trade/service/thrift/thriftimpl/TradeThriftServiceImpl.java:685-721`

**入参对象**: `CommonConfiscateRequest`

**相关属性**:
- `productId` (String) - 业务侧id

### 使用位置详情:

#### 1.1 分布式锁 (第690行)
```java
@DisLock("#request.payTradeId > 0 ? 'trade:'+#request.payTradeId : 'trade:'+#request.businessType+':'+#request.productId+':confiscate'")
```
**位置**: `TradeThriftServiceImpl.java:690`
**用途**: 当 `payTradeId` 为0时，使用 `productId` 构建分布式锁键

#### 1.2 日志键生成 (第698行)
```java
@Override
public String addLogKey() {
    return String.format(LOG_KEY_FORMAT, request.getBusinessType(), request.getProductId());
}
```
**位置**: `TradeThriftServiceImpl.java:698`
**用途**: 生成日志追踪键，格式为 `{businessType}:{productId}`

#### 1.3 业务逻辑锁 (第708行)
```java
LockUtil<Result<ConfiscateResult>> lockUtil = new LockUtil<Result<ConfiscateResult>>(String.format(LOCK_KEY_FORMAT, request.getBusinessType(), request.getProductId(), TradeTypeEnum.confiscate.name()), () -> {
```
**位置**: `TradeThriftServiceImpl.java:708`
**用途**: 使用 `businessType`、`productId` 和交易类型构建业务锁键

#### 1.4 数据库路由 (第711行)
```java
Routing routing = new Routing()
    .setLookupId(routingThriftService.getLookupKeyById(request.getPayTradeId() > 0 ? String.valueOf(request.getPayTradeId()) : request.getProductId()));
```
**位置**: `TradeThriftServiceImpl.java:711`
**用途**: 当 `payTradeId` 为0时，使用 `productId` 进行数据库路由

#### 1.5 TradeOrderDO设置 (CommonConfiscateServiceImpl.java:84)
```java
tradeOrderDO.setMajorProductId(request.getProductId());
```
**位置**: `application/src/main/java/com/didapinche/trade/application/service/impl/confiscate/CommonConfiscateServiceImpl.java:84`
**用途**: 将 `productId` 设置为交易订单DO的 `majorProductId`

#### 1.6 业务逻辑处理 (CommonConfiscateServiceImpl.java:103-110)
```java
if (validNum(request.getProductId())) {//根据产品ID查询
    String bizLineEnum = BizLineMapper.getBizLineEnum(request.getBusinessType());
    TradeBillModel tradeBillModel = billTradeActionService.billOrderByProductId(request.getProductId(), bizLineEnum);
    if(tradeBillModel!=null){
        tradeOrderDO.setBillId(tradeBillModel.getId());
        updateTaxiDriverId(request, tradeBillModel.getPayerId());
    }else {
        log.debug("查询不到产品【{}】对应的账单", request.getProductId());
    }
}
```
**位置**: `application/src/main/java/com/didapinche/trade/application/service/impl/confiscate/CommonConfiscateServiceImpl.java:103-110`
**用途**: 根据 `productId` 查询对应的账单信息

#### 1.7 BO对象转换 (CommonConfiscateServiceImpl.java:189)
```java
private ConfiscateBO generateConfiscateBO(CommonConfiscateRequest request) {
    ConfiscateBO confiscateBO= new ConfiscateBO();
    // ... 其他属性设置
    confiscateBO.setMajorProductId(request.getProductId());
    // ... 其他属性设置
    return confiscateBO;
}
```
**位置**: `application/src/main/java/com/didapinche/trade/application/service/impl/confiscate/CommonConfiscateServiceImpl.java:189`
**用途**: 将 `productId` 设置为 `ConfiscateBO` 的 `majorProductId` 属性

**业务含义**: `productId` 在没收业务中用作业务订单的唯一标识，用于分布式锁、业务锁、数据库路由、查询对应的账单信息并设置为 `majorProductId`。

---

## 2. thirdRefund 方法

**方法位置**: `trade-thrift-service/src/main/java/com/didapinche/trade/service/thrift/thriftimpl/TradeThriftServiceImpl.java:489-524`

**入参对象**: `ThirdRefundRequest`

**相关属性**:
- `productId` (String) - 业务侧id
- `orderId` (Long) - 交易订单id

### 使用位置详情:

#### 2.1 日志键生成 (第499行)
```java
@Override
public String addLogKey() {
    return String.format(LOG_KEY_FORMAT, thirdRefundRequest.getBusinessType(), thirdRefundRequest.getProductId());
}
```
**位置**: `TradeThriftServiceImpl.java:499`
**用途**: 生成日志追踪键，格式为 `{businessType}:{productId}`

#### 2.2 RefundBO设置 (ThirdRefundServiceImpl.java:75)
```java
refundBO.setProductId(request.getProductId())
```
**位置**: `application/src/main/java/com/didapinche/trade/application/service/impl/refund/ThirdRefundServiceImpl.java:75`
**用途**: 将 `productId` 设置到退款BO中

#### 2.3 业务状态同步 (ThirdRefundServiceImpl.java:82)
```java
if (refund.getCode() == 0){
    orderStatusDomainService.syncOrderRefundStatus(request.getBusinessType(),refund.getData(),request.getUserId(),request.getProductId(), OrderRefundCaseEnum.refund,refundBO.getRefunDate());
    doubleWrite(request,refund.getData());
}
```
**位置**: `application/src/main/java/com/didapinche/trade/application/service/impl/refund/ThirdRefundServiceImpl.java:82`
**用途**: 同步订单退款状态时使用 `productId` 作为业务标识

#### 2.4 双写检查中的majorProductId使用 (ThirdRefundServiceImpl.java:104,110,124)
```java
// 设置订单Cid
refundParam.setOrderCid(TBusinessEnum.mall.name().equals(tradeOrderModel.getBusinessType())?tradeOrderModel.getMajorProductId():tradeOrderModel.getId().toString());
// 检查majorProductId是否为数字
if (!TBusinessEnum.mall.name().equals(tradeOrderModel.getBusinessType()) &&
        tradeOrderModel.getMajorProductId().matches("[0-9]+")){
    refundParam.setBusinessOrderId(Long.parseLong(tradeOrderModel.getMajorProductId()));
}
// 设置路由ID
doubleWriteCheckParam.setRouteId(tradeOrderModel.getMajorProductId() + "");
```
**位置**: `application/src/main/java/com/didapinche/trade/application/service/impl/refund/ThirdRefundServiceImpl.java:104,110,124`
**用途**: 在双写检查中使用 `majorProductId` 设置订单Cid、业务订单ID和路由ID

**注意**: 数据库路由使用的是 `orderId` 而不是 `productId`：
```java
Routing routing = new Routing()
    .setLookupId(routingThriftService.getLookupKeyById(String.valueOf(thirdRefundRequest.getOrderId())));
```

**业务含义**: `productId` 在第三方退款业务中主要用于日志记录、业务标识和状态同步，而 `majorProductId` 在双写检查中用于设置各种业务标识。数据库路由使用的是 `orderId`。

---

## 3. thirdCashRefund 方法

**方法位置**: `trade-thrift-service/src/main/java/com/didapinche/trade/service/thrift/thriftimpl/TradeThriftServiceImpl.java:363-399`

**入参对象**: `ThirdCashRefundRequest`

**相关属性**:
- `productId` (String) - 业务侧id
- `majorOrderId` (Long) - 业务侧关联记录交易id

### 使用位置详情:

#### 3.1 分布式锁 (第367行)
```java
@DisLock("'trade:'+#thirdCashRefundRequest.majorOrderId")
```
**位置**: `TradeThriftServiceImpl.java:367`
**用途**: 使用 `majorOrderId` 构建分布式锁键

#### 3.2 日志键生成 (第374行)
```java
@Override
public String addLogKey() {
    return String.format(LOG_KEY_FORMAT, thirdCashRefundRequest.getBusinessType(), thirdCashRefundRequest.getProductId());
}
```
**位置**: `TradeThriftServiceImpl.java:374`
**用途**: 使用 `productId` 生成日志追踪键

#### 3.3 业务逻辑锁 (第384行)
```java
LockUtil<Result<List<RefundResult>>> lockUtil = new LockUtil<Result<List<RefundResult>>>(thirdCashRefundRequest.getMajorOrderId(), () -> {
```
**位置**: `TradeThriftServiceImpl.java:384`
**用途**: 使用 `majorOrderId` 构建业务锁

#### 3.4 数据库路由 (第386行)
```java
Routing routing = new Routing()
    .setLookupId(routingThriftService.getLookupKeyById(String.valueOf(thirdCashRefundRequest.getMajorOrderId())));
```
**位置**: `TradeThriftServiceImpl.java:386`
**用途**: 使用 `majorOrderId` 进行数据库路由

#### 3.5 业务状态同步 (ThirdCashRefundServiceImpl.java:79)
```java
if (refund.getCode() == 0){
    orderStatusDomainService.syncOrderRefundStatus(request.getBusinessType(),refund.getData(),request.getUserId(),request.getProductId(), OrderRefundCaseEnum.buchangjin,refundBO.getRefunDate());
    doubleWrite(request,refund.getData());
}
```
**位置**: `application/src/main/java/com/didapinche/trade/application/service/impl/refund/ThirdCashRefundServiceImpl.java:79`
**用途**: 使用 `productId` 进行订单状态同步

**业务含义**:
- `productId` 用于日志记录、业务标识和状态同步
- `majorOrderId` 用于分布式锁控制、业务锁控制和数据库路由

---

## 4. thirdCashDeductRefund 方法

**方法位置**: `trade-thrift-service/src/main/java/com/didapinche/trade/service/thrift/thriftimpl/TradeThriftServiceImpl.java:282-319`

**入参对象**: `ThirdCashDeductRefundRequest`

**相关属性**:
- `productId` (String) - 业务侧id
- `majorOrderId` (Long) - 业务侧关联记录交易id

### 使用位置详情:

#### 4.1 分布式锁 (第285行)
```java
@DisLock("'trade:'+#thirdCashDeductRefundRequest.majorOrderId")
```
**位置**: `TradeThriftServiceImpl.java:285`
**用途**: 使用 `majorOrderId` 构建分布式锁键

#### 4.2 日志键生成 (第291行)
```java
@Override
public String addLogKey() {
    return String.format(LOG_KEY_FORMAT, thirdCashDeductRefundRequest.getBusinessType(), thirdCashDeductRefundRequest.getProductId());
}
```
**位置**: `TradeThriftServiceImpl.java:291`
**用途**: 使用 `productId` 生成日志追踪键

#### 4.3 业务逻辑锁 (第301行)
```java
LockUtil<Result<List<RefundResult>>> lockUtil = new LockUtil<Result<List<RefundResult>>>(thirdCashDeductRefundRequest.getMajorOrderId(), () -> {
```
**位置**: `TradeThriftServiceImpl.java:301`
**用途**: 使用 `majorOrderId` 构建业务锁

#### 4.4 数据库路由 (第303行)
```java
Routing routing = new Routing()
    .setLookupId(routingThriftService.getLookupKeyById(String.valueOf(thirdCashDeductRefundRequest.getMajorOrderId())))
    .setDynamicTableSuffix(TableSuffixUtils.getSuffix(thirdCashDeductRefundRequest.getUserId()));
```
**位置**: `TradeThriftServiceImpl.java:303-304`
**用途**: 使用 `majorOrderId` 进行数据库路由

#### 4.5 Thea和二清消息发送 (第306行)
```java
pauPaymentApplicationService.sendPaymentTheaAndLiq(thirdCashDeductRefundRequest.getMajorOrderId(), thirdCashDeductRefundRequest.getUserId());
```
**位置**: `TradeThriftServiceImpl.java:306`
**用途**: 使用 `majorOrderId` 和 `userId` 发送支付相关的Thea和二清消息

#### 4.6 业务逻辑处理 (CancelOrderRefundServiceImpl.java:73)
```java
refundBO.setProductId(request.getProductId())//主单ID(行程ID)
```
**位置**: `application/src/main/java/com/didapinche/trade/application/service/impl/refund/CancelOrderRefundServiceImpl.java:73`
**用途**: 将 `productId` 设置为退款BO的产品ID

**业务含义**:
- `productId` 用于日志记录和业务标识
- `majorOrderId` 用于分布式锁、业务锁、数据库路由和消息发送
- 该方法特别涉及Thea和二清消息的发送

---

## 5. priceDifferencesRefund 方法

**方法位置**: `trade-thrift-service/src/main/java/com/didapinche/trade/service/thrift/thriftimpl/TradeThriftServiceImpl.java:447-478`

**入参对象**: `PriceDifferencesRefundRequest`

**相关属性**:
- `productId` (String) - 业务侧id
- `majorOrderId` (Long) - 业务侧关联记录交易id

### 使用位置详情:

#### 5.1 分布式锁 (第450行)
```java
@DisLock("'trade:'+#priceDifferencesRefundRequest.majorOrderId")
```
**位置**: `TradeThriftServiceImpl.java:450`
**用途**: 使用 `majorOrderId` 构建分布式锁键

#### 5.2 日志键生成 (第457行)
```java
@Override
public String addLogKey() {
    return String.format(LOG_KEY_FORMAT, priceDifferencesRefundRequest.getBusinessType(), priceDifferencesRefundRequest.getProductId());
}
```
**位置**: `TradeThriftServiceImpl.java:457`
**用途**: 使用 `productId` 生成日志追踪键

#### 5.3 业务逻辑锁 (第467行)
```java
LockUtil<Result<List<RefundResult>>> lockUtil = new LockUtil<Result<List<RefundResult>>>(priceDifferencesRefundRequest.getMajorOrderId(), () -> {
```
**位置**: `TradeThriftServiceImpl.java:467`
**用途**: 使用 `majorOrderId` 构建业务锁

#### 5.4 数据库路由 (第469行)
```java
Routing routing = new Routing()
    .setLookupId(routingThriftService.getLookupKeyById(String.valueOf(priceDifferencesRefundRequest.getMajorOrderId())));
```
**位置**: `TradeThriftServiceImpl.java:469`
**用途**: 使用 `majorOrderId` 进行数据库路由

**业务含义**:
- `productId` 用于日志记录和业务标识
- `majorOrderId` 用于分布式锁控制、业务锁控制和数据库路由
- 该方法专门处理退差价业务

---

## 6. doTransferWithCheckPay 方法

**方法位置**: `trade-thrift-service/src/main/java/com/didapinche/trade/service/thrift/thriftimpl/TradeThriftServiceImpl.java:645-684`

**入参对象**: `TransferWithCheckPayRequest`

**相关属性**:
- `productId` (String) - 业务侧id
- `payTradeId` (Long) - 支付订单id

### 使用位置详情:

#### 6.1 分布式锁 (第648行)
```java
@DisLock("'trade:'+#request.payTradeId")
```
**位置**: `TradeThriftServiceImpl.java:648`
**用途**: 使用 `payTradeId` 构建分布式锁键

#### 6.2 日志键生成 (第655行)
```java
@Override
public String addLogKey() {
    return String.format(LOG_KEY_FORMAT, request.getBusinessType(), request.getProductId());
}
```
**位置**: `TradeThriftServiceImpl.java:655`
**用途**: 使用 `productId` 生成日志追踪键

#### 6.3 业务逻辑锁 (第665行)
```java
LockUtil<Result<TransferResult>> lockUtil = new LockUtil<Result<TransferResult>>(request.getPayTradeId(), () -> {
```
**位置**: `TradeThriftServiceImpl.java:665`
**用途**: 使用 `payTradeId` 构建业务锁

#### 6.4 数据库路由 (第667行)
```java
Routing routing = new Routing()
    .setLookupId(routingThriftService.getLookupKeyById(String.valueOf(request.getPayTradeId())));
```
**位置**: `TradeThriftServiceImpl.java:667`
**用途**: 使用 `payTradeId` 进行数据库路由

#### 6.5 Thea和二清消息发送 (第669行)
```java
pauPaymentApplicationService.sendPaymentTheaAndLiq(request.getPayTradeId(), request.getPayUserId());
```
**位置**: `TradeThriftServiceImpl.java:669`
**用途**: 使用 `payTradeId` 和 `payUserId` 发送支付相关的Thea和二清消息

#### 6.6 业务对象设置 (TransferWithCheckPayServiceImpl.java:89)
```java
tradeOrderDO.setMajorProductId(request.getProductId());//行程ID
```
**位置**: `application/src/main/java/com/didapinche/trade/application/service/impl/transfer/TransferWithCheckPayServiceImpl.java:89`
**用途**: 将 `productId` 设置为交易订单DO的 `majorProductId`

**业务含义**:
- `productId` 用于日志记录和设置为 `majorProductId`
- `payTradeId` 用于分布式锁、业务锁、数据库路由和消息发送
- 该方法处理有支付订单的到账业务，并发送Thea和二清消息

---

## 7. doCommonTransfer 方法

**方法位置**: `trade-thrift-service/src/main/java/com/didapinche/trade/service/thrift/thriftimpl/TradeThriftServiceImpl.java:607-643`

**入参对象**: `CommonTransferRequest`

**相关属性**:
- `productId` (String) - 业务侧id
- `payTradeId` (Long) - 支付订单id (可选)

### 使用位置详情:

#### 7.1 分布式锁 (第610行)
```java
@DisLock("#request.payTradeId > 0 ? 'trade:'+#request.payTradeId : 'trade:'+#request.businessType+':'+#request.productId+':transfer'")
```
**位置**: `TradeThriftServiceImpl.java:610`
**用途**: 根据条件使用 `payTradeId` 或 `productId` 构建分布式锁键

#### 7.2 日志键生成 (第617行)
```java
@Override
public String addLogKey() {
    return String.format(LOG_KEY_FORMAT, request.getBusinessType(), request.getProductId());
}
```
**位置**: `TradeThriftServiceImpl.java:617`
**用途**: 使用 `productId` 生成日志追踪键

#### 7.3 业务逻辑锁 (第627行)
```java
LockUtil<Result<TransferResult>> lockUtil = new LockUtil<Result<TransferResult>>(String.format(LOCK_KEY_FORMAT, request.getBusinessType(), request.getProductId(), TradeTypeEnum.transfer.name()), () -> {
```
**位置**: `TradeThriftServiceImpl.java:627`
**用途**: 使用 `businessType`、`productId` 和交易类型构建业务锁键

#### 7.4 数据库路由 (第629行)
```java
Routing routing = new Routing()
    .setLookupId(routingThriftService.getLookupKeyById(request.getPayTradeId() > 0 ? String.valueOf(request.getPayTradeId()) : request.getProductId()));
```
**位置**: `TradeThriftServiceImpl.java:629`
**用途**: 根据条件使用 `payTradeId` 或 `productId` 进行数据库路由

#### 7.5 majorProductId获取 (CommonTransferServiceImpl.java:123)
```java
if (validNums(commonTransferRequest.getPayTradeId(),commonTransferRequest.getPayuserId()) ) {
    TradeOrderModel tradeOrderModel = tradeOrderModelService.selectById(commonTransferRequest.getPayTradeId(), commonTransferRequest.getPayuserId());
    if (tradeOrderModel != null && validNum(tradeOrderModel.getBillId())) {
        tradeOrderDO.setBillId(tradeOrderModel.getId());
        majorId=tradeOrderModel.getMajorProductId();
    }
}
```
**位置**: `application/src/main/java/com/didapinche/trade/application/service/impl/transfer/CommonTransferServiceImpl.java:123`
**用途**: 从原交易订单中获取 `majorProductId`

#### 7.6 重复转账检查 (TransferDomainService.java:154,156)
```java
List<TradeOrderDO> tradeOrderDos = tradeOrderRepository.queryTradeOrderList(tradeOrderDO.getMajorProductId(), TradeTypeEnum.transfer.name(), tradeOrderDO.getBusinessType(), transferDO.getUserId());
if (!CollectionUtils.isEmpty(tradeOrderDos)){
    log.warn("{}：{}重复转账",tradeOrderDO.getBusinessType(),tradeOrderDO.getMajorProductId());
}
```
**位置**: `domain/src/main/java/com/didapinche/trade/domin/transfer/service/TransferDomainService.java:154,156`
**用途**: 使用 `majorProductId` 进行重复转账检查

**业务含义**:
- `productId` 用于日志记录、分布式锁（当无 `payTradeId` 时）、业务锁和数据库路由（当无 `payTradeId` 时）
- `payTradeId` 优先用于分布式锁和数据库路由（当存在时）
- `majorProductId` 用于重复转账检查和业务关联
- 该方法处理通用转账业务，支持有无支付订单两种场景

---

## 8. carpoolMultiRefund 方法

**方法位置**: `trade-thrift-service/src/main/java/com/didapinche/trade/service/thrift/thriftimpl/TradeThriftServiceImpl.java:402-440`

**入参对象**: `CarpoolMultiRefundRequest`

**相关属性**:
- `productId` (String) - 业务侧id
- `majorOrderId` (Long) - 业务侧关联记录交易id

### 使用位置详情:

#### 8.1 分布式锁 (第405行)
```java
@DisLock("'trade:'+#carpoolMultiRefundRequest.majorOrderId")
```
**位置**: `TradeThriftServiceImpl.java:405`
**用途**: 使用 `majorOrderId` 构建分布式锁键

#### 8.2 日志键生成 (第412行)
```java
@Override
public String addLogKey() {
    return String.format(LOG_KEY_FORMAT, carpoolMultiRefundRequest.getBusinessType(), carpoolMultiRefundRequest.getProductId());
}
```
**位置**: `TradeThriftServiceImpl.java:412`
**用途**: 使用 `productId` 生成日志追踪键

#### 8.3 业务逻辑锁 (第423行)
```java
LockUtil<Result<List<RefundResult>>> lockUtil = new LockUtil<Result<List<RefundResult>>>(carpoolMultiRefundRequest.getMajorOrderId(), () -> {
```
**位置**: `TradeThriftServiceImpl.java:423`
**用途**: 使用 `majorOrderId` 构建业务锁

#### 8.4 数据库路由 (第425行)
```java
Routing routing = new Routing()
    .setLookupId(routingThriftService.getLookupKeyById(String.valueOf(carpoolMultiRefundRequest.getMajorOrderId())))
    .setDynamicTableSuffix(TableSuffixUtils.getSuffix(carpoolMultiRefundRequest.getUserId()));
```
**位置**: `TradeThriftServiceImpl.java:425-426`
**用途**: 使用 `majorOrderId` 进行数据库路由，并设置动态表后缀

#### 8.5 退款检查中的productId使用 (CarpoolMultiRefundServiceImpl.java:95)
```java
checkRefundDomainRequest.setProductId(request.getProductId());
```
**位置**: `application/src/main/java/com/didapinche/trade/application/service/impl/refund/CarpoolMultiRefundServiceImpl.java:95`
**用途**: 在退款检查请求中设置 `productId`

#### 8.6 重复调用检查 (CarpoolMultiRefundServiceImpl.java:98)
```java
log.warn("{} {}重复调用",this.getClass(),request.getProductId());
```
**位置**: `application/src/main/java/com/didapinche/trade/application/service/impl/refund/CarpoolMultiRefundServiceImpl.java:98`
**用途**: 在重复调用检查时记录 `productId`

#### 8.7 业务状态同步 (CarpoolMultiRefundServiceImpl.java:167)
```java
orderStatusDomainService.syncOrderRefundStatus(request.getBusinessType(),refundResultList,
        request.getUserId(),request.getProductId(), OrderRefundCaseEnum.he_pin,now);
```
**位置**: `application/src/main/java/com/didapinche/trade/application/service/impl/refund/CarpoolMultiRefundServiceImpl.java:167`
**用途**: 同步订单退款状态时使用 `productId`

#### 8.8 双写检查路由ID (CarpoolMultiRefundServiceImpl.java:216)
```java
doubleWriteCheckParam.setRouteId(request.getProductId() + "");
```
**位置**: `application/src/main/java/com/didapinche/trade/application/service/impl/refund/CarpoolMultiRefundServiceImpl.java:216`
**用途**: 在双写检查中设置路由ID

**业务含义**:
- `productId` 用于日志记录、业务标识、退款检查、状态同步和双写检查路由
- `majorOrderId` 用于分布式锁控制、业务锁控制和数据库路由
- 该方法专门处理顺风车合拼返款业务

---

## 总结

### 使用模式分析

1. **日志记录**: 所有方法都使用 `productId` 生成日志追踪键
2. **分布式锁**: 大多数方法使用 `majorOrderId` 或 `payTradeId` 构建分布式锁，部分方法在没有交易ID时使用 `productId`
3. **数据库路由**: 优先使用 `majorOrderId` 或 `payTradeId`，其次使用 `productId`
4. **业务逻辑锁**: 使用 `businessType`、`productId` 和交易类型构建业务锁键
5. **业务对象设置**: `productId` 经常被设置为 `TradeOrderDO` 的 `majorProductId`
6. **重复检查**: 使用 `majorProductId` 进行重复操作检查
7. **状态同步**: 使用 `productId` 进行订单状态同步
8. **双写检查**: 使用 `productId` 或 `majorProductId` 设置路由ID

### 字段含义

- **productId**: 业务侧订单ID，如行程ID、商品ID等
- **majorProductId**: 主产品ID，通常与 `productId` 相同，用于标识业务订单
- **majorOrderId**: 业务侧关联记录交易ID，用于关联原始交易
- **payTradeId**: 支付订单ID，用于标识具体的支付交易
- **orderId**: 交易订单ID，用于标识具体的交易记录

### 关键发现

1. **Thea和二清消息**: `thirdCashDeductRefund` 和 `doTransferWithCheckPay` 方法会发送Thea和二清消息
2. **路由策略**: 数据库路由优先使用交易ID（`majorOrderId`/`payTradeId`/`orderId`），其次使用业务ID（`productId`）
3. **锁策略**: 分布式锁和业务锁通常使用交易ID，确保同一交易的串行处理
4. **业务标识**: `productId` 主要用于业务层面的标识、日志追踪和状态同步
5. **数据一致性**: `majorProductId` 用于重复检查、双写检查和业务关联，确保数据一致性
6. **业务流程**: 在退款、转账、没收等业务流程中，`productId` 和 `majorProductId` 起到连接不同交易环节的作用
7. **灰度发布**: 在部分业务中，`majorProductId` 被用于灰度发布控制

### 新发现的使用场景

1. **账单查询**: 在没收业务中，使用 `productId` 查询对应的账单信息
2. **双写检查中的复杂逻辑**: 在退款业务中，`majorProductId` 被用于设置订单Cid、业务订单ID和路由ID
3. **重复调用检查**: 在合拼退款中，使用 `productId` 进行重复调用检查
4. **业务对象转换**: 在多个业务中，`productId` 被转换为各种业务对象的 `majorProductId` 属性

这些字段在交易系统中起到了连接业务系统与交易系统的关键作用，确保了交易的正确性、一致性和可追溯性。通过详细分析，我们发现这些字段的使用远比最初想象的更加广泛和复杂。
